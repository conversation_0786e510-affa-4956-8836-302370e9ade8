# دليل إنشاء API REST للوحة تحكم Firebase

## نظرة عامة

هذا الدليل يوضح كيفية إنشاء API REST باستخدام Firebase Cloud Functions لإدارة البيانات من خلال واجهات برمجة التطبيقات.

---

## 1. إعداد Firebase Cloud Functions

### 1.1 تثبيت Firebase CLI

```bash
# تثبيت Firebase CLI عالمياً
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# تهيئة المشروع
firebase init functions
```

### 1.2 هيكل المشروع

```
functions/
├── src/
│   ├── controllers/
│   │   ├── subscriptionController.js
│   │   ├── packageController.js
│   │   ├── updateController.js
│   │   └── whatsappController.js
│   ├── middleware/
│   │   ├── auth.js
│   │   └── validation.js
│   ├── models/
│   │   ├── subscription.js
│   │   ├── package.js
│   │   ├── update.js
│   │   └── whatsapp.js
│   ├── routes/
│   │   ├── subscriptions.js
│   │   ├── packages.js
│   │   ├── updates.js
│   │   └── whatsapp.js
│   └── index.js
├── package.json
└── .eslintrc.js
```

---

## 2. إعداد Express.js مع Firebase Functions

### 2.1 تثبيت التبعيات

```bash
cd functions
npm install express cors helmet morgan joi
npm install firebase-admin
```

### 2.2 إعداد الخادم الرئيسي

```javascript
// functions/src/index.js
const functions = require('firebase-functions');
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

// استيراد المسارات
const subscriptionRoutes = require('./routes/subscriptions');
const packageRoutes = require('./routes/packages');
const updateRoutes = require('./routes/updates');
const whatsappRoutes = require('./routes/whatsapp');

const app = express();

// Middleware
app.use(helmet());
app.use(cors({ origin: true }));
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// المسارات
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/packages', packageRoutes);
app.use('/api/updates', updateRoutes);
app.use('/api/whatsapp', whatsappRoutes);

// مسار الصحة
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'ISP Manager API'
  });
});

// معالج الأخطاء العام
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: 'خطأ داخلي في الخادم',
    message: err.message 
  });
});

// تصدير كـ Firebase Function
exports.api = functions.https.onRequest(app);
```

---

## 3. إعداد Firebase Admin

```javascript
// functions/src/config/firebase.js
const admin = require('firebase-admin');

// تهيئة Firebase Admin
admin.initializeApp();

const db = admin.firestore();
const auth = admin.auth();

module.exports = { admin, db, auth };
```

---

## 4. Middleware للمصادقة

```javascript
// functions/src/middleware/auth.js
const { auth } = require('../config/firebase');

const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ 
        error: 'رمز الوصول مطلوب' 
      });
    }

    const decodedToken = await auth.verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    console.error('خطأ في المصادقة:', error);
    res.status(403).json({ 
      error: 'رمز الوصول غير صالح' 
    });
  }
};

const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user || !req.user.admin) {
      return res.status(403).json({ 
        error: 'صلاحيات المدير مطلوبة' 
      });
    }
    next();
  } catch (error) {
    res.status(403).json({ 
      error: 'صلاحيات غير كافية' 
    });
  }
};

module.exports = { authenticateToken, requireAdmin };
```

---

## 5. Middleware للتحقق من البيانات

```javascript
// functions/src/middleware/validation.js
const Joi = require('joi');

const validateSubscription = (req, res, next) => {
  const schema = Joi.object({
    adminId: Joi.string().required(),
    deviceId: Joi.string().required(),
    packageId: Joi.string().required(),
    packageName: Joi.string().required(),
    price: Joi.number().positive().required(),
    currency: Joi.string().required(),
    startDate: Joi.date().required(),
    endDate: Joi.date().greater(Joi.ref('startDate')).required(),
    status: Joi.string().valid('active', 'expired', 'pending').required(),
    autoRenew: Joi.boolean().default(true)
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({ 
      error: 'بيانات غير صحيحة',
      details: error.details 
    });
  }
  next();
};

const validatePackage = (req, res, next) => {
  const schema = Joi.object({
    adminId: Joi.string().required(),
    name: Joi.string().required(),
    description: Joi.string().optional(),
    price: Joi.number().positive().required(),
    currency: Joi.string().required(),
    duration: Joi.number().positive().required(),
    durationUnit: Joi.string().valid('days', 'months', 'years').required(),
    speed: Joi.string().required(),
    isActive: Joi.boolean().default(true)
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({ 
      error: 'بيانات غير صحيحة',
      details: error.details 
    });
  }
  next();
};

const validateUpdate = (req, res, next) => {
  const schema = Joi.object({
    version: Joi.string().required(),
    buildNumber: Joi.number().positive().required(),
    title: Joi.string().required(),
    description: Joi.string().required(),
    downloadUrl: Joi.string().uri().required(),
    isForceUpdate: Joi.boolean().default(false),
    isActive: Joi.boolean().default(true)
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({ 
      error: 'بيانات غير صحيحة',
      details: error.details 
    });
  }
  next();
};

module.exports = { 
  validateSubscription, 
  validatePackage, 
  validateUpdate 
};
```

---

## 6. Controllers

### 6.1 Subscription Controller

```javascript
// functions/src/controllers/subscriptionController.js
const { db } = require('../config/firebase');

class SubscriptionController {
  // إنشاء اشتراك جديد
  async create(req, res) {
    try {
      const subscriptionData = {
        ...req.body,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      };

      const docRef = await db.collection('device_subscriptions').add(subscriptionData);
      
      res.status(201).json({
        id: docRef.id,
        message: 'تم إنشاء الاشتراك بنجاح',
        data: subscriptionData
      });
    } catch (error) {
      console.error('خطأ في إنشاء الاشتراك:', error);
      res.status(500).json({ 
        error: 'خطأ في إنشاء الاشتراك' 
      });
    }
  }

  // جلب جميع اشتراكات المدير
  async getByAdmin(req, res) {
    try {
      const { adminId } = req.params;
      const { status, limit = 50, offset = 0 } = req.query;

      let query = db.collection('device_subscriptions')
        .where('adminId', '==', adminId)
        .orderBy('createdAt', 'desc')
        .limit(parseInt(limit))
        .offset(parseInt(offset));

      if (status) {
        query = query.where('status', '==', status);
      }

      const snapshot = await query.get();
      const subscriptions = [];

      snapshot.forEach(doc => {
        subscriptions.push({
          id: doc.id,
          ...doc.data()
        });
      });

      res.json({
        data: subscriptions,
        total: subscriptions.length,
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
    } catch (error) {
      console.error('خطأ في جلب الاشتراكات:', error);
      res.status(500).json({ 
        error: 'خطأ في جلب الاشتراكات' 
      });
    }
  }

  // جلب اشتراك محدد
  async getById(req, res) {
    try {
      const { id } = req.params;
      const doc = await db.collection('device_subscriptions').doc(id).get();

      if (!doc.exists) {
        return res.status(404).json({ 
          error: 'الاشتراك غير موجود' 
        });
      }

      res.json({
        id: doc.id,
        ...doc.data()
      });
    } catch (error) {
      console.error('خطأ في جلب الاشتراك:', error);
      res.status(500).json({ 
        error: 'خطأ في جلب الاشتراك' 
      });
    }
  }

  // تحديث اشتراك
  async update(req, res) {
    try {
      const { id } = req.params;
      const updateData = {
        ...req.body,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      };

      await db.collection('device_subscriptions').doc(id).update(updateData);

      res.json({
        message: 'تم تحديث الاشتراك بنجاح',
        id: id
      });
    } catch (error) {
      console.error('خطأ في تحديث الاشتراك:', error);
      res.status(500).json({ 
        error: 'خطأ في تحديث الاشتراك' 
      });
    }
  }

  // حذف اشتراك
  async delete(req, res) {
    try {
      const { id } = req.params;
      await db.collection('device_subscriptions').doc(id).delete();

      res.json({
        message: 'تم حذف الاشتراك بنجاح',
        id: id
      });
    } catch (error) {
      console.error('خطأ في حذف الاشتراك:', error);
      res.status(500).json({ 
        error: 'خطأ في حذف الاشتراك' 
      });
    }
  }

  // تجديد اشتراك
  async renew(req, res) {
    try {
      const { id } = req.params;
      const { durationDays } = req.body;

      const doc = await db.collection('device_subscriptions').doc(id).get();
      
      if (!doc.exists) {
        return res.status(404).json({ 
          error: 'الاشتراك غير موجود' 
        });
      }

      const subscription = doc.data();
      const newEndDate = new Date(subscription.endDate.toDate());
      newEndDate.setDate(newEndDate.getDate() + parseInt(durationDays));

      await db.collection('device_subscriptions').doc(id).update({
        endDate: newEndDate,
        status: 'active',
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      res.json({
        message: 'تم تجديد الاشتراك بنجاح',
        newEndDate: newEndDate,
        id: id
      });
    } catch (error) {
      console.error('خطأ في تجديد الاشتراك:', error);
      res.status(500).json({ 
        error: 'خطأ في تجديد الاشتراك' 
      });
    }
  }

  // إحصائيات الاشتراكات
  async getStats(req, res) {
    try {
      const { adminId } = req.params;
      
      const activeQuery = db.collection('device_subscriptions')
        .where('adminId', '==', adminId)
        .where('status', '==', 'active')
        .where('endDate', '>', new Date());
      
      const expiredQuery = db.collection('device_subscriptions')
        .where('adminId', '==', adminId)
        .where('endDate', '<', new Date());
      
      const pendingQuery = db.collection('device_subscriptions')
        .where('adminId', '==', adminId)
        .where('status', '==', 'pending');

      const [activeSnapshot, expiredSnapshot, pendingSnapshot] = await Promise.all([
        activeQuery.get(),
        expiredQuery.get(),
        pendingQuery.get()
      ]);

      const stats = {
        active: activeSnapshot.size,
        expired: expiredSnapshot.size,
        pending: pendingSnapshot.size,
        total: activeSnapshot.size + expiredSnapshot.size + pendingSnapshot.size
      };

      res.json(stats);
    } catch (error) {
      console.error('خطأ في جلب الإحصائيات:', error);
      res.status(500).json({ 
        error: 'خطأ في جلب الإحصائيات' 
      });
    }
  }
}

module.exports = new SubscriptionController();
```

### 6.2 Package Controller

```javascript
// functions/src/controllers/packageController.js
const { db } = require('../config/firebase');

class PackageController {
  // إنشاء باقة جديدة
  async create(req, res) {
    try {
      const packageData = {
        ...req.body,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      };

      const docRef = await db.collection('app_subscription_packages').add(packageData);
      
      res.status(201).json({
        id: docRef.id,
        message: 'تم إنشاء الباقة بنجاح',
        data: packageData
      });
    } catch (error) {
      console.error('خطأ في إنشاء الباقة:', error);
      res.status(500).json({ 
        error: 'خطأ في إنشاء الباقة' 
      });
    }
  }

  // جلب جميع باقات المدير
  async getByAdmin(req, res) {
    try {
      const { adminId } = req.params;
      const { isActive } = req.query;

      let query = db.collection('app_subscription_packages')
        .where('adminId', '==', adminId)
        .orderBy('createdAt', 'desc');

      if (isActive !== undefined) {
        query = query.where('isActive', '==', isActive === 'true');
      }

      const snapshot = await query.get();
      const packages = [];

      snapshot.forEach(doc => {
        packages.push({
          id: doc.id,
          ...doc.data()
        });
      });

      res.json({
        data: packages,
        total: packages.length
      });
    } catch (error) {
      console.error('خطأ في جلب الباقات:', error);
      res.status(500).json({ 
        error: 'خطأ في جلب الباقات' 
      });
    }
  }

  // جلب باقة محددة
  async getById(req, res) {
    try {
      const { id } = req.params;
      const doc = await db.collection('app_subscription_packages').doc(id).get();

      if (!doc.exists) {
        return res.status(404).json({ 
          error: 'الباقة غير موجودة' 
        });
      }

      res.json({
        id: doc.id,
        ...doc.data()
      });
    } catch (error) {
      console.error('خطأ في جلب الباقة:', error);
      res.status(500).json({ 
        error: 'خطأ في جلب الباقة' 
      });
    }
  }

  // تحديث باقة
  async update(req, res) {
    try {
      const { id } = req.params;
      const updateData = {
        ...req.body,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      };

      await db.collection('app_subscription_packages').doc(id).update(updateData);

      res.json({
        message: 'تم تحديث الباقة بنجاح',
        id: id
      });
    } catch (error) {
      console.error('خطأ في تحديث الباقة:', error);
      res.status(500).json({ 
        error: 'خطأ في تحديث الباقة' 
      });
    }
  }

  // حذف باقة
  async delete(req, res) {
    try {
      const { id } = req.params;
      await db.collection('app_subscription_packages').doc(id).delete();

      res.json({
        message: 'تم حذف الباقة بنجاح',
        id: id
      });
    } catch (error) {
      console.error('خطأ في حذف الباقة:', error);
      res.status(500).json({ 
        error: 'خطأ في حذف الباقة' 
      });
    }
  }

  // تفعيل/إلغاء تفعيل باقة
  async toggleStatus(req, res) {
    try {
      const { id } = req.params;
      const { isActive } = req.body;

      await db.collection('app_subscription_packages').doc(id).update({
        isActive: isActive,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      res.json({
        message: `تم ${isActive ? 'تفعيل' : 'إلغاء تفعيل'} الباقة بنجاح`,
        id: id,
        isActive: isActive
      });
    } catch (error) {
      console.error('خطأ في تغيير حالة الباقة:', error);
      res.status(500).json({ 
        error: 'خطأ في تغيير حالة الباقة' 
      });
    }
  }
}

module.exports = new PackageController();
```

---

## 🔄 تحديث منطق باقات الاشتراك الافتراضية

- **أسماء الباقات الافتراضية:**
  - الباقة الشهرية
  - الباقة نصف السنوية
  - الباقة السنوية

- **كل باقة افتراضية تحتوي على:**
  - `details`: وصف مختصر
  - `features`: قائمة مميزات

- **منطق الإنشاء:**
  - يتم التحقق من وجود الباقات الافتراضية الثلاثة عند الحاجة.
  - إذا كانت أي باقة ناقصة، يتم إنشاؤها تلقائيًا.
  - لا يتم حذف أي باقة موجودة (مخصصة أو قديمة)، ولا تتكرر الباقات الافتراضية.

## مثال استجابة API لباقة افتراضية:

```json
{
  "id": "auto-generated",
  "name": "الباقة الشهرية",
  "duration_days": 30,
  "price": 5000,
  "is_active": true,
  "is_recommended": true,
  "details": "أفضل خيار للتجربة أو الاستخدام القصير.",
  "features": [
    "دعم فني سريع",
    "تحديثات مجانية",
    "إمكانية الترقية في أي وقت"
  ]
}
```

---

## 7. Routes

### 7.1 Subscription Routes

```javascript
// functions/src/routes/subscriptions.js
const express = require('express');
const router = express.Router();
const subscriptionController = require('../controllers/subscriptionController');
const { authenticateToken } = require('../middleware/auth');
const { validateSubscription } = require('../middleware/validation');

// جميع المسارات تتطلب مصادقة
router.use(authenticateToken);

// إنشاء اشتراك جديد
router.post('/', validateSubscription, subscriptionController.create);

// جلب جميع اشتراكات المدير
router.get('/admin/:adminId', subscriptionController.getByAdmin);

// جلب اشتراك محدد
router.get('/:id', subscriptionController.getById);

// تحديث اشتراك
router.put('/:id', validateSubscription, subscriptionController.update);

// حذف اشتراك
router.delete('/:id', subscriptionController.delete);

// تجديد اشتراك
router.post('/:id/renew', subscriptionController.renew);

// إحصائيات الاشتراكات
router.get('/admin/:adminId/stats', subscriptionController.getStats);

module.exports = router;
```

### 7.2 Package Routes

```javascript
// functions/src/routes/packages.js
const express = require('express');
const router = express.Router();
const packageController = require('../controllers/packageController');
const { authenticateToken } = require('../middleware/auth');
const { validatePackage } = require('../middleware/validation');

// جميع المسارات تتطلب مصادقة
router.use(authenticateToken);

// إنشاء باقة جديدة
router.post('/', validatePackage, packageController.create);

// جلب جميع باقات المدير
router.get('/admin/:adminId', packageController.getByAdmin);

// جلب باقة محددة
router.get('/:id', packageController.getById);

// تحديث باقة
router.put('/:id', validatePackage, packageController.update);

// حذف باقة
router.delete('/:id', packageController.delete);

// تفعيل/إلغاء تفعيل باقة
router.patch('/:id/toggle', packageController.toggleStatus);

module.exports = router;
```

---

## 8. اختبار API

### 8.1 اختبار محلي

```bash
# تشغيل Firebase Functions محلياً
firebase emulators:start --only functions

# اختبار نقطة النهاية
curl -X GET http://localhost:5001/your-project/us-central1/api/health
```

### 8.2 أمثلة على الاستخدام

#### إنشاء اشتراك جديد
```bash
curl -X POST https://your-project.cloudfunctions.net/api/subscriptions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ID_TOKEN" \
  -d '{
    "adminId": "admin123",
    "deviceId": "device456",
    "packageId": "package789",
    "packageName": "باقة الإنترنت الأساسية",
    "price": 50000,
    "currency": "د.ع",
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-12-31T23:59:59Z",
    "status": "active",
    "autoRenew": true
  }'
```

#### جلب اشتراكات المدير
```bash
curl -X GET "https://your-project.cloudfunctions.net/api/subscriptions/admin/admin123?status=active&limit=10" \
  -H "Authorization: Bearer YOUR_ID_TOKEN"
```

#### تجديد اشتراك
```bash
curl -X POST https://your-project.cloudfunctions.net/api/subscriptions/subscription123/renew \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ID_TOKEN" \
  -d '{
    "durationDays": 30
  }'
```

---

## 9. النشر

### 9.1 نشر Cloud Functions

```bash
# نشر جميع الدوال
firebase deploy --only functions

# نشر دالة محددة
firebase deploy --only functions:api
```

### 9.2 إعداد المتغيرات البيئية

```bash
# تعيين متغيرات بيئية
firebase functions:config:set app.environment="production"
firebase functions:config:set app.version="1.0.0"

# عرض المتغيرات
firebase functions:config:get
```

---

## 10. المراقبة والسجلات

### 10.1 عرض السجلات

```bash
# عرض سجلات الدوال
firebase functions:log

# مراقبة السجلات في الوقت الفعلي
firebase functions:log --follow
```

### 10.2 مراقبة الأداء

```bash
# عرض إحصائيات الاستخدام
firebase functions:list
```

---

## 11. الأمان

### 11.1 قواعد الأمان للـ API

```javascript
// functions/src/middleware/rateLimit.js
const rateLimit = require('express-rate-limit');

const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 100, // حد أقصى 100 طلب لكل IP
  message: {
    error: 'تم تجاوز الحد الأقصى للطلبات، يرجى المحاولة لاحقاً'
  }
});

module.exports = apiLimiter;
```

### 11.2 تطبيق Rate Limiting

```javascript
// functions/src/index.js
const apiLimiter = require('./middleware/rateLimit');

// تطبيق Rate Limiting على جميع المسارات
app.use('/api/', apiLimiter);
```

---

## خاتمة

هذا الدليل يوفر أساساً قوياً لإنشاء API REST آمن وقابل للتطوير باستخدام Firebase Cloud Functions. يمكنك تخصيص الوظائف والمسارات حسب احتياجاتك الخاصة.

للمزيد من المعلومات:
- [وثائق Firebase Functions](https://firebase.google.com/docs/functions)
- [وثائق Express.js](https://expressjs.com/)
- [وثائق Joi Validation](https://joi.dev/) 