import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:android_intent_plus/android_intent.dart';
import 'package:android_intent_plus/flag.dart';

import '../models/device_subscription_model.dart';
import '../services/firebase_app_subscription_packages_service.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/firebase_app_settings_service.dart';
import '../services/zaincash_service.dart';
import '../config/zaincash_config.dart';
import 'payment_history_page.dart';
import 'zaincash_otp_payment_page.dart';

class RenewSubscriptionPage extends StatefulWidget {
  final DeviceSubscription deviceSubscription;
  const RenewSubscriptionPage({super.key, required this.deviceSubscription});

  @override
  State<RenewSubscriptionPage> createState() => _RenewSubscriptionPageState();
}

class _RenewSubscriptionPageState extends State<RenewSubscriptionPage> {
  List<Map<String, dynamic>> _packages = [];
  String? _whatsappNumber;
  bool _isLoading = true;
  String? _error;
  String? _selectedPackageId;
  final FirebaseAppSubscriptionPackagesService _packagesService = FirebaseAppSubscriptionPackagesService();
  final ZainCashService _zainCashService = ZainCashService();


  @override
  void initState() {
    super.initState();
    _fetchData();
  }





  Future<void> _fetchData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });
    try {
      // تنظيف وإنشاء الباقات الافتراضية إذا لم توجد
      await _packagesService.ensureDefaultAppSubscriptionPackagesExist();
      final packagesRes = await _packagesService.getActiveAppSubscriptionPackages();
      final whatsappNumber = await _packagesService.getFirstActiveWhatsAppNumber();

      if (mounted) {
        setState(() {
          _packages = packagesRes;
          _whatsappNumber = whatsappNumber;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'خطأ في تحميل الباقات: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _openWhatsapp(Map<String, dynamic> package) async {
    if (_whatsappNumber == null) {
      // إنشاء بيانات افتراضية إذا لم تكن موجودة
      await FirebaseAppSettingsService.ensureDefaultWhatsAppNumber();
      // رابط إنشاء فهرس whatsapp_numbers في Firebase Console
      const indexUrl = 'https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/indexes?create_composite=CUSTOM_INDEX_ID';
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('رقم الواتساب غير متوفر'),
            content: Text('رقم الواتساب غير متوفر في قاعدة البيانات. يمكنك إنشاء فهرس لجدول أرقام الواتساب من هنا:'),
            actions: [
              TextButton(
                onPressed: () async {
                  if (await canLaunchUrl(Uri.parse(indexUrl))) {
                    await launchUrl(Uri.parse(indexUrl));
                  }
                },
                child: Text('فتح صفحة الفهارس'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق'),
              ),
            ],
          ),
        );
      }
      return;
    }

    final cleanWhatsAppNumber = _whatsappNumber!.replaceAll(RegExp(r'[^ -9+]'), '');
    final msg =
        'مرحباً، أود شراء باقة اشتراك تطبيق\n\n'
        '**رقم الحساب:** ${widget.deviceSubscription.accountNumber}\n'
        '**الباقة المختارة:** ${package['name']}\n'
        '**السعر:** ${package['price']} د.ع\n'
        '**المدة:** ${package['duration_days']} يوم\n\n'
        'الرجاء تزويدي بتعليمات الدفع.';

    final uri = Uri.parse('https://wa.me/$cleanWhatsAppNumber?text=${Uri.encodeComponent(msg)}');

    try {
      final intent = AndroidIntent(
        action: 'action_view',
        data: uri.toString(),
        package: 'com.whatsapp',
        flags: <int>[Flag.FLAG_ACTIVITY_NEW_TASK],
      );
      await intent.launch();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم فتح واتساب بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _showErrorSnackBar('تعذر فتح واتساب عبر android_intent_plus: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return WillPopScope(
      onWillPop: () async {
        // السماح بالرجوع إذا كان الحساب نشطاً فقط
        return widget.deviceSubscription.isActive && 
               widget.deviceSubscription.subscriptionEndDate.isAfter(DateTime.now());
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('باقات اشتراك التطبيق', style: TextStyle(fontWeight: FontWeight.bold)),
          backgroundColor: theme.colorScheme.surface,
          elevation: 1,
          actions: [
            IconButton(
              icon: const Icon(Icons.history),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PaymentHistoryPage(),
                  ),
                );
              },
              tooltip: 'تاريخ المدفوعات',
            ),
          ],
        ),
        body: _buildBody(theme),
      ),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return _buildLoadingShimmer();
    }
    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, color: theme.colorScheme.error, size: 50),
              const SizedBox(height: 16),
              Text(_error!, style: theme.textTheme.titleMedium, textAlign: TextAlign.center),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _fetchData,
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }
    return RefreshIndicator(
      onRefresh: _fetchData,
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildAccountHeader(theme),
          const SizedBox(height: 16),
          _buildHeader(theme),
          const SizedBox(height: 24),
          if (_packages.isEmpty)
            _buildEmptyPackages(theme)
          else ..._packages.map((pkg) => _buildPackageCard(pkg, theme)),
        ],
      ),
    );
  }

  Widget _buildAccountHeader(ThemeData theme) {
    final sub = widget.deviceSubscription;
    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.account_circle, color: theme.colorScheme.primary, size: 32),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('رقم الحساب: ${sub.accountNumber}', style: theme.textTheme.titleMedium),
                  Row(
                    children: [
                      Icon(sub.isActive ? Icons.check_circle : Icons.cancel, color: sub.isActive ? Colors.green : Colors.red, size: 18),
                      const SizedBox(width: 4),
                      Text('الحالة: ', style: theme.textTheme.bodyMedium),
                      Text(sub.isActive ? 'نشط' : 'منتهي', style: TextStyle(color: sub.isActive ? Colors.green : Colors.red, fontWeight: FontWeight.bold)),
                    ],
                  ),
                  Text('ينتهي في: ${sub.formattedEndDate}', style: theme.textTheme.bodySmall),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'باقات اشتراك التطبيق',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اختر باقة اشتراك التطبيق التي تناسبك وقم بتفعيلها مباشرة عبر واتساب.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyPackages(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.subscriptions, color: theme.colorScheme.primary.withOpacity(0.7), size: 64),
            const SizedBox(height: 16),
            Text(
              'لا توجد باقات اشتراك تطبيق متاحة',
              style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم إنشاء الباقات الافتراضية تلقائياً عند الحاجة',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _fetchData,
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPackageCard(Map<String, dynamic> package, ThemeData theme) {
    final isSelected = _selectedPackageId == package['id'];
    final isRecommended = package['is_recommended'] == true;

    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        gradient: isSelected
            ? LinearGradient(
                colors: [
                  theme.colorScheme.primary.withOpacity(0.08),
                  theme.colorScheme.primary.withOpacity(0.12),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: isSelected ? null : theme.cardColor,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isSelected
              ? theme.colorScheme.primary
              : (isRecommended
                  ? theme.colorScheme.secondary
                  : theme.colorScheme.outline.withOpacity(0.2)),
          width: isSelected ? 2.5 : (isRecommended ? 2 : 1),
        ),
        boxShadow: [
          BoxShadow(
            color: isSelected
                ? theme.colorScheme.primary.withOpacity(0.25)
                : (isRecommended
                    ? theme.colorScheme.secondary.withOpacity(0.15)
                    : Colors.black.withOpacity(0.08)),
            blurRadius: isSelected ? 20 : 12,
            offset: Offset(0, isSelected ? 6 : 4),
            spreadRadius: isSelected ? 2 : 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            setState(() => _selectedPackageId = package['id']);
          },
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with title and recommended badge
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            package['name'],
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              fontSize: 20,
                              color: isSelected
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.onSurface,
                            ),
                          ),
                          if (package['details'] != null && package['details'].toString().isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(top: 6),
                              child: Text(
                                package['details'],
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                                  height: 1.4,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    if (isRecommended)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [theme.colorScheme.secondary, theme.colorScheme.secondary.withOpacity(0.8)],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: theme.colorScheme.secondary.withOpacity(0.3),
                              blurRadius: 8,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          'الأكثر شعبية',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 11,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Price and duration info
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? theme.colorScheme.primary.withOpacity(0.1)
                        : theme.colorScheme.surfaceVariant.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? theme.colorScheme.primary.withOpacity(0.3)
                          : theme.colorScheme.outline.withOpacity(0.1),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.schedule,
                                  size: 18,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  'المدة',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${package['duration_days']} يوم',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        width: 1,
                        height: 40,
                        color: theme.colorScheme.outline.withOpacity(0.2),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.payments,
                                  size: 18,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  'السعر',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${package['price']} د.ع',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Features
                if (package['features'] != null && package['features'] is List)
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الميزات:',
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ...((package['features'] as List).map((feature) => Padding(
                          padding: const EdgeInsets.only(bottom: 6),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  color: Colors.green.withOpacity(0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.check,
                                  color: Colors.green,
                                  size: 14,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  feature.toString(),
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    height: 1.4,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ))),
                      ],
                    ),
                  ),
                
                const SizedBox(height: 20),
                
                // Action button
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: () => _onBuyPressed(package),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.primary.withOpacity(0.9),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(14),
                      ),
                      elevation: isSelected ? 6 : 3,
                      shadowColor: theme.colorScheme.primary.withOpacity(0.4),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.shopping_cart,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'شراء الباقة',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onBuyPressed(Map<String, dynamic> package) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.payment,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'اختر طريقة الدفع',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ],
        ),
        content: Container(
          width: double.maxFinite,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7, // Limit height to 70% of screen
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Package details card
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.inventory_2, size: 20, color: Theme.of(context).colorScheme.primary),
                          const SizedBox(width: 8),
                          Text(
                            'تفاصيل الباقة',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      const Divider(height: 16),
                      _buildPackageDetailRow('الباقة', package['name'], Icons.card_giftcard),
                      const SizedBox(height: 6),
                      _buildPackageDetailRow('السعر', '${package['price']} د.ع', Icons.attach_money),
                      const SizedBox(height: 6),
                      _buildPackageDetailRow('المدة', '${package['duration_days']} يوم', Icons.schedule),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'اختر طريقة الدفع المناسبة:',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 12),
                // Payment methods
                Row(
                  children: [
                    // ZainCash with logo
                    Expanded(
                      child: Container(
                        height: 76,
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () {
                              Navigator.pop(context);
                              _payWithZainCash(package);
                            },
                            borderRadius: BorderRadius.circular(12),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Color(0xFFFF6B35).withOpacity(0.3),
                                  width: 2,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Color(0xFFFF6B35).withOpacity(0.2),
                                    blurRadius: 8,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Image.asset(
                                  'assets/zain-cash-seeklogo.png',
                                  width: 56,
                                  height: 56,
                                  fit: BoxFit.contain,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      width: 56,
                                      height: 56,
                                      decoration: BoxDecoration(
                                        color: Color(0xFFFF6B35),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Icon(
                                        Icons.payment,
                                        color: Colors.white,
                                        size: 32,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // WhatsApp
                    Expanded(
                      child: Container(
                        height: 76,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            _openWhatsapp(package);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xFF25D366), // WhatsApp green
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 3,
                            shadowColor: Color(0xFF25D366).withOpacity(0.4),
                            padding: const EdgeInsets.all(14),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.chat, color: Colors.white, size: 28),
                              const SizedBox(height: 3),
                              Text(
                                'واتساب',
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.outline,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageDetailRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ),
      ],
    );
  }

  /// الدفع عبر ZainCash - محدث لاستخدام OTP داخل التطبيق
  Future<void> _payWithZainCash(Map<String, dynamic> package) async {
    try {
      // التحقق من صحة البيانات
      final amount = (package['price'] as num).toDouble();
      final accountNumber = widget.deviceSubscription.accountNumber;

      // التحقق من الحد الأدنى للمبلغ
      if (amount < ZainCashConfig.minAmount) {
        _showErrorSnackBar(ZainCashConfig.errorMessages['min_amount']!);
        return;
      }

      if (!_zainCashService.validatePaymentData(
        amount: amount,
        accountNumber: accountNumber,
      )) {
        _showErrorSnackBar('بيانات الدفع غير صحيحة');
        return;
      }

      // فتح صفحة الدفع مع OTP داخل التطبيق
      final result = await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) => ZainCashOTPPaymentPage(
            packageId: package['id'],
            accountNumber: accountNumber,
            amount: amount,
            packageName: package['name'],
            durationDays: package['duration_days'],
          ),
        ),
      );

      if (result == true) {
        // تم الدفع بنجاح - إعادة تحميل البيانات
        _showSuccessSnackBar('تم الدفع وتفعيل الاشتراك بنجاح! 🎉');
        await _fetchData(); // إعادة تحميل بيانات الاشتراك
      } else if (result == false) {
        // تم إلغاء الدفع
        _showErrorSnackBar('تم إلغاء عملية الدفع');
      }
      // إذا كان result == null، فهذا يعني أن المستخدم ضغط زر الرجوع

    } catch (e) {
      _showErrorSnackBar('خطأ في عملية الدفع: $e');
    }
  }













  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 3),
      ),
    );
  }

  Widget _buildLoadingShimmer() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
          ),
        ),
        const SizedBox(height: 24),
        ...List.generate(3, (index) => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              height: 150,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
        )),
      ],
    );
  }
}