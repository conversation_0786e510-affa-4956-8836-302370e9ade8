import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/subscriber_model.dart';
import '../models/package_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'app_settings_service.dart';

class AIService {
  static const String _apiKey = 'OPENAI-API-KEY';
  static const String _baseUrl = 'https://api.openai.com/v1/chat/completions';  // Keys for storing AI data locally
  static const String _localInsightsKey = 'local_ai_insights';
  static const String _subscriberAnalyticsKey = 'subscriber_analytics';
  static const String _lastLocalAnalysisTimeKey = 'last_local_analysis_time';

  // Flag to determine whether to use online or offline AI
  bool _useOfflineAI = true;
  
  // Cached analytics data
  Map<String, dynamic> _cachedAnalytics = {};

  static final AIService _instance = AIService._internal();
  factory AIService() => _instance;

  AIService._internal() {
    // Initialize offline AI data if needed
    _initializeOfflineAI();
  }  // Initialize offline AI data structures
  Future<void> _initializeOfflineAI() async {
    final prefs = await SharedPreferences.getInstance();
    _useOfflineAI = prefs.getBool('use_offline_ai') ?? true;
    
    // Load cached analytics if available
    final analyticsJson = prefs.getString(_subscriberAnalyticsKey);
    if (analyticsJson != null) {
      try {
        _cachedAnalytics = jsonDecode(analyticsJson);
      } catch (e) {
        _cachedAnalytics = {};
      }
    }
    
    // Initialize local insights cache if needed
    if (!prefs.containsKey(_localInsightsKey)) {
      await prefs.setString(_localInsightsKey, jsonEncode({
        'lastUpdate': DateTime.now().toIso8601String(),
        'insights': [],
      }));
    }
    
    // Check if we need to update local analysis
    final lastAnalysisTimeStr = prefs.getString(_lastLocalAnalysisTimeKey);
    if (lastAnalysisTimeStr == null) {
      await prefs.setString(_lastLocalAnalysisTimeKey, DateTime.now().toIso8601String());
    }
  }
  // Toggle between online and offline AI
  Future<void> toggleOfflineAI(bool useOffline) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('use_offline_ai', useOffline);
    _useOfflineAI = useOffline;
    
    // If switching to offline mode, ensure we have recent cache data
    if (useOffline) {
      await _refreshLocalCache();
    }
  }
  // Refresh local cache data
  Future<void> _refreshLocalCache() async {
    final prefs = await SharedPreferences.getInstance();
    final lastAnalysisTimeStr = prefs.getString(_lastLocalAnalysisTimeKey);
    final DateTime lastAnalysisTime = lastAnalysisTimeStr != null 
        ? DateTime.parse(lastAnalysisTimeStr)
        : DateTime.now().subtract(const Duration(days: 30));
        
    // Only refresh if last analysis was more than a day ago
    if (DateTime.now().difference(lastAnalysisTime).inDays > 0) {
      // Update cached analytics with current timestamp
      _cachedAnalytics['lastUpdate'] = DateTime.now().toIso8601String();
      await prefs.setString(_subscriberAnalyticsKey, jsonEncode(_cachedAnalytics));
      await prefs.setString(_lastLocalAnalysisTimeKey, DateTime.now().toIso8601String());
    }
  }

  // Check if we're using offline AI
  bool get isUsingOfflineAI => _useOfflineAI;
  
  // Get the last time local analysis was performed
  Future<DateTime> getLastAnalysisTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastAnalysisTimeStr = prefs.getString(_lastLocalAnalysisTimeKey);
    if (lastAnalysisTimeStr != null) {
      return DateTime.parse(lastAnalysisTimeStr);
    }
    return DateTime.now().subtract(const Duration(days: 30));
  }
  Future<String> generateSubscriberInsights(List<SubscriberModel> subscribers, List<PackageModel> packages) async {    // Use offline AI if enabled
    if (_useOfflineAI) {
      return await _generateLocalSubscriberInsights(subscribers, packages);
    }
    
    try {
      final prompt = '''
تحليل بيانات مشتركي الإنترنت:

إجمالي المشتركين: ${subscribers.length}
المشتركين النشطين: ${subscribers.where((s) => !s.isExpired).length}
المشتركين المنتهيين: ${subscribers.where((s) => s.isExpired).length}
إجمالي الديون: ${subscribers.fold(0.0, (sum, s) => sum + s.debtAmount)}

أرجو تقديم تحليل باللغة العربية يتضمن:
1. نظرة عامة على حالة المشتركين
2. توصيات لتحسين عمليات التحصيل
3. اقتراحات لحملات تسويقية مناسبة
4. تحليل أداء الباقات المختلفة

قدم الإجابة في فقرات واضحة ومفيدة للإدارة.
''';

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': 'gpt-4o-mini',
          'messages': [
            {
              'role': 'system',
              'content': 'أنت مساعد ذكي متخصص في تحليل بيانات مشتركي الإنترنت وتقديم رؤى مفيدة للإدارة باللغة العربية.',
            },
            {
              'role': 'user',
              'content': prompt,
            },
          ],
          'max_tokens': 1000,
          'temperature': 0.7,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        return data['choices'][0]['message']['content'].toString().trim();      } else {
        // Fallback to local analysis
        return await _generateLocalSubscriberInsights(subscribers, packages);
      }
    } catch (e) {
      // Fallback to local analysis
      return await _generateLocalSubscriberInsights(subscribers, packages);
    }
  }
  Future<List<String>> generatePaymentReminders(SubscriberModel subscriber, PackageModel package) async {    // Use offline AI if enabled
    if (_useOfflineAI) {
      return await _generateLocalPaymentReminders(subscriber, package);
    }
    
    try {      final currencyAmount = await AppSettingsService.formatCurrency(subscriber.debtAmount);
      final prompt = '''
مشترك الإنترنت "${subscriber.fullName}" لديه دين قدره $currencyAmount عن باقة "${package.name}".
تاريخ انتهاء الاشتراك: ${subscriber.subscriptionEnd != null ? '${subscriber.subscriptionEnd!.day}/${subscriber.subscriptionEnd!.month}/${subscriber.subscriptionEnd!.year}' : 'غير محدد'}

أرجو إنشاء 3 رسائل تذكير للدفع باللغة العربية:
1. رسالة مهذبة وودودة
2. رسالة تذكير قوية
3. رسالة تحذير نهائية

يجب أن تكون الرسائل قصيرة ومناسبة للإرسال عبر الهاتف أو الواتساب. قدم كل رسالة في سطر منفصل.
''';

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': 'gpt-4o-mini',
          'messages': [
            {
              'role': 'system',
              'content': 'أنت مساعد ذكي متخصص في كتابة رسائل تذكير مهنية ومهذبة للعملاء باللغة العربية.',
            },
            {
              'role': 'user',
              'content': prompt,
            },
          ],
          'max_tokens': 500,
          'temperature': 0.8,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        final content = data['choices'][0]['message']['content'].toString().trim();
        return content.split('\n').where((line) => line.trim().isNotEmpty).toList();
      } else {
        // Fallback to local reminders
        return _generateLocalPaymentReminders(subscriber, package);
      }
    } catch (e) {
      // Fallback to local reminders
      return _generateLocalPaymentReminders(subscriber, package);
    }
  }

  Future<String> suggestOptimalPackage(String customerProfile) async {
    try {      final basicPackagePrice = await AppSettingsService.formatCurrency(15);
      final advancedPackagePrice = await AppSettingsService.formatCurrency(25);
      final ultraPackagePrice = await AppSettingsService.formatCurrency(35);
      
      final prompt = '''
ملف العميل: $customerProfile

بناءً على المعلومات المقدمة، أرجو اقتراح الباقة الأنسب للعميل من بين الخيارات التالية:
- الباقة الأساسية: $basicPackagePrice - 10 ميجا - 3 أجهزة
- الباقة المتقدمة: $advancedPackagePrice - 50 ميجا - 5 أجهزة
- الباقة الألترا: $ultraPackagePrice - 100 ميجا - 10 أجهزة

قدم اقتراحك مع التبرير باللغة العربية في فقرة واحدة قصيرة.
''';

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': 'gpt-4o',
          'messages': [
            {
              'role': 'system',
              'content': 'أنت مستشار خبير في اقتراح باقات الإنترنت المناسبة للعملاء باللغة العربية.',
            },
            {
              'role': 'user',
              'content': prompt,
            },
          ],
          'max_tokens': 200,
          'temperature': 0.5,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        return data['choices'][0]['message']['content'].toString().trim();
      } else {
        return 'نوصي بالباقة الأساسية كنقطة انطلاق جيدة لمعظم العملاء.';
      }
    } catch (e) {
      return 'نوصي بالباقة الأساسية كنقطة انطلاق جيدة لمعظم العملاء.';
    }
  }

  Future<Map<String, dynamic>> analyzeSubscriberBehavior(List<SubscriberModel> subscribers) async {
    try {
      final activeCount = subscribers.where((s) => !s.isExpired).length;
      final expiredCount = subscribers.where((s) => s.isExpired).length;
      final pendingPayments = subscribers.where((s) => s.paymentStatus == PaymentStatus.pending).length;      final totalDebt = subscribers.fold(0.0, (sum, s) => sum + s.debtAmount);
      final formattedTotalDebt = await AppSettingsService.formatCurrency(totalDebt);

      final prompt = '''
تحليل سلوك المشتركين:
- المشتركين النشطين: $activeCount
- المشتركين المنتهيين: $expiredCount
- الدفعات المعلقة: $pendingPayments
- إجمالي الديون: $formattedTotalDebt

أرجو تقديم تحليل JSON يحتوي على:
- نسبة الاحتفاظ بالعملاء
- توصيات لتحسين التحصيل
- مؤشرات الأداء الرئيسية
- اقتراحات للحملات التسويقية

قدم الإجابة كـ JSON object صالح باللغة العربية.
''';

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': 'o3-mini',
          'messages': [
            {
              'role': 'system',
              'content': 'أنت محلل بيانات خبير. قدم إجابتك كـ JSON object صالح باللغة العربية.',
            },
            {
              'role': 'user',
              'content': prompt,
            },
          ],
          'response_format': {'type': 'json_object'},
          'max_tokens': 800,
          'temperature': 0.3,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        final content = data['choices'][0]['message']['content'].toString();
        return jsonDecode(content);
      } else {
        return _getDefaultAnalysis(activeCount, expiredCount, totalDebt);
      }
    } catch (e) {
      return _getDefaultAnalysis(
        subscribers.where((s) => !s.isExpired).length,
        subscribers.where((s) => s.isExpired).length,
        subscribers.fold(0.0, (sum, s) => sum + s.debtAmount),
      );
    }
  }

  Map<String, dynamic> _getDefaultAnalysis(int activeCount, int expiredCount, double totalDebt) {
    final total = activeCount + expiredCount;
    final retentionRate = total > 0 ? (activeCount / total * 100).toStringAsFixed(1) : '0';

    return {
      'نسبة_الاحتفاظ': '$retentionRate%',
      'حالة_التحصيل': totalDebt > 50000 ? 'تحتاج تحسين' : 'جيدة',
      'توصيات_التحصيل': [
        'متابعة الديون المستحقة بانتظام',
        'تطبيق نظام تذكير آلي',
        'تقديم خصومات للدفع المبكر'
      ],
      'مؤشرات_الأداء': {
        'المشتركين_النشطين': activeCount,
        'المشتركين_المنتهيين': expiredCount,
        'إجمالي_الديون': totalDebt
      },
      'اقتراحات_تسويقية': [
        'حملة استرداد العملاء المنتهيين',
        'برنامج إحالة صديق',
        'عروض ترقية للباقات الأعلى'
      ]
    };
  }
  // Generate local subscriber insights without internet connection
  Future<String> _generateLocalSubscriberInsights(List<SubscriberModel> subscribers, List<PackageModel> packages) async {
    final totalSubscribers = subscribers.length;
    final activeSubscribers = subscribers.where((s) => !s.isExpired).length;
    final expiredSubscribers = subscribers.where((s) => s.isExpired).length;
    final totalDebt = subscribers.fold(0.0, (sum, s) => sum + s.debtAmount);
    final averageDebt = totalSubscribers > 0 ? totalDebt / totalSubscribers : 0.0;
    
    // Calculate package distribution
    final packageDistribution = <String, int>{};    for (final subscriber in subscribers) {
      final packageName = packages.firstWhere(
        (p) => p.id == subscriber.packageId, 
        orElse: () => PackageModel(adminId: "",serverId: "",
          id: '', 
          name: 'غير محدد', 
          price: 0, 
          speed: '0', 
          deviceCount: 0,
          durationInDays: 30,
          createdAt: DateTime.now(),
        )
      ).name;
      packageDistribution[packageName] = (packageDistribution[packageName] ?? 0) + 1;
    }
    
    // Calculate payment status distribution
    final pendingPayments = subscribers.where((s) => s.paymentStatus == PaymentStatus.pending).length;
    final paidSubscribers = subscribers.where((s) => s.paymentStatus == PaymentStatus.paid).length;
    
    // Generate insights based on local calculations
    final retentionRate = totalSubscribers > 0 ? (activeSubscribers / totalSubscribers * 100) : 0.0;
      final formattedTotalDebt = await AppSettingsService.formatCurrency(totalDebt);
    final formattedAverageDebt = await AppSettingsService.formatCurrency(averageDebt);
    
    return '''
📊 تحليل شامل لبيانات مشتركي الإنترنت

🔍 نظرة عامة:
• إجمالي المشتركين: $totalSubscribers
• المشتركين النشطين: $activeSubscribers (${retentionRate.toStringAsFixed(1)}%)
• المشتركين المنتهيين: $expiredSubscribers
• إجمالي الديون: $formattedTotalDebt
• متوسط الدين للمشترك: $formattedAverageDebt

💰 تحليل المدفوعات:
• دفعات مكتملة: $paidSubscribers
• دفعات معلقة: $pendingPayments
• نسبة التحصيل: ${totalSubscribers > 0 ? ((paidSubscribers / totalSubscribers) * 100).toStringAsFixed(1) : 0}%

📦 توزيع الباقات:
${packageDistribution.entries.map((e) => '• ${e.key}: ${e.value} مشترك').join('\n')}

🎯 توصيات للتحسين:
${_generateLocalRecommendations(retentionRate, totalDebt, pendingPayments, totalSubscribers)}

📈 اقتراحات الحملات التسويقية:
${_generateMarketingRecommendations(activeSubscribers, expiredSubscribers, packageDistribution)}
''';
  }
  
  // Generate local recommendations based on data
  String _generateLocalRecommendations(double retentionRate, double totalDebt, int pendingPayments, int totalSubscribers) {
    final recommendations = <String>[];
    
    if (retentionRate < 70) {
      recommendations.add('• تحسين خدمة العملاء لزيادة معدل الاحتفاظ بالمشتركين');
      recommendations.add('• تطوير برنامج ولاء للعملاء طويلي المدى');
    }
    
    if (totalDebt > 10000) {
      recommendations.add('• تفعيل نظام تذكير آلي للمدفوعات المستحقة');
      recommendations.add('• تقديم خطط دفع مرنة للعملاء المتعثرين');
    }
    
    if (pendingPayments > totalSubscribers * 0.3) {
      recommendations.add('• تحسين طرق الدفع الإلكتروني');
      recommendations.add('• تقديم خصومات للدفع المبكر');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('• الحفاظ على الأداء الحالي الجيد');
      recommendations.add('• التوسع في استقطاب عملاء جدد');
    }
    
    return recommendations.join('\n');
  }
  
  // Generate marketing recommendations
  String _generateMarketingRecommendations(int activeSubscribers, int expiredSubscribers, Map<String, int> packageDistribution) {
    final recommendations = <String>[];
    
    if (expiredSubscribers > activeSubscribers * 0.2) {
      recommendations.add('• حملة استرداد العملاء المنتهيين بعروض خاصة');
    }
    
    // Find most popular package
    if (packageDistribution.isNotEmpty) {
      final mostPopular = packageDistribution.entries.reduce((a, b) => a.value > b.value ? a : b);
      recommendations.add('• التركيز على ترويج باقة "${mostPopular.key}" الأكثر شعبية');
    }
    
    recommendations.add('• برنامج إحالة صديق مع مكافآت مجزية');
    recommendations.add('• عروض ترقية للباقات الأعلى مع فترة تجريبية');
    recommendations.add('• حملات تسويقية موسمية بخصومات محدودة');
      return recommendations.join('\n');
  }
  // Local payment reminder generation
  Future<List<String>> _generateLocalPaymentReminders(SubscriberModel subscriber, PackageModel package) async {
    final subscriberName = subscriber.fullName;
    final debtAmount = await AppSettingsService.formatCurrency(subscriber.debtAmount);
    final packageName = package.name;
    
    return [
      'عزيزي $subscriberName، نتشرف بتذكيركم بضرورة تسديد رسوم اشتراك باقة "$packageName" البالغة $debtAmount. نقدر تعاونكم المستمر معنا.',
      'السيد/ة $subscriberName، يرجى المبادرة لتسديد المبلغ المستحق قدره $debtAmount لباقة "$packageName" لضمان استمرارية الخدمة.',
      'تحذير نهائي: سيتم إيقاف خدمة الإنترنت للمشترك $subscriberName في حال عدم تسديد المبلغ المستحق $debtAmount خلال 24 ساعة.',
    ];
  }
  // Local package recommendations
  Future<List<PackageModel>> generateLocalPackageRecommendations(SubscriberModel subscriber, List<PackageModel> availablePackages) async {
    // Get current package info from packages list
    final currentPackage = availablePackages.firstWhere(
      (p) => p.id == subscriber.packageId,
      orElse: () => PackageModel(adminId: "",serverId: "",
        id: '',
        name: 'غير محدد',
        price: 0,
        speed: '0',
        deviceCount: 0,
        durationInDays: 30,
        createdAt: DateTime.now(),
      ),
    );
    
    final currentSpeed = int.tryParse(currentPackage.speed) ?? 0;
    final currentPrice = currentPackage.price;
    final paymentHistory = subscriber.debtAmount == 0 ? 'good' : 'has_debt';
    
    // Sort packages based on suitability
    final recommendations = List<PackageModel>.from(availablePackages);
    
    recommendations.sort((a, b) {
      double scoreA = _calculatePackageScore(a, currentSpeed, currentPrice, paymentHistory);
      double scoreB = _calculatePackageScore(b, currentSpeed, currentPrice, paymentHistory);
      return scoreB.compareTo(scoreA);
    });
    
    // Return top 3 recommendations
    return recommendations.take(3).toList();
  }
  // Calculate package recommendation score
  double _calculatePackageScore(PackageModel package, int currentSpeed, double currentPrice, String paymentHistory) {
    double score = 0;
    
    // Parse package speed as integer
    final packageSpeed = int.tryParse(package.speed) ?? 0;
    
    // Speed preference score
    if (packageSpeed > currentSpeed) {
      score += 2; // Bonus for faster speed
    } else if (packageSpeed == currentSpeed) {
      score += 1; // Neutral for same speed
    }
    
    // Price preference score
    if (package.price < currentPrice) {
      score += 3; // High bonus for cheaper price
    } else if (package.price == currentPrice) {
      score += 1; // Neutral for same price
    } else if (package.price > currentPrice && package.price - currentPrice < 10) {
      score += 0.5; // Small penalty for slightly higher price
    }
    
    // Payment history consideration
    if (paymentHistory == 'good' && package.price > currentPrice) {
      score += 1; // Bonus for good payers to upgrade
    }
    
    // Speed-to-price ratio (avoid division by zero)
    if (package.price > 0) {
      double speedToPriceRatio = packageSpeed / package.price;
      score += speedToPriceRatio * 0.1;
    }
    
    return score;
  }
  // Generate network optimization recommendations
  Future<Map<String, dynamic>> generateLocalNetworkOptimization() async {
    return {
      'recommendations': [
        'فحص وتحديث أجهزة الراوتر القديمة في المناطق السكنية الكثيفة',
        'تحسين توزيع عرض النطاق خلال ساعات الذروة (8-11 مساءً)',
        'إضافة محطات تقوية الإشارة في المناطق النائية',
        'تطبيق نظام إدارة جودة الخدمة (QoS) للباقات المتميزة',
        'مراقبة الشبكة بشكل مستمر وإجراء صيانة دورية',
      ],
      'priority_areas': [
        'المناطق السكنية الجديدة',
        'المراكز التجارية والمكاتب',
        'المناطق التي تشهد شكاوى متكررة',
      ],
      'cost_estimate': '${await AppSettingsService.formatCurrency(15000)} - ${await AppSettingsService.formatCurrency(25000)} لتحسينات الشبكة الأساسية',
      'timeline': '3-6 أشهر للتطبيق الكامل',
    };
  }
  // Generate local marketing campaigns
  Future<List<Map<String, dynamic>>> generateLocalMarketingCampaigns() async {
    return [
      {
        'title': 'حملة العودة إلى المدارس',
        'description': 'باقات مخفضة للطلاب والعائلات مع بداية العام الدراسي',
        'target': 'العائلات والطلاب',
        'duration': 'شهر واحد',
        'discount': '20-30%',
        'expected_revenue': '${await AppSettingsService.formatCurrency(10000)} - ${await AppSettingsService.formatCurrency(15000)}',
      },
      {
        'title': 'حملة الباقات التجارية',
        'description': 'عروض خاصة للشركات والمكاتب مع ضمان الخدمة المتقدمة',
        'target': 'الشركات والمؤسسات',
        'duration': 'شهرين',
        'discount': '15-25%',
        'expected_revenue': '${await AppSettingsService.formatCurrency(20000)} - ${await AppSettingsService.formatCurrency(30000)}',
      },
      {
        'title': 'برنامج الإحالة والولاء',
        'description': 'مكافآت للعملاء الذين يحيلون عملاء جدد ويسددون في الوقت المحدد',
        'target': 'العملاء الحاليين',
        'duration': 'مستمر',
        'reward': 'خصم شهر مجاني أو رصيد نقدي',
        'expected_revenue': '${await AppSettingsService.formatCurrency(5000)} - ${await AppSettingsService.formatCurrency(8000)} شهرياً',
      },
    ];
  }

  // Store and retrieve local insights
  Future<void> storeLocalInsights(Map<String, dynamic> insights) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_localInsightsKey, jsonEncode({
      'lastUpdate': DateTime.now().toIso8601String(),
      'insights': insights,
    }));
  }

  Future<Map<String, dynamic>?> getStoredLocalInsights() async {
    final prefs = await SharedPreferences.getInstance();
    final insightsJson = prefs.getString(_localInsightsKey);
    if (insightsJson != null) {
      try {
        final data = jsonDecode(insightsJson);
        return data['insights'];
      } catch (e) {
        return null;
      }
    }
    return null;
  }
}
