import 'package:freezed_annotation/freezed_annotation.dart';

part 'advanced_ai_assistant_model.freezed.dart';

@freezed
class AdvancedAIAssistantModel with _$AdvancedAIAssistantModel {
  const factory AdvancedAIAssistantModel({
    required String id,
    required String deviceId,
    required String prompt,
    required String response,
    required Map<String, dynamic> context,
    required Map<String, dynamic> analysis,
    required Map<String, dynamic> recommendations,
    required Map<String, dynamic> commands,
    required DateTime timestamp,
    required String status,
    required String error,
    required int priority,
  }) = _AdvancedAIAssistantModel;

  factory AdvancedAIAssistantModel.fromJson(Map<String, dynamic> json) => 
      _$AdvancedAIAssistantModelFromJson(json);
}
