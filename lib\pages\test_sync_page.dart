import 'package:flutter/material.dart';
import 'sync_progress_page.dart';

class TestSyncPage extends StatelessWidget {
  const TestSyncPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار شاشة المزامنة'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            print('==== [TEST] Opening SyncProgressPage ====');
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const SyncProgressPage(
                  host: 'test.com',
                  username: 'test',
                  password: 'test',
                ),
              ),
            );
          },
          child: const Text('فتح شاشة المزامنة'),
        ),
      ),
    );
  }
} 