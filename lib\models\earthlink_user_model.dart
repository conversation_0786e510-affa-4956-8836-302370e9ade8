import 'dart:convert';

/// Model for Earthlink users (subscribers) based on API response
class EarthlinkUser {
  final int? userIndex;
  final String? userID;
  final String? firstName;
  final String? lastName;
  final String? displayName;
  final String? company;
  final String? address;
  final String? city;
  final String? state;
  final String? country;
  final String? zip;
  final String? email;
  final String? mobileNumber;
  final String? homePhone;
  final String? workPhone;
  final String? accountName;
  final int? accountIndex;
  final double? accountCost;
  final DateTime? subscriptionEnd;
  final bool? isActive;
  final String? status;
  final String? earthMaxMAC;
  final int? affiliateIndex;
  final String? affiliateName;

  EarthlinkUser({
    this.userIndex,
    this.userID,
    this.firstName,
    this.lastName,
    this.displayName,
    this.company,
    this.address,
    this.city,
    this.state,
    this.country,
    this.zip,
    this.email,
    this.mobileNumber,
    this.homePhone,
    this.workPhone,
    this.accountName,
    this.accountIndex,
    this.accountCost,
    this.subscriptionEnd,
    this.isActive,
    this.status,
    this.earthMaxMAC,
    this.affiliateIndex,
    this.affiliateName,
  });

  factory EarthlinkUser.fromJson(Map<String, dynamic> json) {
    // Helper function to parse dates in various formats
    DateTime? parseDate(dynamic dateValue) {
      if (dateValue == null) return null;
      
      try {
        final dateStr = dateValue.toString();
        
        // Handle ISO format dates (e.g., "2024-11-29T17:21:00")
        if (dateStr.contains('T')) {
          return DateTime.parse(dateStr);
        }
        
        // Handle format like "29/11/2024 05:21 PM"
        if (dateStr.contains('/')) {
          final parts = dateStr.split(' ');
          if (parts.length >= 2) {
            final datePart = parts[0];
            final dateParts = datePart.split('/');
            if (dateParts.length == 3) {
              // Convert from DD/MM/YYYY to YYYY-MM-DD
              final day = int.parse(dateParts[0]);
              final month = int.parse(dateParts[1]);
              final year = int.parse(dateParts[2]);
              return DateTime(year, month, day);
            }
          }
        }
        
        // Handle format like "2024-11-29"
        if (dateStr.contains('-')) {
          return DateTime.parse(dateStr.split(' ')[0]);
        }
        
        // If all else fails, try direct parsing
        return DateTime.parse(dateStr);
      } catch (e) {
        print('Error parsing Earthlink date: $dateValue - $e');
        return null;
      }
    }
    
    return EarthlinkUser(
      userIndex: json['userIndex'] is int 
          ? json['userIndex'] 
          : int.tryParse(json['userIndex']?.toString() ?? ''),
      userID: json['userId']?.toString() ?? json['userID']?.toString(),
      firstName: json['firstName']?.toString(),
      lastName: json['lastName']?.toString(),
      displayName: json['displayName']?.toString(),
      company: json['company']?.toString(),
      address: json['address']?.toString(),
      city: json['city']?.toString(),
      state: json['state']?.toString(),
      country: json['country']?.toString(),
      zip: json['zip']?.toString(),
      email: json['email']?.toString(),
      mobileNumber: json['mobileNumber']?.toString(),
      homePhone: json['homePhone']?.toString(),
      workPhone: json['workPhone']?.toString(),
      accountName: json['accountName']?.toString(),
      accountIndex: json['accountIndex'] is int 
          ? json['accountIndex'] 
          : int.tryParse(json['accountIndex']?.toString() ?? ''),
      accountCost: json['accountCost'] is double 
          ? json['accountCost']
          : double.tryParse(json['accountCost']?.toString() ?? '0'),
      subscriptionEnd: parseDate(json['subscriptionEnd']) ?? parseDate(json['manualExpirationDate']) ?? parseDate(json['expiryDate']),
      isActive: json['isActive'] == true || json['isActive']?.toString().toLowerCase() == 'true',
      status: json['status']?.toString(),
      earthMaxMAC: json['earthMaxMAC']?.toString(),
      affiliateIndex: json['affiliateIndex'] is int 
          ? json['affiliateIndex'] 
          : int.tryParse(json['affiliateIndex']?.toString() ?? ''),
      affiliateName: json['affiliateName']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userIndex': userIndex,
      'userID': userID,
      'firstName': firstName,
      'lastName': lastName,
      'displayName': displayName,
      'company': company,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'zip': zip,
      'email': email,
      'mobileNumber': mobileNumber,
      'homePhone': homePhone,
      'workPhone': workPhone,
      'accountName': accountName,
      'accountIndex': accountIndex,
      'accountCost': accountCost,
      'subscriptionEnd': subscriptionEnd?.toIso8601String(),
      'isActive': isActive,
      'status': status,
      'earthMaxMAC': earthMaxMAC,
      'affiliateIndex': affiliateIndex,
      'affiliateName': affiliateName,
    };
  }

  String get fullName {
    if (displayName != null && displayName!.isNotEmpty) {
      return displayName!;
    }
    final first = firstName ?? '';
    final last = lastName ?? '';
    return '$first $last'.trim();
  }

  String get phoneNumber {
    return mobileNumber ?? homePhone ?? workPhone ?? '';
  }

  String get username {
    return userID ?? '';
  }

  @override
  String toString() {
    return 'EarthlinkUser{userIndex: $userIndex, userID: $userID, fullName: $fullName, accountName: $accountName}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EarthlinkUser && other.userIndex == userIndex;
  }

  @override
  int get hashCode {
    return userIndex.hashCode;
  }
}