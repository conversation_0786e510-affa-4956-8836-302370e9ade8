import 'package:flutter/material.dart';
import 'package:isp_manager/services/database_service.dart';
import 'package:isp_manager/models/subscriber_model.dart';
import 'package:isp_manager/models/package_model.dart';
import 'package:isp_manager/models/activity_log_model.dart';
import 'package:isp_manager/models/payment_record_model.dart';
import 'package:isp_manager/models/user_model.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'subscriber_readonly_detail_page.dart';
import 'subscriber_service_request_page.dart';

class SubscriberLoginPage extends StatefulWidget {
  const SubscriberLoginPage({Key? key}) : super(key: key);

  @override
  State<SubscriberLoginPage> createState() => _SubscriberLoginPageState();
}

class _SubscriberLoginPageState extends State<SubscriberLoginPage> {
  final TextEditingController subscriberUsernameController = TextEditingController();
  final TextEditingController subscriberPasswordController = TextEditingController();
  String? _errorMessage;
  bool _isLoading = false;

  @override
  void dispose() {
    subscriberUsernameController.dispose();
    subscriberPasswordController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    final username = subscriberUsernameController.text.trim();
    final password = subscriberPasswordController.text.trim();
    if (username.isEmpty || password.isEmpty) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'يرجى إدخال اسم المستخدم وكلمة المرور.';
      });
      return;
    }
    try {
      final db = DatabaseService();
      final subscriber = await db.authenticateSubscriber(username, password);
      if (subscriber == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة.';
        });
        return;
      }
      // جلب الباقة
      db.adminId = subscriber.adminId;
      PackageModel? package;
      if (subscriber.packageId.isNotEmpty) {
        package = await db.getPackageById(subscriber.packageId);
      }
      // جلب السجلات
      final activityLogs = await db.getActivityLogsFire();
      final paymentRecords = await db.getPaymentRecordsFire();
      final subscriberLogs = activityLogs.where((log) => log.subscriberId == subscriber.id).toList();
      final subscriberPayments = paymentRecords.where((rec) => rec.subscriberId == subscriber.id).toList();
      if (!mounted) return;
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => SubscriberReadonlyDetailPage(
            subscriber: subscriber,
            package: package,
            activityLogs: subscriberLogs,
            paymentRecords: subscriberPayments,
          ),
        ),
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تسجيل الدخول: $e';
      });
    }
    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('دخول المشترك'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Builder(
        builder: (context) => Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.deepPurple, Colors.purpleAccent],
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
            ),
          ),
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Card(
                elevation: 8,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.person, size: 60, color: Colors.deepPurple),
                      const SizedBox(height: 20),
                      Text(
                        'تسجيل دخول المشترك',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.deepPurple,
                            ),
                      ),
                      const SizedBox(height: 24),
                      TextField(
                        controller: subscriberUsernameController,
                        decoration: InputDecoration(
                          labelText: 'اسم المستخدم',
                          prefixIcon: const Icon(Icons.person),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        keyboardType: TextInputType.text,
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: subscriberPasswordController,
                        decoration: InputDecoration(
                          labelText: 'كلمة المرور',
                          prefixIcon: const Icon(Icons.lock),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        obscureText: true,
                        keyboardType: TextInputType.text,
                      ),
                      const SizedBox(height: 24),
                      if (_errorMessage != null)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: Text(
                            _errorMessage!,
                            style: const TextStyle(color: Colors.red, fontSize: 14),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      _isLoading
                          ? const CircularProgressIndicator()
                          : Column(
                              children: [
                                ElevatedButton.icon(
                                  onPressed: _login,
                                  icon: const Icon(Icons.login),
                                  label: const Text('تسجيل الدخول'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.deepPurple,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 40, vertical: 15),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    textStyle: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton.icon(
                                  onPressed: () {
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (context) => const SubscriberServiceRequestPage(),
                                      ),
                                    );
                                  },
                                  icon: const Icon(Icons.add_location),
                                  label: const Text('طلب خدمة جديدة'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 40, vertical: 15),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    textStyle: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
