import 'package:flutter/material.dart';
import '../models/sync_operation_model.dart';
import '../services/earthlink_service.dart';
import '../services/database_service.dart';

class EarthlinkSyncProgressPage extends StatefulWidget {
  final String username;
  final String password;

  const EarthlinkSyncProgressPage({
    super.key,
    required this.username,
    required this.password,
  });

  @override
  State<EarthlinkSyncProgressPage> createState() => _EarthlinkSyncProgressPageState();
}

class _EarthlinkSyncProgressPageState extends State<EarthlinkSyncProgressPage> {
  final EarthlinkService _earthlinkService = EarthlinkService();
  final DatabaseService _databaseService = DatabaseService();
  final List<SyncOperation> _operations = [];
  final ScrollController _scrollController = ScrollController();
  bool _isSyncing = false;
  String _overallStatus = 'بدء المزامنة...';

  @override
  void initState() {
    super.initState();
    _startSync();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _addOperation(SyncOperation operation) {
    setState(() {
      _operations.add(operation);
    });
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _startSync() async {
    setState(() {
      _isSyncing = true;
      _operations.clear();
      _overallStatus = 'جاري المزامنة...';
    });

    try {
      // First, login to Earthlink
      _addOperation(SyncOperation.info(
        message: 'تسجيل الدخول إلى Earthlink...',
        status: SyncStatus.running,
      ));
      
      final loginResult = await _earthlinkService.login(
        username: widget.username,
        password: widget.password,
      );

      if (!loginResult['success']) {
        _addOperation(SyncOperation.error(
          message: 'فشل تسجيل الدخول: ${loginResult['error']}',
        ));
        setState(() {
          _isSyncing = false;
          _overallStatus = 'فشلت المزامنة';
        });
        return;
      }

      _addOperation(SyncOperation.success(
        message: 'تم تسجيل الدخول بنجاح',
      ));

      // Now call the DatabaseService synchronization method
      _addOperation(SyncOperation.info(
        message: 'بدء مزامنة البيانات مع Earthlink...',
        status: SyncStatus.running,
      ));

      // Add a more detailed progress reporting
      _addOperation(SyncOperation.info(
        message: 'جاري جلب الحسابات من Earthlink...',
        status: SyncStatus.running,
      ));

      final syncSuccess = await _databaseService.syncFromEarthlink(
        earthlinkService: _earthlinkService,
      );

      if (syncSuccess) {
        _addOperation(SyncOperation.success(
          message: 'تمت مزامنة جميع البيانات بنجاح',
        ));
        _addOperation(SyncOperation.success(
          message: 'تم حفظ الحسابات والمستخدمين في قاعدة البيانات',
        ));
        setState(() {
          _overallStatus = 'اكتملت المزامنة بنجاح!';
        });
      } else {
        _addOperation(SyncOperation.error(
          message: 'فشلت مزامنة البيانات',
        ));
        setState(() {
          _overallStatus = 'فشلت المزامنة';
        });
      }
    } catch (e) {
      _addOperation(SyncOperation.error(
        message: 'حدث خطأ أثناء المزامنة: $e',
      ));
      setState(() {
        _overallStatus = 'فشلت المزامنة';
      });
    } finally {
      setState(() {
        _isSyncing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مزامنة بيانات Earthlink'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[100],
            child: Row(
              children: [
                if (_isSyncing) const CircularProgressIndicator(),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    _overallStatus,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(8.0),
              itemCount: _operations.length,
              itemBuilder: (context, index) {
                return _buildOperationCard(_operations[index]);
              },
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16.0),
            child: ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
              label: const Text('إغلاق'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOperationCard(SyncOperation operation) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Icon(operation.icon, color: operation.color, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                operation.message,
                style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
