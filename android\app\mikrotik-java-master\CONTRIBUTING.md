Thank you for considering contributing to this project. This library is one tiny way I am giving back to the open source community, and I am
always amazed and humbled when users contribute back. 

There are several ways of contributing to this small open source library:

* You can ask for help
* You can log a bug
* You can contribute code

# Ask for help
If you experience a problem using the mikrotik-java library, the best way to get in touch with me is to either send me an
email or open an issue on GitHub. While you can seek help on StackOverflow or the Mikrotik Forums (both of which I've seen 
people do), I do not read those sites often. If you wish to log your question there (it makes sense for it to be searchable or 
in a place where other interested people can find it) and want me to take a look at it, please send me an email pointing to 
the question. 

I find that it is very easy to help users of this library if they include the following things which are usually quite relevant:

* A snipped of sample code showing what they're doing 
* The stack trace (if there is one) when it goes wrong
* Version of the library they're using

# Log a bug 
If you are pretty sure you have found a bug in the library, please log a bug by opening an issue on GithHub. When things get buggy, 
the following information (mostly the same as for asking help) becomes important:

* A snipped of sample code showing what they're doing 
* The stack trace (if there is one) when it goes wrong
* Version of the library they're using
* The version of RouterOS they're connecting to

I take bug reports very seriously, and will usually bring out immediate minor versions to fix show stoppers. 

# Contribute code 
If you want to contribute code to this library, you're welcome. Please read this section before submitting pull requests.

## If you're fixing a bug
If you've fixed a bug, please submit the pull request on GitHub. If there is an already existing issue being fixed, please 
reference that issue so I can look at what you're fixing, and if not, please explain the issue you're fixing in the pull request. 

## If you want new functionality or API changes
Please contact me via email before you create a pull request for feature additions or API changes. The mikrotik-java library is stable and,
for it's current purpose, feature complete. The situations in which I will add features to the library or change it's interface are:
* If Mikrotik adds new RouterOS functionality that requires an API change to remain feature complete
* If some very bad design flaw in the interface is discovered
* If somebody has a truly compelling reason for adding to the library. To be compelling means the added functionality must be useful 
for more users than just the submitter
* If some new pattern or convention arises in the Java standard libraries that is useful 

I *really* hate declining people's PRs, but I will do so if I don't think they add value as explained above, or if I can't figure out why
they want the change. For this reason, if you are at all unsure if your PR will be accepted, please contact me and discuss what you want 
before submitting a PR. 

Gideon 
