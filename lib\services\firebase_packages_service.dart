import 'package:cloud_firestore/cloud_firestore.dart';

class FirebasePackagesService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// جلب جميع الباقات النشطة
  Future<List<Map<String, dynamic>>> getActivePackages() async {
    try {
      final querySnapshot = await _firestore
          .collection('packages')
          .where('is_active', isEqualTo: true)
          .orderBy('price')
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('خطأ في جلب الباقات: $e');
      return [];
    }
  }

  /// جلب جميع الباقات
  Future<List<Map<String, dynamic>>> getAllPackages() async {
    try {
      final querySnapshot = await _firestore
          .collection('packages')
          .orderBy('created_at', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('خطأ في جلب جميع الباقات: $e');
      return [];
    }
  }

  /// جلب باقة محددة بالمعرف
  Future<Map<String, dynamic>?> getPackageById(String packageId) async {
    try {
      final doc = await _firestore
          .collection('packages')
          .doc(packageId)
          .get();

      if (doc.exists) {
        final data = doc.data()!;
        data['id'] = doc.id;
        return data;
      }
      return null;
    } catch (e) {
      print('خطأ في جلب الباقة: $e');
      return null;
    }
  }

  /// إنشاء باقة جديدة
  Future<String> createPackage(Map<String, dynamic> packageData) async {
    try {
      final docRef = await _firestore.collection('packages').add({
        ...packageData,
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });
      return docRef.id;
    } catch (e) {
      print('خطأ في إنشاء الباقة: $e');
      rethrow;
    }
  }

  /// تحديث باقة موجودة
  Future<void> updatePackage(String packageId, Map<String, dynamic> packageData) async {
    try {
      await _firestore
          .collection('packages')
          .doc(packageId)
          .update({
        ...packageData,
        'updated_at': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('خطأ في تحديث الباقة: $e');
      rethrow;
    }
  }

  /// حذف باقة
  Future<void> deletePackage(String packageId) async {
    try {
      await _firestore
          .collection('packages')
          .doc(packageId)
          .delete();
    } catch (e) {
      print('خطأ في حذف الباقة: $e');
      rethrow;
    }
  }

  /// جلب أرقام الواتساب النشطة
  Future<List<Map<String, dynamic>>> getActiveWhatsAppNumbers() async {
    try {
      final querySnapshot = await _firestore
          .collection('whatsapp_numbers')
          .where('is_active', isEqualTo: true)
          .orderBy('created_at', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('خطأ في جلب أرقام الواتساب: $e');
      return [];
    }
  }

  /// جلب أول رقم واتساب نشط
  Future<String?> getFirstActiveWhatsAppNumber() async {
    try {
      final querySnapshot = await _firestore
          .collection('whatsapp_numbers')
          .where('is_active', isEqualTo: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return querySnapshot.docs.first.data()['number'] as String?;
      }
      return null;
    } catch (e) {
      print('خطأ في جلب رقم الواتساب: $e');
      return null;
    }
  }

  /// إنشاء رقم واتساب جديد
  Future<String> createWhatsAppNumber(Map<String, dynamic> numberData) async {
    try {
      final docRef = await _firestore.collection('whatsapp_numbers').add({
        ...numberData,
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });
      return docRef.id;
    } catch (e) {
      print('خطأ في إنشاء رقم الواتساب: $e');
      rethrow;
    }
  }

  /// تحديث رقم واتساب
  Future<void> updateWhatsAppNumber(String numberId, Map<String, dynamic> numberData) async {
    try {
      await _firestore
          .collection('whatsapp_numbers')
          .doc(numberId)
          .update({
        ...numberData,
        'updated_at': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('خطأ في تحديث رقم الواتساب: $e');
      rethrow;
    }
  }

  /// حذف رقم واتساب
  Future<void> deleteWhatsAppNumber(String numberId) async {
    try {
      await _firestore
          .collection('whatsapp_numbers')
          .doc(numberId)
          .delete();
    } catch (e) {
      print('خطأ في حذف رقم الواتساب: $e');
      rethrow;
    }
  }

  /// جلب الباقات الموصى بها
  Future<List<Map<String, dynamic>>> getRecommendedPackages() async {
    try {
      final querySnapshot = await _firestore
          .collection('packages')
          .where('is_active', isEqualTo: true)
          .where('is_recommended', isEqualTo: true)
          .orderBy('price')
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('خطأ في جلب الباقات الموصى بها: $e');
      return [];
    }
  }

  /// البحث في الباقات
  Future<List<Map<String, dynamic>>> searchPackages(String searchTerm) async {
    try {
      // البحث في اسم الباقة
      final nameQuery = await _firestore
          .collection('packages')
          .where('name', isGreaterThanOrEqualTo: searchTerm)
          .where('name', isLessThan: searchTerm + '\uf8ff')
          .get();

      // البحث في تفاصيل الباقة
      final detailsQuery = await _firestore
          .collection('packages')
          .where('details', isGreaterThanOrEqualTo: searchTerm)
          .where('details', isLessThan: searchTerm + '\uf8ff')
          .get();

      final allDocs = {...nameQuery.docs, ...detailsQuery.docs};
      
      return allDocs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('خطأ في البحث في الباقات: $e');
      return [];
    }
  }

  /// تحديث عداد استخدام الباقة
  Future<void> incrementPackageUsage(String packageId) async {
    try {
      await _firestore
          .collection('packages')
          .doc(packageId)
          .update({
        'usage_count': FieldValue.increment(1),
        'last_used': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('خطأ في تحديث عداد استخدام الباقة: $e');
    }
  }

  /// جلب إحصائيات الباقات
  Future<Map<String, dynamic>> getPackagesStats() async {
    try {
      final totalQuery = await _firestore
          .collection('packages')
          .count()
          .get();

      final activeQuery = await _firestore
          .collection('packages')
          .where('is_active', isEqualTo: true)
          .count()
          .get();

      final recommendedQuery = await _firestore
          .collection('packages')
          .where('is_recommended', isEqualTo: true)
          .count()
          .get();

      return {
        'total_packages': totalQuery.count,
        'active_packages': activeQuery.count,
        'recommended_packages': recommendedQuery.count,
      };
    } catch (e) {
      print('خطأ في جلب إحصائيات الباقات: $e');
      return {
        'total_packages': 0,
        'active_packages': 0,
        'recommended_packages': 0,
      };
    }
  }
} 