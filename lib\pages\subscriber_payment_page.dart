import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/payment_method_model.dart';
import '../models/payment_request_model.dart';
import '../models/subscriber_model.dart';
import 'package:uuid/uuid.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:qr/qr.dart';

class SubscriberPaymentPage extends StatefulWidget {
  final SubscriberModel subscriber;
  const SubscriberPaymentPage({Key? key, required this.subscriber}) : super(key: key);

  @override
  State<SubscriberPaymentPage> createState() => _SubscriberPaymentPageState();
}

class _SubscriberPaymentPageState extends State<SubscriberPaymentPage> {
  List<PaymentMethodModel> _methods = [];
  PaymentMethodModel? _selectedMethod;
  final _amountController = TextEditingController();
  final _transactionController = TextEditingController();
  bool _isLoading = true;
  bool _isSubmitting = false;
  String? _submitMessage;
  List<PaymentRequestModel> _previousRequests = [];
  bool _loadingRequests = true;

  @override
  void initState() {
    super.initState();
    _fetchMethods();
    _fetchPreviousRequests();
  }

  Future<void> _fetchMethods() async {
    setState(() => _isLoading = true);
    final snapshot = await FirebaseFirestore.instance
        .collection('payment_methods')
        .where('adminId', isEqualTo: widget.subscriber.adminId)
        .get();
    _methods = snapshot.docs.map((doc) => PaymentMethodModel.fromMap(doc.data())).toList();
    setState(() => _isLoading = false);
  }

  Future<void> _fetchPreviousRequests() async {
    setState(() => _loadingRequests = true);
    final snapshot = await FirebaseFirestore.instance
        .collection('payment_requests')
        .where('subscriberId', isEqualTo: widget.subscriber.id)
        .orderBy('createdAt', descending: true)
        .get();
    _previousRequests = snapshot.docs.map((doc) => PaymentRequestModel.fromMap(doc.data())).toList();
    setState(() => _loadingRequests = false);
  }

  Future<void> _submitRequest() async {
    if (_selectedMethod == null || _amountController.text.isEmpty || _transactionController.text.isEmpty) {
      setState(() => _submitMessage = 'يرجى اختيار وسيلة دفع وإدخال المبلغ ورقم العملية');
      return;
    }
    setState(() {
      _isSubmitting = true;
      _submitMessage = null;
    });
    try {
      final req = PaymentRequestModel(
        id: const Uuid().v4(),
        adminId: widget.subscriber.adminId,
        subscriberId: widget.subscriber.id,
        subscriberName: widget.subscriber.fullName,
        paymentMethodId: _selectedMethod!.id,
        paymentMethodName: _selectedMethod!.name,
        amount: double.tryParse(_amountController.text) ?? 0,
        transactionNumber: _transactionController.text.trim(),
        status: PaymentRequestStatus.pending,
        createdAt: DateTime.now(),
      );
      await FirebaseFirestore.instance.collection('payment_requests').doc(req.id).set(req.toMap());
      setState(() {
        _submitMessage = 'تم إرسال طلب الدفع بنجاح! سيتم مراجعته من قبل الإدارة.';
        _amountController.clear();
        _transactionController.clear();
        _selectedMethod = null;
      });
      await _fetchPreviousRequests();
    } catch (e) {
      setState(() => _submitMessage = 'حدث خطأ أثناء إرسال الطلب: $e');
    }
    setState(() => _isSubmitting = false);
  }

  @override
  void dispose() {
    _amountController.dispose();
    _transactionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(title: const Text('الدفع الإلكتروني')),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _methods.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.account_balance_wallet, size: 80, color: Colors.teal[200]),
                      const SizedBox(height: 16),
                      const Text('لا توجد وسائل دفع متاحة حالياً.', style: TextStyle(fontSize: 18)),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Center(
                        child: Column(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.teal[50],
                                shape: BoxShape.circle,
                              ),
                              padding: const EdgeInsets.all(18),
                              child: Icon(Icons.account_balance_wallet, size: 48, color: Colors.teal[700]),
                            ),
                            const SizedBox(height: 8),
                            Text('الدفع الإلكتروني', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
                            const SizedBox(height: 4),
                            Text('اختر وسيلة الدفع وأرسل بيانات التحويل', style: theme.textTheme.bodyMedium),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      Text('وسائل الدفع المتاحة:', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 12),
                      // Payment Methods List
                      ..._methods.map((m) => Card(
                            elevation: 2,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                            color: _selectedMethod?.id == m.id ? Colors.teal[50] : theme.cardColor,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(12),
                              onTap: () => setState(() => _selectedMethod = m),
                              child: Padding(
                                padding: const EdgeInsets.all(16),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Icon(m.displayType == PaymentDisplayType.number ? Icons.numbers : Icons.qr_code, size: 32, color: Colors.teal),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(m.name, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                                          const SizedBox(height: 4),
                                          m.displayType == PaymentDisplayType.number
                                              ? SelectableText(m.accountInfo, style: theme.textTheme.bodyLarge)
                                              : Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    const Text('امسح رمز QR أدناه:', style: TextStyle(fontSize: 13)),
                                                    const SizedBox(height: 8),
                                                    Center(
                                                      child: Container(
                                                        decoration: BoxDecoration(
                                                          border: Border.all(color: Colors.teal, width: 2),
                                                          borderRadius: BorderRadius.circular(12),
                                                          color: Colors.white,
                                                        ),
                                                        padding: const EdgeInsets.all(8),
                                                        child: QrImageView(
                                                          data: m.accountInfo,
                                                          version: QrVersions.auto,
                                                          size: 120,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                        ],
                                      ),
                                    ),
                                    Radio<PaymentMethodModel>(
                                      value: m,
                                      groupValue: _selectedMethod,
                                      onChanged: (v) => setState(() => _selectedMethod = v),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          )),
                      const SizedBox(height: 24),
                      // Payment Form
                      Container(
                        decoration: BoxDecoration(
                          color: theme.cardColor,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.teal.withOpacity(0.05),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('بيانات التحويل', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                            const SizedBox(height: 12),
                            TextField(
                              controller: _amountController,
                              decoration: const InputDecoration(
                                labelText: 'المبلغ المحول',
                                prefixIcon: Icon(Icons.attach_money),
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 16),
                            TextField(
                              controller: _transactionController,
                              decoration: const InputDecoration(
                                labelText: 'رقم العملية/التحويل',
                                prefixIcon: Icon(Icons.confirmation_number),
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.text,
                            ),
                            const SizedBox(height: 16),
                            if (_submitMessage != null)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 12),
                                child: Text(
                                  _submitMessage!,
                                  style: TextStyle(
                                    color: _submitMessage!.contains('نجاح') ? Colors.green : Colors.red,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.send),
                                label: _isSubmitting ? const Text('...') : const Text('إرسال الطلب'),
                                onPressed: _isSubmitting ? null : _submitRequest,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.teal,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  textStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 32),
                      // Previous Requests Section
                      Text('طلبات الدفع السابقة:', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      _loadingRequests
                          ? const Center(child: CircularProgressIndicator())
                          : _previousRequests.isEmpty
                              ? Column(
                                  children: [
                                    Icon(Icons.receipt_long, size: 60, color: Colors.teal[100]),
                                    const SizedBox(height: 8),
                                    const Text('لا توجد طلبات دفع سابقة.', style: TextStyle(fontSize: 16)),
                                  ],
                                )
                              : ListView.separated(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: _previousRequests.length,
                                  separatorBuilder: (_, __) => const Divider(),
                                  itemBuilder: (context, i) {
                                    final r = _previousRequests[i];
                                    Color statusColor;
                                    IconData statusIcon;
                                    switch (r.status) {
                                      case PaymentRequestStatus.pending:
                                        statusColor = Colors.orange;
                                        statusIcon = Icons.hourglass_top;
                                        break;
                                      case PaymentRequestStatus.approved:
                                        statusColor = Colors.green;
                                        statusIcon = Icons.check_circle;
                                        break;
                                      case PaymentRequestStatus.rejected:
                                        statusColor = Colors.red;
                                        statusIcon = Icons.cancel;
                                        break;
                                    }
                                    return Card(
                                      elevation: 1,
                                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                                      child: ListTile(
                                        leading: Icon(statusIcon, color: statusColor, size: 32),
                                        title: Text('${r.paymentMethodName} - ${r.amount}', style: theme.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold)),
                                        subtitle: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text('رقم العملية: ${r.transactionNumber}'),
                                            Text('الحالة: ${_statusText(r.status)}', style: TextStyle(color: statusColor, fontWeight: FontWeight.bold)),
                                            Text('تاريخ الطلب: ${_formatDateTime(r.createdAt)}'),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                    ],
                  ),
                ),
    );
  }

  String _statusText(PaymentRequestStatus status) {
    switch (status) {
      case PaymentRequestStatus.pending:
        return 'بانتظار الموافقة';
      case PaymentRequestStatus.approved:
        return 'تمت الموافقة';
      case PaymentRequestStatus.rejected:
        return 'مرفوض';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} - ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
} 