import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:isp_manager/services/database_service.dart';
import '../models/network_device.dart';
import '../services/network_device_service.dart';
import '../helpers/database_helper.dart';
import '../widgets/subscriber_selector_widget.dart';
import 'device_info_page.dart';
import 'set_default_credentials_page.dart';
import '../services/firebase_auth_service.dart';
import '../models/user_model.dart';

class NetworkDevicesPage extends StatefulWidget {
  const NetworkDevicesPage({super.key});

  @override
  State<NetworkDevicesPage> createState() => _NetworkDevicesPageState();
}

class _NetworkDevicesPageState extends State<NetworkDevicesPage> {
  final NetworkDeviceService _deviceService = NetworkDeviceService();
  final DatabaseHelper _dbHelper = DatabaseHelper();
  List<NetworkDevice> _devices = [];
  bool _isLoading = true;
  UserModel? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadUser();
    _loadDevices();
  }

  Future<void> _loadUser() async {
    final authService = FirebaseAuthService();
    final firebaseUser = authService.currentUser;
    if (firebaseUser != null) {
      final userData = await authService.getUserData(firebaseUser.uid);
      setState(() {
        _currentUser = userData;
      });
    }
  }

  Future<void> _loadDevices() async {
    setState(() => _isLoading = true);
    try {
      final devices = await _dbHelper.getNetworkDevicesFire();
      await _dbHelper.syncDevicesToFirebase();
      setState(() {
        _devices = devices;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      // if (mounted) {
      //   ScaffoldMessenger.of(
      //     context,
      //   ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الأجهزة: $e')));
      // }
    }
  }

  Future<void> _deleteDevice(NetworkDevice device) async {
    if (!(_currentUser?.hasPermission(Permission.deleteNetworkDevices) ??
        false)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('ليس لديك صلاحية لحذف الجهاز'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل تريد حذف الجهاز "${device.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _dbHelper.deleteNetworkDevice(device.id);
        await _loadDevices();
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('تم حذف الجهاز بنجاح')));
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في حذف الجهاز: $e')));
        }
      }
    }
  }

  Future<void> _rebootDevice(NetworkDevice device) async {
    if (!(_currentUser?.hasPermission(Permission.rebootNetworkDevices) ??
        false)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('ليس لديك صلاحية لإعادة تشغيل الجهاز'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد إعادة التشغيل'),
        content: Text(
          'هل تريد إعادة تشغيل الجهاز "${device.name}"؟\n\nسيستغرق الجهاز بضع دقائق ليعود للعمل.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('إعادة تشغيل'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // عرض مؤشر التحميل
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text('جارٍ إعادة تشغيل الجهاز...'),
              ],
            ),
          ),
        );

        await _deviceService.rebootDevice(device);

        if (mounted) {
          Navigator.pop(context); // إغلاق مؤشر التحميل
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال أمر إعادة التشغيل بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          Navigator.pop(context); // إغلاق مؤشر التحميل
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إعادة تشغيل الجهاز: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showDeviceInfo(NetworkDevice device) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => DeviceInfoPage(device: device)),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!(_currentUser?.hasPermission(Permission.viewNetworkDevices) ??
        false)) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('أجهزة الشبكة'),
          elevation: 0,
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
        ),
        body: Column(
          children: [
            // Header with device count and add button
            Container(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'عدد الأجهزة: ${_devices.length}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Row(
                    children: [
                      ElevatedButton.icon(
                        onPressed: () => _showDefaultCredentialsDialog(),
                        icon: const Icon(Icons.vpn_key, size: 18),
                        label: const Text('بيانات افتراضية'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 10,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        onPressed: () => _navigateToAddDevice(),
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة جهاز'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Devices list
            Expanded(
              child: _isLoading
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('جارٍ تحميل الأجهزة...'),
                        ],
                      ),
                    )
                  : _devices.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.router_outlined,
                            size: 80,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'لا توجد أجهزة مضافة',
                            style: TextStyle(fontSize: 18, color: Colors.grey),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'اضغط على "إضافة جهاز" لإضافة أول جهاز',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: _loadDevices,
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _devices.length,
                        itemBuilder: (context, index) {
                          final device = _devices[index];
                          return _buildDeviceCard(device);
                        },
                      ),
                    ),
            ),
          ],
        ),
      );
    } else {
      return Scaffold(
        appBar: AppBar(
          title: const Text('أجهزة الشبكة'),
          elevation: 0,
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
        ),
        body: Column(
          children: [
            // Header with device count and add button
            Container(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'عدد الأجهزة: ${_devices.length}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Row(
                    children: [
                      ElevatedButton.icon(
                        onPressed: () => _showDefaultCredentialsDialog(),
                        icon: const Icon(Icons.vpn_key, size: 18),
                        label: const Text('بيانات افتراضية'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 10,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        onPressed: () => _navigateToAddDevice(),
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة جهاز'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Devices list
            Expanded(
              child: _isLoading
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('جارٍ تحميل الأجهزة...'),
                        ],
                      ),
                    )
                  : _devices.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.router_outlined,
                            size: 80,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'لا توجد أجهزة مضافة',
                            style: TextStyle(fontSize: 18, color: Colors.grey),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'اضغط على "إضافة جهاز" لإضافة أول جهاز',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: _loadDevices,
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _devices.length,
                        itemBuilder: (context, index) {
                          final device = _devices[index];
                          return _buildDeviceCard(device);
                        },
                      ),
                    ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildDeviceCard(NetworkDevice device) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showDeviceInfo(device),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getDeviceTypeColor(device.type).withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getDeviceTypeIcon(device.type),
                      color: _getDeviceTypeColor(device.type),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          device.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          device.ipAddress,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getDeviceTypeColor(device.type),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      device.type,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      TextButton.icon(
                        onPressed: () => _showDeviceInfo(device),
                        icon: const Icon(Icons.info_outline, size: 18),
                        label: const Text('معلومات'),
                        style: TextButton.styleFrom(
                          foregroundColor: Theme.of(context).primaryColor,
                        ),
                      ),
                      const SizedBox(width: 8),
                      TextButton.icon(
                        onPressed: () => _rebootDevice(device),
                        icon: const Icon(Icons.restart_alt, size: 18),
                        label: const Text('إعادة تشغيل'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.orange,
                        ),
                      ),
                    ],
                  ),
                  IconButton(
                    onPressed: () => _deleteDevice(device),
                    icon: const Icon(Icons.delete_outline),
                    color: Colors.red,
                    tooltip: 'حذف الجهاز',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getDeviceTypeColor(String type) {
    switch (type) {
      case 'UBNT':
        return Colors.blue;
      case 'MIMOSA':
        return Colors.green;
      case 'TPLINK':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getDeviceTypeIcon(String type) {
    switch (type) {
      case 'UBNT':
        return Icons.wifi;
      case 'MIMOSA':
        return Icons.router;
      case 'TPLINK':
        return Icons.device_hub;
      default:
        return Icons.router_outlined;
    }
  }

  void _navigateToAddDevice() async {
    if (!(_currentUser?.hasPermission(Permission.addNetworkDevices) ?? false)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('ليس لديك صلاحية لإضافة جهاز'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddDevicePage()),
    );

    if (result == true) {
      _loadDevices();
    }
  }

  void _showDefaultCredentialsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.vpn_key, color: Colors.orange),
            const SizedBox(width: 8),
            const Text('تعيين بيانات الدخول الافتراضية'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue.shade700, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'هذه البيانات ستُستخدم كبيانات افتراضية لجميع المشتركين عند جلب معلومات الإشارة',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontSize: 13,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'سيتم تطبيق هذه البيانات على جميع الأجهزة الموجودة والجديدة:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text('• عدد الأجهزة: ${_devices.length}'),
            Text('• سيتم تحديث بيانات الدخول لجميع الأجهزة'),
            Text('• يمكن للمشتركين تخصيص بيانات دخول مختلفة لاحقاً'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToSetDefaultCredentials();
            },
            icon: const Icon(Icons.settings, size: 18),
            label: const Text('تعيين البيانات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToSetDefaultCredentials() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SetDefaultCredentialsPage(devices: _devices),
      ),
    ).then((_) => _loadDevices()); // Refresh devices after setting credentials
  }
}

class AddDevicePage extends StatefulWidget {
  const AddDevicePage({super.key});

  @override
  State<AddDevicePage> createState() => _AddDevicePageState();
}

class _AddDevicePageState extends State<AddDevicePage> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ipController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  String _selectedType = 'UBNT';
  bool _obscurePassword = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _ipController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  Future<void> _addDevice() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final device = NetworkDevice(
        adminId: DatabaseService().adminId,
        id: _firestore.collection('network_devices').doc().id,
        name: _nameController.text.trim(),
        ipAddress: _ipController.text.trim(),
        type: _selectedType,
        username: _usernameController.text.trim(),
        password: _passwordController.text.trim(),
      );

      await _dbHelper.insertNetworkDevice(device);

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم إضافة الجهاز بنجاح')));
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في إضافة الجهاز: $e')));
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSubscriberSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SubscriberSelectorWidget(
          nameController: _nameController,
          onSubscriberSelected: () {
            // Optional callback when subscriber is selected
            setState(() {}); // Refresh UI if needed
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة جهاز جديد'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 20),
              // Device icon header
              Center(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.router,
                    size: 50,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
              const SizedBox(height: 30),

              // Device Name Field with Subscriber Selector
              Row(
                children: [
                  Expanded(
                    child: _buildInputField(
                      controller: _nameController,
                      label: 'اسم الجهاز',
                      icon: Icons.device_unknown,
                      validator: (value) => value?.isEmpty ?? true
                          ? 'يرجى إدخال اسم الجهاز'
                          : null,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    height: 56,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      onPressed: _showSubscriberSelector,
                      icon: const Icon(Icons.people),
                      tooltip: 'اختيار من المشتركين',
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // IP Address Field
              _buildInputField(
                controller: _ipController,
                label: 'عنوان IP',
                icon: Icons.language,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value?.isEmpty ?? true) return 'يرجى إدخال عنوان IP';
                  final ipRegex = RegExp(r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$');
                  if (!ipRegex.hasMatch(value!)) {
                    return 'يرجى إدخال عنوان IP صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Device Type Dropdown
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: DropdownButtonFormField<String>(
                  value: _selectedType,
                  decoration: const InputDecoration(
                    labelText: 'نوع الجهاز',
                    prefixIcon: Icon(Icons.category),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: 'UBNT',
                      child: Text('UBNT - يوبيكويتي'),
                    ),
                    const DropdownMenuItem(
                      value: 'MIMOSA',
                      child: Text('MIMOSA - ميموسا'),
                    ),
                    const DropdownMenuItem(
                      value: 'TPLINK',
                      child: Text('TP-LINK - تي بي لينك'),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => _selectedType = value);
                    }
                  },
                ),
              ),
              const SizedBox(height: 20),

              // Username Field
              _buildInputField(
                controller: _usernameController,
                label: 'اسم المستخدم',
                icon: Icons.person,
                validator: (value) =>
                    value?.isEmpty ?? true ? 'يرجى إدخال اسم المستخدم' : null,
              ),
              const SizedBox(height: 20),

              // Password Field
              _buildInputField(
                controller: _passwordController,
                label: 'كلمة المرور',
                icon: Icons.lock,
                obscureText: _obscurePassword,
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility : Icons.visibility_off,
                  ),
                  onPressed: () =>
                      setState(() => _obscurePassword = !_obscurePassword),
                ),
                validator: (value) =>
                    value?.isEmpty ?? true ? 'يرجى إدخال كلمة المرور' : null,
              ),
              const SizedBox(height: 40),

              // Add Button
              ElevatedButton(
                onPressed: _isLoading ? null : _addDevice,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 3,
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text(
                        'إضافة الجهاز',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
              const SizedBox(height: 20),

              // Cancel Button
              OutlinedButton(
                onPressed: _isLoading ? null : () => Navigator.pop(context),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        obscureText: obscureText,
        validator: validator,
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: Icon(icon),
          suffixIcon: suffixIcon,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }
}
