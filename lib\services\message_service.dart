import 'package:intl/intl.dart';
import '../models/message_template_model.dart';
import '../models/subscriber_model.dart';
import '../models/package_model.dart';
import 'database_service.dart';
import 'app_settings_service.dart';
import 'whatsapp_service.dart';

class MessageService {
  static final MessageService _instance = MessageService._internal();
  factory MessageService() => _instance;
  MessageService._internal();

  final DatabaseService _databaseService = DatabaseService();
  // إرسال رسالة تجديد الاشتراك
  Future<String> sendRenewalMessage(
    SubscriberModel subscriber,
    PackageModel package,
  ) async {
    final template = await _databaseService.getDefaultMessageTemplate(
      MessageTemplateType.renewal,
    );
    if (template == null) {
      final message = await _processTemplate(
        "تم تجديد اشتراك ${subscriber.fullName} فى باقة ${package.name}",
        subscriber,
        package,
      );
      return message;
    } else {
      final message = await _processTemplate(
        template.content,
        subscriber,
        package,
      );
      // هنا يمكن إضافة منطق الإرسال الفعلي للرسالة (SMS, WhatsApp, etc.)

      return message;
    }
  }

  // إرسال رسالة تسجيل دفعة
  Future<String> sendPaymentMessage(
    SubscriberModel subscriber,
    PackageModel package,
    double amount,
  ) async {
    final template = await _databaseService.getDefaultMessageTemplate(
      MessageTemplateType.payment,
    );
    if (template == null) {
      final message = await _processTemplate(
        "تم دفع",
        subscriber,
        package,
        paymentAmount: amount,
      );
      // هنا يمكن إضافة منطق الإرسال الفعلي للرسالة

      return message;
    }

    final message = await _processTemplate(
      template.content,
      subscriber,
      package,
      paymentAmount: amount,
    );
    // هنا يمكن إضافة منطق الإرسال الفعلي للرسالة

    return message;
  }

  // إرسال رسالة تذكير بالدفع
  Future<String> sendReminderMessage(
    SubscriberModel subscriber,
    PackageModel package,
  ) async {
    final packages = await _databaseService.getPackagesFire();
    print('جميع معرفات الباقات المتوفرة: ${packages.map((p) => p.id).toList()}');
    print('معرف الباقة المطلوب: ${package.id}');
    if (!packages.any((p) => p.id == package.id)) {
      print('تحذير: لم يتم العثور على باقة المشترك بالمعرف: ${package.id}');
      print('استخدام الباقة المقدمة في المعاملات...');
    }
    final template = await _databaseService.getDefaultMessageTemplate(
      MessageTemplateType.reminder,
    );
    if (template == null) {
      throw Exception('لم يتم العثور على قالب رسالة التذكير');
    }

    final message = await _processTemplate(
      template.content,
      subscriber,
      package,
    );
    // هنا يمكن إضافة منطق الإرسال الفعلي للرسالة

    return message;
  }

  // إرسال رسالة ترحيب بمشترك جديد
  Future<String> sendWelcomeMessage(
    SubscriberModel subscriber,
    PackageModel package,
  ) async {
    final template = await _databaseService.getDefaultMessageTemplate(
      MessageTemplateType.welcome,
    );
    if (template == null) {
      throw Exception('لم يتم العثور على قالب رسالة الترحيب');
    }

    final message = await _processTemplate(
      template.content,
      subscriber,
      package,
    );
    // هنا يمكن إضافة منطق الإرسال الفعلي للرسالة
    return message;
  }

  // إرسال تذكير انتهاء الاشتراك
  Future<String> sendExpiryReminder(SubscriberModel subscriber) async {
    // الحصول على الباقة
    PackageModel? package =
        await _databaseService.getPackageById(subscriber.packageId);
    
    // إذا لم يتم العثور على الباقة، استخدم باقة افتراضية
    if (package == null) {
      print('تحذير: لم يتم العثور على باقة المشترك بالمعرف: ${subscriber.packageId}');
      print('إنشاء باقة افتراضية للرسالة...');
      
      package = PackageModel(
        adminId: subscriber.adminId,
        id: 'default',
        name: 'باقة افتراضية',
        price: 0.0,
        durationInDays: 30,
        speed: 'غير محدد',
        deviceCount: 1,
        createdAt: DateTime.now(),
        serverId: '',
      );
    }

    // إرسال رسالة انتهاء الاشتراك
    final message = await sendExpiryMessage(subscriber, package);

    // فتح واتساب لإرسال الرسالة
    await sendWhatsAppMessage(subscriber, message);

    return message;
  }

  // إرسال رسالة انتهاء الاشتراك
  Future<String> sendExpiryMessage(
    SubscriberModel subscriber,
    PackageModel package,
  ) async {
    final template = await _databaseService.getDefaultMessageTemplate(
      MessageTemplateType.expiry,
    );
    if (template == null) {
      throw Exception('لم يتم العثور على قالب رسالة انتهاء الاشتراك');
    }

    final message = await _processTemplate(
      template.content,
      subscriber,
      package,
    );
    // هنا يمكن إضافة منطق الإرسال الفعلي للرسالة

    return message;
  }

  // معالجة القالب وتعبئة المتغيرات
  Future<String> _processTemplate(
    String template,
    SubscriberModel subscriber,
    PackageModel package, {
    double? paymentAmount,
    DateTime? paymentDate,
  }) async {
    final formatter = DateFormat('yyyy/MM/dd hh:mm a');
    final now = DateTime.now();

    // Handle nullable subscription dates
    final String startDateText = subscriber.subscriptionStart != null
        ? formatter.format(subscriber.subscriptionStart!)
        : 'غير محدد';
    final String endDateText = subscriber.subscriptionEnd != null
        ? formatter.format(subscriber.subscriptionEnd!)
        : 'غير محدد';
    final String daysRemainingText = subscriber.daysRemaining != null
        ? subscriber.daysRemaining.toString()
        : 'غير محدد';

    String processedTemplate = template;

    // تعبئة متغيرات المشترك
    processedTemplate = processedTemplate.replaceAll(
      '{subscriber_name}',
      subscriber.fullName,
    );

    // تعبئة متغيرات الباقة مع استخدام العملة من الإعدادات
    processedTemplate = processedTemplate.replaceAll(
      '{package_name}',
      package.name,
    );
    processedTemplate = processedTemplate.replaceAll(
      '{package_price}',
      await AppSettingsService.formatCurrency(package.price),
    );
    processedTemplate = processedTemplate.replaceAll(
      '{package_duration}',
      '${package.durationInDays} يوم',
    );
    processedTemplate = processedTemplate.replaceAll(
      '{package_speed}',
      package.speed,
    );
    processedTemplate = processedTemplate.replaceAll(
      '{package_devices}',
      package.deviceCount.toString(),
    );

    // تعبئة متغيرات التواريخ
    processedTemplate = processedTemplate.replaceAll(
      '{start_date}',
      startDateText,
    );
    processedTemplate = processedTemplate.replaceAll('{end_date}', endDateText);
    processedTemplate = processedTemplate.replaceAll(
      '{days_remaining}',
      daysRemainingText,
    );

    // تعبئة متغيرات الديون والدفع مع استخدام العملة من الإعدادات
    processedTemplate = processedTemplate.replaceAll(
      '{debt_amount}',
      await AppSettingsService.formatCurrency(subscriber.debtAmount),
    );

    if (paymentAmount != null) {
      processedTemplate = processedTemplate.replaceAll(
        '{payment_amount}',
        await AppSettingsService.formatCurrency(paymentAmount),
      );
    }

    if (paymentDate != null) {
      processedTemplate = processedTemplate.replaceAll(
        '{payment_date}',
        formatter.format(paymentDate),
      );
    } else {
      processedTemplate = processedTemplate.replaceAll(
        '{payment_date}',
        formatter.format(now),
      );
    }

    return processedTemplate;
  }

  // معاينة الرسالة قبل الإرسال
  Future<String> previewMessage(
    String template,
    SubscriberModel subscriber,
    PackageModel package, {
    double? paymentAmount,
    DateTime? paymentDate,
  }) async {
    return await _processTemplate(
      template,
      subscriber,
      package,
      paymentAmount: paymentAmount,
      paymentDate: paymentDate,
    );
  }

  // التحقق من صحة القالب
  bool validateTemplate(String template) {
    // التحقق من وجود متغيرات صحيحة فقط
    final RegExp variablePattern = RegExp(r'\{([^}]+)\}');
    final matches = variablePattern.allMatches(template);

    for (final match in matches) {
      final variable = match.group(0)!;
      if (!MessageTemplateModel.isValidVariable(variable)) {
        return false;
      }
    }

    return true;
  }

  // الحصول على قائمة المتغيرات المستخدمة في القالب
  List<String> getUsedVariables(String template) {
    final RegExp variablePattern = RegExp(r'\{([^}]+)\}');
    final matches = variablePattern.allMatches(template);

    return matches.map((match) => match.group(0)!).toSet().toList();
  }

  // إنشاء قوالب افتراضية
  Future<void> createDefaultTemplates() async {
    final existingTemplates = await _databaseService.getMessageTemplates();

    // تحقق من وجود قوالب افتراضية لكل نوع
    final types = MessageTemplateType.values;

    for (final type in types) {
      final existing = existingTemplates
          .where((t) => t.type == type && t.isDefault)
          .toList();

      if (existing.isEmpty) {
        final defaultTemplates = _getDefaultTemplates(type);
        for (final template in defaultTemplates) {
          if (template != null) {
            await _databaseService.addMessageTemplate(template);
          }
        }
      }
    }
  }

  // الحصول على القوالب الافتراضية لكل نوع
  List<MessageTemplateModel?> _getDefaultTemplates(MessageTemplateType type) {
    final id1 = _databaseService.generateId();
    final id2 = _databaseService.generateId();
    final id3 = _databaseService.generateId();

    switch (type) {
      case MessageTemplateType.renewal:
        return [
          MessageTemplateModel(
            adminId: DatabaseService().adminId,
            id: id1,
            name: 'قالب تجديد الاشتراك الافتراضي',
            content:
                'عزيزي {subscriber_name}،\n\nتم تجديد اشتراكك في باقة {package_name} بنجاح.\n\nتفاصيل الاشتراك:\n• السرعة: {package_speed}\n• عدد الأجهزة: {package_devices}\n• تاريخ الانتهاء: {end_date}\n\nشكراً لثقتكم بنا.',
            type: type,
            createdAt: DateTime.now(),
            isDefault: true,
          ),
        ];

      case MessageTemplateType.payment:
        return [
          MessageTemplateModel(
            adminId: DatabaseService().adminId,
            id: id1,
            name: 'قالب تسجيل دفعة افتراضي',
            content:
                'عزيزي {subscriber_name}،\n\nتم استلام دفعتك بمبلغ {payment_amount} بتاريخ {payment_date}.\n\nالمبلغ المتبقي: {debt_amount}\n\nشكراً لكم.',
            type: type,
            createdAt: DateTime.now(),
            isDefault: true,
          ),
        ];

      case MessageTemplateType.reminder:
        return [
          MessageTemplateModel(
            adminId: DatabaseService().adminId,
            id: id1,
            name: 'قالب تذكير بالدفع الودود',
            content:
                'عزيزي {subscriber_name}،\n\nنتشرف بتذكيركم بضرورة تسديد رسوم اشتراك باقة "{package_name}" البالغة {debt_amount}.\n\nنقدر تعاونكم المستمر معنا ونتمنى لكم تجربة ممتعة.',
            type: type,
            createdAt: DateTime.now(),
            isDefault: true,
          ),
          MessageTemplateModel(
            adminId: DatabaseService().adminId,
            id: id2,
            name: 'قالب تذكير بالدفع قوي',
            content:
                'السيد/ة {subscriber_name}،\n\nنذكركم بأن لديكم مبلغ مستحق قدره {debt_amount} لباقة "{package_name}".\n\nيرجى المبادرة لتسديد المبلغ في أقرب وقت ممكن لضمان استمرارية الخدمة.',
            type: type,
            createdAt: DateTime.now(),
            isDefault: true,
          ),
          MessageTemplateModel(
            adminId: DatabaseService().adminId,
            id: id3,
            name: 'قالب تذكير بالدفع نهائي',
            content:
                'تحذير نهائي: سيتم إيقاف خدمة الإنترنت للمشترك {subscriber_name} في حال عدم تسديد المبلغ المستحق {debt_amount} لباقة "{package_name}" خلال 24 ساعة.\n\nيرجى التواصل معنا فوراً لتجنب قطع الخدمة.',
            type: type,
            createdAt: DateTime.now(),
            isDefault: true,
          ),
        ];

      case MessageTemplateType.welcome:
        return [
          MessageTemplateModel(
            adminId: DatabaseService().adminId,
            id: id1,
            name: 'قالب ترحيب افتراضي',
            content:
                'أهلاً وسهلاً {subscriber_name}،\n\nمرحباً بكم في خدمة الإنترنت الخاصة بنا!\n\nتم تفعيل باقة {package_name} بنجاح:\n• السرعة: {package_speed}\n• عدد الأجهزة: {package_devices}\n• تاريخ الانتهاء: {end_date}\n\nنتمنى لكم تجربة ممتعة.',
            type: type,
            createdAt: DateTime.now(),
            isDefault: true,
          ),
        ];

      case MessageTemplateType.expiry:
        return [
          MessageTemplateModel(
            adminId: DatabaseService().adminId,
            id: id1,
            name: 'قالب انتهاء الاشتراك الافتراضي',
            content:
                'عزيزي {subscriber_name}،\n\nننبهكم بأن اشتراككم في باقة {package_name} سينتهي في {end_date}.\n\nالأيام المتبقية: {days_remaining} يوم\n\nيرجى التجديد لضمان استمرار الخدمة.',
            type: type,
            createdAt: DateTime.now(),
            isDefault: true,
          ),
        ];
      case MessageTemplateType.nearExpiry:
        return [
          MessageTemplateModel(
            adminId: DatabaseService().adminId,
            id: id1,
            name: 'قالب تنبيه قرب انتهاء الاشتراك الافتراضي',
            content:
                'تنبيه: اشتراككم في باقة {package_name} سينتهي بعد {days_remaining} يوم في {end_date}. يرجى التجديد لضمان استمرار الخدمة.',
            type: type,
            createdAt: DateTime.now(),
            isDefault: true,
          ),
        ];
      case MessageTemplateType.custom:
        return [null]; // لا نحتاج قالب افتراضي للمخصص
    }
  }

  // إرسال رسالة عبر واتساب
  Future<void> sendWhatsAppMessage(
    SubscriberModel subscriber,
    String message,
  ) async {
    final whatsappService = WhatsAppService();
    await whatsappService.sendMessage(subscriber.phoneNumber, message);
  }
}
