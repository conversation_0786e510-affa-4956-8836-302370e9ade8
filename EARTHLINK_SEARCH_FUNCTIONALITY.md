# Earthlink Search Functionality

## Overview
This document explains the implementation of the Earthlink API's user search functionality using the POST `/api/reseller/user/all` endpoint. This endpoint provides comprehensive filtering options for retrieving Earthlink users.

## API Endpoint
**POST** `https://rapi.earthlink.iq/api/reseller/user/all`

## Parameters

### User Status Filter (UserStatusID)
- "All" Value="0"
- "Active" Value="1"
- "Inactive" Value="2"
- "Online" Value="3"
- "Offline" Value="4"
- "Will be disabled" Value="5"
- "Disabled" Value="6"
- "Activated" Value="7"
- "Paid" Value="9"
- "Didnt pay" Value="8"

### Time Period Filter (TimePeriodID)
Used in conjunction with UserStatusID. Available values depend on the selected UserStatusID:

**For statuses: Disabled (6), Activated (7), Paid (9)**
- "Today" Value="0"
- "This week" Value="1"
- "This month" Value="2"
- "Last week" Value="3"
- "Last month" Value="4"
- "Last 7 days" Value="5"
- "Last 30 days" Value="6"

**For status: "Will be disabled" (5)**
- Days: "1", "2", "3", "4", "5", "6", "7", "10", "14", "21", "30"

**For status: "Didnt pay" (8)**
- "a week since last refill" Value="0"
- "a month since last refill" Value="1"
- "a week since last payment" Value="2"
- "a month since last payment" Value="3"

### Other Filters
- **SubAffliateIndex**: Sub affiliate ID from "get reseller affiliate"
- **AccountStatusID**: Account status ID
- **AccountType**: Package type ("MAX", "Plus", "Other") - default is null ("All")
- **AccountIDs**: Comma-separated account IDs from all accounts API
- **UserId**: User ID to search for
- **FirstName**: Search by first name (English only)
- **LastName**: Search by last name (English only)
- **CallerId**: Search by user MAC address
- **PhoneFax**: User mobile number
- **ArName**: Arabic name
- **EnName**: English name
- **Notes**: Notes field
- **CreateDate1/2**: User creation date range
- **CreateDate1S/2S**: Comparison operators for creation dates (>=, >, <, =)
- **LastModified1/2**: Last modified date range
- **LastModified1S/2S**: Comparison operators for last modified dates
- **Expiration1/2**: Expiration date range
- **Expiration1S/2S**: Comparison operators for expiration dates
- **Startindex**: Starting index for pagination (default: 0)
- **Rowcount**: Number of rows to return (default: 50)
- **OrderBy**: Field to order by (default: "Account Name")
- **OrderDescending**: Whether to order descending (default: false)
- **Query**: General search query

## Implementation Details

### New Methods Added

1. **searchUsers()** - Main method to search users with all available filters
2. **getAllUsersAdvanced()** - Method to fetch all users using advanced search strategies
3. **getUserProfile()** - Method to fetch user profile information
4. **getUserSubscriptionReport()** - Method to fetch user subscription report

### Usage Examples

#### Search for all active users
```dart
final result = await earthlinkService.searchUsers(
  userStatusID: 1, // Active
);
```

#### Search for users with a specific package type
```dart
final result = await earthlinkService.searchUsers(
  accountType: 'MAX',
);
```

#### Search for users by name
```dart
final result = await earthlinkService.searchUsers(
  firstName: 'John',
  lastName: 'Doe',
);
```

#### Search with pagination
```dart
final result = await earthlinkService.searchUsers(
  startIndex: 0,
  rowCount: 100,
);
```

## Integration with Existing Functionality

The new search functionality has been integrated with:
1. **Database Service** - Used for Earthlink user synchronization
2. **Subscriber Detail Page** - Used for fetching detailed user information
3. **Existing Fallback Methods** - Maintains backward compatibility

## Benefits

1. **Comprehensive Filtering**: More precise control over which users to retrieve
2. **Better Performance**: More efficient than character-by-character searching
3. **Rich Data**: Access to more user details than the autocomplete endpoint
4. **Flexibility**: Support for complex search criteria
5. **Pagination**: Better handling of large result sets

## Error Handling

The implementation includes robust error handling:
- Authentication checks
- Network error handling
- Response parsing validation
- Fallback mechanisms to previous methods

## Future Improvements

1. Implement caching for search results
2. Add retry logic for failed requests
3. Enhance filtering UI in the application
4. Add support for saving search presets
5. Implement real-time search as user types