import 'package:cloud_firestore/cloud_firestore.dart';

class SupportChatModel {
  final String id;
  final String subscriberId;
  final String adminId;
  final DateTime createdAt;
  final String lastMessage;
  final String lastSender; // id
  final bool isOpen;

  SupportChatModel({
    required this.id,
    required this.subscriberId,
    required this.adminId,
    required this.createdAt,
    required this.lastMessage,
    required this.lastSender,
    required this.isOpen,
  });

  factory SupportChatModel.fromMap(Map<String, dynamic> map) {
    return SupportChatModel(
      id: map['id'] ?? '',
      subscriberId: map['subscriberId'] ?? '',
      adminId: map['adminId'] ?? '',
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      lastMessage: map['lastMessage'] ?? '',
      lastSender: map['lastSender'] ?? '',
      isOpen: map['isOpen'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'subscriberId': subscriberId,
      'adminId': adminId,
      'createdAt': createdAt,
      'lastMessage': lastMessage,
      'lastSender': lastSender,
      'isOpen': isOpen,
    };
  }
} 