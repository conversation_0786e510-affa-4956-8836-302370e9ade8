# دليل إنشاء لوحة تحكم ويب لإدارة Firebase

## نظرة عامة

هذا الدليل يوضح كيفية إنشاء لوحة تحكم ويب لإدارة بيانات Firebase الخاصة بنظام إدارة مزودي الإنترنت.

---

## 1. إعداد المشروع

### 1.1 إنشاء مشروع React

```bash
# إنشاء مشروع React جديد
npx create-react-app firebase-admin-panel
cd firebase-admin-panel

# تثبيت المكتبات المطلوبة
npm install firebase react-router-dom @mui/material @emotion/react @emotion/styled
npm install @mui/icons-material @mui/x-data-grid @mui/x-date-pickers
npm install date-fns axios
```

### 1.2 إعداد Firebase

```javascript
// src/firebase/config.js
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};

const app = initializeApp(firebaseConfig);
export const db = getFirestore(app);
export const auth = getAuth(app);
```

---

## 2. هيكل المشروع

```
src/
├── components/
│   ├── Layout/
│   │   ├── Sidebar.js
│   │   ├── Header.js
│   │   └── Layout.js
│   ├── Subscriptions/
│   │   ├── SubscriptionsList.js
│   │   ├── SubscriptionForm.js
│   │   └── SubscriptionDetails.js
│   ├── Packages/
│   │   ├── PackagesList.js
│   │   ├── PackageForm.js
│   │   └── PackageDetails.js
│   ├── Updates/
│   │   ├── UpdatesList.js
│   │   ├── UpdateForm.js
│   │   └── UpdateDetails.js
│   └── WhatsApp/
│       ├── WhatsAppSettings.js
│       └── WhatsAppForm.js
├── services/
│   ├── firebaseService.js
│   ├── subscriptionService.js
│   ├── packageService.js
│   ├── updateService.js
│   └── whatsappService.js
├── hooks/
│   ├── useAuth.js
│   └── useFirestore.js
├── utils/
│   ├── constants.js
│   └── helpers.js
└── App.js
```

---

## 3. الخدمات الأساسية

### 3.1 Firebase Service

```javascript
// src/services/firebaseService.js
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc,
  query,
  where,
  orderBy,
  limit
} from 'firebase/firestore';
import { db } from '../firebase/config';

export class FirebaseService {
  constructor(collectionName) {
    this.collectionName = collectionName;
  }

  // جلب جميع المستندات
  async getAll() {
    const querySnapshot = await getDocs(collection(db, this.collectionName));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  // جلب مستند محدد
  async getById(id) {
    const docRef = doc(db, this.collectionName, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() };
    }
    return null;
  }

  // إضافة مستند جديد
  async add(data) {
    const docRef = await addDoc(collection(db, this.collectionName), {
      ...data,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    return docRef.id;
  }

  // تحديث مستند
  async update(id, data) {
    const docRef = doc(db, this.collectionName, id);
    await updateDoc(docRef, {
      ...data,
      updatedAt: new Date()
    });
  }

  // حذف مستند
  async delete(id) {
    const docRef = doc(db, this.collectionName, id);
    await deleteDoc(docRef);
  }

  // البحث حسب معيار
  async queryByField(field, value) {
    const q = query(
      collection(db, this.collectionName),
      where(field, "==", value)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }
}
```

### 3.2 Subscription Service

```javascript
// src/services/subscriptionService.js
import { FirebaseService } from './firebaseService';
import { query, where, orderBy } from 'firebase/firestore';
import { db } from '../firebase/config';

export class SubscriptionService extends FirebaseService {
  constructor() {
    super('device_subscriptions');
  }

  // جلب اشتراكات المدير
  async getByAdminId(adminId) {
    const q = query(
      collection(db, this.collectionName),
      where('adminId', '==', adminId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  // جلب الاشتراكات النشطة
  async getActiveSubscriptions(adminId) {
    const q = query(
      collection(db, this.collectionName),
      where('adminId', '==', adminId),
      where('status', '==', 'active'),
      where('endDate', '>', new Date())
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  // جلب الاشتراكات المنتهية
  async getExpiredSubscriptions(adminId) {
    const q = query(
      collection(db, this.collectionName),
      where('adminId', '==', adminId),
      where('endDate', '<', new Date())
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  // تجديد اشتراك
  async renewSubscription(id, durationDays) {
    const subscription = await this.getById(id);
    if (subscription) {
      const newEndDate = new Date(subscription.endDate);
      newEndDate.setDate(newEndDate.getDate() + durationDays);
      
      await this.update(id, {
        endDate: newEndDate,
        status: 'active'
      });
    }
  }
}
```

### 3.3 Package Service

```javascript
// src/services/packageService.js
import { FirebaseService } from './firebaseService';

export class PackageService extends FirebaseService {
  constructor() {
    super('app_subscription_packages');
  }

  // جلب الباقات النشطة
  async getActivePackages(adminId) {
    const q = query(
      collection(db, this.collectionName),
      where('adminId', '==', adminId),
      where('isActive', '==', true)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  // تفعيل/إلغاء تفعيل باقة
  async toggleStatus(id, isActive) {
    await this.update(id, { isActive });
  }
}
```

---

## 4. مكونات الواجهة

### 4.1 Layout Component

```javascript
// src/components/Layout/Layout.js
import React from 'react';
import { Box, CssBaseline } from '@mui/material';
import Sidebar from './Sidebar';
import Header from './Header';

const Layout = ({ children }) => {
  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <Sidebar />
      <Box sx={{ flexGrow: 1 }}>
        <Header />
        <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default Layout;
```

### 4.2 Sidebar Component

```javascript
// src/components/Layout/Sidebar.js
import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Box
} from '@mui/material';
import {
  Dashboard,
  Subscriptions,
  Inventory,
  SystemUpdate,
  WhatsApp,
  Settings
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';

const drawerWidth = 240;

const menuItems = [
  { text: 'لوحة التحكم', icon: <Dashboard />, path: '/' },
  { text: 'الاشتراكات', icon: <Subscriptions />, path: '/subscriptions' },
  { text: 'الباقات', icon: <Inventory />, path: '/packages' },
  { text: 'التحديثات', icon: <SystemUpdate />, path: '/updates' },
  { text: 'WhatsApp', icon: <WhatsApp />, path: '/whatsapp' },
  { text: 'الإعدادات', icon: <Settings />, path: '/settings' },
];

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
        },
      }}
    >
      <Box sx={{ overflow: 'auto', mt: 8 }}>
        <List>
          {menuItems.map((item) => (
            <ListItem
              button
              key={item.text}
              onClick={() => navigate(item.path)}
              selected={location.pathname === item.path}
            >
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItem>
        ))}
        </List>
      </Box>
    </Drawer>
  );
};

export default Sidebar;
```

### 4.3 Subscriptions List Component

```javascript
// src/components/Subscriptions/SubscriptionsList.js
import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  Typography
} from '@mui/material';
import { Edit, Delete, Refresh } from '@mui/icons-material';
import { SubscriptionService } from '../../services/subscriptionService';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

const SubscriptionsList = () => {
  const [subscriptions, setSubscriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const subscriptionService = new SubscriptionService();

  useEffect(() => {
    loadSubscriptions();
  }, []);

  const loadSubscriptions = async () => {
    try {
      setLoading(true);
      const data = await subscriptionService.getAll();
      setSubscriptions(data);
    } catch (error) {
      console.error('Error loading subscriptions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'expired':
        return 'error';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'expired':
        return 'منتهي';
      case 'pending':
        return 'في الانتظار';
      default:
        return status;
    }
  };

  if (loading) {
    return <Typography>جاري التحميل...</Typography>;
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h4">الاشتراكات</Typography>
        <Button
          variant="contained"
          startIcon={<Refresh />}
          onClick={loadSubscriptions}
        >
          تحديث
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>المشترك</TableCell>
              <TableCell>الباقة</TableCell>
              <TableCell>السعر</TableCell>
              <TableCell>تاريخ البداية</TableCell>
              <TableCell>تاريخ الانتهاء</TableCell>
              <TableCell>الحالة</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {subscriptions.map((subscription) => (
              <TableRow key={subscription.id}>
                <TableCell>{subscription.subscriberName}</TableCell>
                <TableCell>{subscription.packageName}</TableCell>
                <TableCell>
                  {subscription.price} {subscription.currency}
                </TableCell>
                <TableCell>
                  {format(subscription.startDate.toDate(), 'dd/MM/yyyy', { locale: ar })}
                </TableCell>
                <TableCell>
                  {format(subscription.endDate.toDate(), 'dd/MM/yyyy', { locale: ar })}
                </TableCell>
                <TableCell>
                  <Chip
                    label={getStatusText(subscription.status)}
                    color={getStatusColor(subscription.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <IconButton size="small">
                    <Edit />
                  </IconButton>
                  <IconButton size="small" color="error">
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default SubscriptionsList;
```

### 4.4 Subscription Form Component

```javascript
// src/components/Subscriptions/SubscriptionForm.js
import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ar } from 'date-fns/locale';
import { SubscriptionService } from '../../services/subscriptionService';
import { PackageService } from '../../services/packageService';

const SubscriptionForm = ({ open, onClose, subscription = null }) => {
  const [formData, setFormData] = useState({
    subscriberName: '',
    subscriberPhone: '',
    packageId: '',
    startDate: new Date(),
    endDate: new Date(),
    status: 'active',
    autoRenew: true
  });
  const [packages, setPackages] = useState([]);
  const [loading, setLoading] = useState(false);

  const subscriptionService = new SubscriptionService();
  const packageService = new PackageService();

  useEffect(() => {
    loadPackages();
    if (subscription) {
      setFormData({
        ...subscription,
        startDate: subscription.startDate.toDate(),
        endDate: subscription.endDate.toDate()
      });
    }
  }, [subscription]);

  const loadPackages = async () => {
    try {
      const data = await packageService.getAll();
      setPackages(data);
    } catch (error) {
      console.error('Error loading packages:', error);
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      if (subscription) {
        await subscriptionService.update(subscription.id, formData);
      } else {
        await subscriptionService.add(formData);
      }
      onClose();
    } catch (error) {
      console.error('Error saving subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {subscription ? 'تعديل اشتراك' : 'إضافة اشتراك جديد'}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mt: 2 }}>
          <TextField
            label="اسم المشترك"
            value={formData.subscriberName}
            onChange={(e) => setFormData({ ...formData, subscriberName: e.target.value })}
            fullWidth
          />
          <TextField
            label="رقم الهاتف"
            value={formData.subscriberPhone}
            onChange={(e) => setFormData({ ...formData, subscriberPhone: e.target.value })}
            fullWidth
          />
          <FormControl fullWidth>
            <InputLabel>الباقة</InputLabel>
            <Select
              value={formData.packageId}
              onChange={(e) => setFormData({ ...formData, packageId: e.target.value })}
            >
              {packages.map((pkg) => (
                <MenuItem key={pkg.id} value={pkg.id}>
                  {pkg.name} - {pkg.price} {pkg.currency}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl fullWidth>
            <InputLabel>الحالة</InputLabel>
            <Select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value })}
            >
              <MenuItem value="active">نشط</MenuItem>
              <MenuItem value="expired">منتهي</MenuItem>
              <MenuItem value="pending">في الانتظار</MenuItem>
            </Select>
          </FormControl>
          <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ar}>
            <DatePicker
              label="تاريخ البداية"
              value={formData.startDate}
              onChange={(date) => setFormData({ ...formData, startDate: date })}
              renderInput={(params) => <TextField {...params} fullWidth />}
            />
            <DatePicker
              label="تاريخ الانتهاء"
              value={formData.endDate}
              onChange={(date) => setFormData({ ...formData, endDate: date })}
              renderInput={(params) => <TextField {...params} fullWidth />}
            />
          </LocalizationProvider>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>إلغاء</Button>
        <Button onClick={handleSubmit} variant="contained" disabled={loading}>
          {loading ? 'جاري الحفظ...' : 'حفظ'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SubscriptionForm;
```

---

## 5. إعداد التوجيه (Routing)

```javascript
// src/App.js
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ar } from 'date-fns/locale';

import Layout from './components/Layout/Layout';
import Dashboard from './components/Dashboard/Dashboard';
import SubscriptionsList from './components/Subscriptions/SubscriptionsList';
import PackagesList from './components/Packages/PackagesList';
import UpdatesList from './components/Updates/UpdatesList';
import WhatsAppSettings from './components/WhatsApp/WhatsAppSettings';

const theme = createTheme({
  direction: 'rtl',
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: 'Roboto, Arial, sans-serif',
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ar}>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/subscriptions" element={<SubscriptionsList />} />
              <Route path="/packages" element={<PackagesList />} />
              <Route path="/updates" element={<UpdatesList />} />
              <Route path="/whatsapp" element={<WhatsAppSettings />} />
            </Routes>
          </Layout>
        </Router>
      </LocalizationProvider>
    </ThemeProvider>
  );
}

export default App;
```

---

## 6. إعداد المصادقة (Authentication)

```javascript
// src/hooks/useAuth.js
import { useState, useEffect } from 'react';
import { auth } from '../firebase/config';
import { onAuthStateChanged, signInWithEmailAndPassword, signOut } from 'firebase/auth';

export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const login = async (email, password) => {
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      return result.user;
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
    } catch (error) {
      throw error;
    }
  };

  return {
    user,
    loading,
    login,
    logout
  };
};
```

---

## 🔄 تحديث منطق باقات الاشتراك الافتراضية

- **أسماء الباقات الافتراضية:**
  - الباقة الشهرية
  - الباقة نصف السنوية
  - الباقة السنوية

- **كل باقة افتراضية تحتوي على:**
  - `details`: وصف مختصر
  - `features`: قائمة مميزات

- **منطق الإنشاء:**
  - يتم التحقق من وجود الباقات الافتراضية الثلاثة عند الحاجة.
  - إذا كانت أي باقة ناقصة، يتم إنشاؤها تلقائيًا.
  - لا يتم حذف أي باقة موجودة (مخصصة أو قديمة)، ولا تتكرر الباقات الافتراضية.

## مثال بيانات باقة افتراضية (Firestore):

```json
{
  "name": "الباقة الشهرية",
  "duration_days": 30,
  "price": 5000,
  "is_active": true,
  "is_recommended": true,
  "details": "أفضل خيار للتجربة أو الاستخدام القصير.",
  "features": [
    "دعم فني سريع",
    "تحديثات مجانية",
    "إمكانية الترقية في أي وقت"
  ]
}
```

---

## 7. تشغيل المشروع

### 7.1 تثبيت التبعيات

```bash
npm install
```

### 7.2 تشغيل المشروع

```bash
npm start
```

### 7.3 بناء المشروع للإنتاج

```bash
npm run build
```

---

## 8. النشر (Deployment)

### 8.1 نشر على Firebase Hosting

```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# تهيئة المشروع
firebase init hosting

# بناء المشروع
npm run build

# النشر
firebase deploy
```

### 8.2 نشر على Vercel

```bash
# تثبيت Vercel CLI
npm install -g vercel

# النشر
vercel
```

---

## 9. الميزات الإضافية

### 9.1 الإشعارات

```javascript
// src/services/notificationService.js
import { addDoc, collection } from 'firebase/firestore';
import { db } from '../firebase/config';

export const sendNotification = async (notification) => {
  await addDoc(collection(db, 'notifications'), {
    ...notification,
    createdAt: new Date(),
    read: false
  });
};
```

### 9.2 التقارير

```javascript
// src/services/reportService.js
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../firebase/config';

export const generateReport = async (adminId, startDate, endDate) => {
  const q = query(
    collection(db, 'device_subscriptions'),
    where('adminId', '==', adminId),
    where('createdAt', '>=', startDate),
    where('createdAt', '<=', endDate)
  );
  
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  }));
};
```

---

## 10. الأمان

### 10.1 قواعد الأمان

```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // التحقق من تسجيل الدخول
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // التحقق من أن المستخدم هو المدير
    function isAdmin(adminId) {
      return isAuthenticated() && request.auth.uid == adminId;
    }
    
    // قواعد الاشتراكات
    match /device_subscriptions/{document} {
      allow read, write: if isAdmin(resource.data.adminId);
    }
    
    // قواعد الباقات
    match /app_subscription_packages/{document} {
      allow read, write: if isAdmin(resource.data.adminId);
    }
    
    // قواعد التحديثات
    match /app_updates/{document} {
      allow read: if true;
      allow write: if isAuthenticated() && request.auth.token.admin == true;
    }
  }
}
```

---

## خاتمة

هذا الدليل يوفر أساساً قوياً لإنشاء لوحة تحكم ويب شاملة لإدارة Firebase. يمكنك تخصيص المكونات والوظائف حسب احتياجاتك الخاصة.

للمزيد من المعلومات:
- [وثائق Firebase](https://firebase.google.com/docs)
- [وثائق Material-UI](https://mui.com/)
- [وثائق React Router](https://reactrouter.com/) 