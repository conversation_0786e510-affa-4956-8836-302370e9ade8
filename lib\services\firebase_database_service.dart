import 'dart:async';
import 'package:uuid/uuid.dart';
import 'firebase_service.dart';
import 'migration_service.dart';
import '../models/user_model.dart';
import '../models/package_model.dart';
import '../models/subscriber_model.dart';
import '../models/activity_log_model.dart';
import '../models/payment_record_model.dart';
import '../models/message_template_model.dart';
import '../models/expense_category_model.dart';
import '../models/expense_model.dart';
import '../models/sas_server_model.dart';
import '../models/mikrotik_device_model.dart';

class FirebaseDatabaseService {
  static final FirebaseDatabaseService _instance = FirebaseDatabaseService._internal();
  factory FirebaseDatabaseService() => _instance;
  FirebaseDatabaseService._internal();

  final FirebaseService _firebaseService = FirebaseService();
  final MigrationService _migrationService = MigrationService();
  final Uuid _uuid = Uuid();

  // Initialize Firebase database
  Future<void> initialize() async {
    try {
      await _firebaseService.initialize();
      
      // Check if migration is needed
      if (!await _migrationService.isMigrationCompleted()) {
        print('==== [FirebaseDB] Migration needed, starting migration ====');
        final success = await _migrationService.migrateToFirebase();
        if (success) {
          print('==== [FirebaseDB] Migration completed successfully ====');
        } else {
          print('==== [FirebaseDB] Migration failed ====');
        }
      }
    } catch (e) {
      print('Error initializing Firebase database: $e');
      rethrow;
    }
  }

  // User operations
  Future<List<UserModel>> getUsers() async {
    try {
      final usersData = await _firebaseService.getUsers();
      return usersData.map((data) => UserModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting users: $e');
      return [];
    }
  }

  Future<String> insertUser(UserModel user) async {
    try {
      return await _firebaseService.insertUser(user.toMap());
    } catch (e) {
      print('Error inserting user: $e');
      rethrow;
    }
  }

  Future<void> updateUser(UserModel user) async {
    try {
      await _firebaseService.updateUser(user.toMap());
    } catch (e) {
      print('Error updating user: $e');
      rethrow;
    }
  }

  Future<void> deleteUser(String id) async {
    try {
      await _firebaseService.deleteUser(id);
    } catch (e) {
      print('Error deleting user: $e');
      rethrow;
    }
  }

  Future<UserModel?> getUserById(String id) async {
    try {
      final userData = await _firebaseService.getUserById(id);
      return userData != null ? UserModel.fromMap(userData) : null;
    } catch (e) {
      print('Error getting user by ID: $e');
      return null;
    }
  }

  Future<UserModel?> getUserByUsername(String username) async {
    try {
      final userData = await _firebaseService.getUserByUsername(username);
      return userData != null ? UserModel.fromMap(userData) : null;
    } catch (e) {
      print('Error getting user by username: $e');
      return null;
    }
  }

  // Package operations
  Future<List<PackageModel>> getPackages() async {
    try {
      final packagesData = await _firebaseService.getPackages();
      return packagesData.map((data) => PackageModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting packages: $e');
      return [];
    }
  }

  Future<String> insertPackage(PackageModel package) async {
    try {
      return await _firebaseService.insertPackage(package.toMap());
    } catch (e) {
      print('Error inserting package: $e');
      rethrow;
    }
  }

  Future<void> updatePackage(PackageModel package) async {
    try {
      await _firebaseService.updatePackage(package.toMap());
    } catch (e) {
      print('Error updating package: $e');
      rethrow;
    }
  }

  Future<void> deletePackage(String id) async {
    try {
      await _firebaseService.deletePackage(id);
    } catch (e) {
      print('Error deleting package: $e');
      rethrow;
    }
  }

  Future<PackageModel?> getPackageById(String id) async {
    try {
      final packageData = await _firebaseService.getPackageById(id);
      return packageData != null ? PackageModel.fromMap(packageData) : null;
    } catch (e) {
      print('Error getting package by ID: $e');
      return null;
    }
  }

  // Subscriber operations
  Future<List<SubscriberModel>> getSubscribers() async {
    try {
      final subscribersData = await _firebaseService.getSubscribers();
      return subscribersData.map((data) => SubscriberModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting subscribers: $e');
      return [];
    }
  }

  Future<String> insertSubscriber(SubscriberModel subscriber) async {
    try {
      return await _firebaseService.insertSubscriber(subscriber.toMap());
    } catch (e) {
      print('Error inserting subscriber: $e');
      rethrow;
    }
  }

  Future<void> updateSubscriber(SubscriberModel subscriber) async {
    try {
      await _firebaseService.updateSubscriber(subscriber.toMap());
    } catch (e) {
      print('Error updating subscriber: $e');
      rethrow;
    }
  }

  Future<void> deleteSubscriber(String id) async {
    try {
      await _firebaseService.deleteSubscriber(id);
    } catch (e) {
      print('Error deleting subscriber: $e');
      rethrow;
    }
  }

  Future<SubscriberModel?> getSubscriberById(String id) async {
    try {
      final subscriberData = await _firebaseService.getSubscriberById(id);
      return subscriberData != null ? SubscriberModel.fromMap(subscriberData) : null;
    } catch (e) {
      print('Error getting subscriber by ID: $e');
      return null;
    }
  }

  // Activity Log operations
  Future<List<ActivityLogModel>> getActivityLogs() async {
    try {
      final logsData = await _firebaseService.getActivityLogs();
      return logsData.map((data) => ActivityLogModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting activity logs: $e');
      return [];
    }
  }

  Future<String> insertActivityLog(ActivityLogModel log) async {
    try {
      return await _firebaseService.insertActivityLog(log.toMap());
    } catch (e) {
      print('Error inserting activity log: $e');
      rethrow;
    }
  }

  Future<List<ActivityLogModel>> getActivityLogsBySubscriber(String subscriberId) async {
    try {
      final logsData = await _firebaseService.getActivityLogsBySubscriber(subscriberId);
      return logsData.map((data) => ActivityLogModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting activity logs by subscriber: $e');
      return [];
    }
  }

  // Payment Record operations
  Future<List<PaymentRecordModel>> getPaymentRecords() async {
    try {
      final recordsData = await _firebaseService.getPaymentRecords();
      return recordsData.map((data) => PaymentRecordModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting payment records: $e');
      return [];
    }
  }

  Future<String> insertPaymentRecord(PaymentRecordModel record) async {
    try {
      return await _firebaseService.insertPaymentRecord(record.toMap());
    } catch (e) {
      print('Error inserting payment record: $e');
      rethrow;
    }
  }

  Future<List<PaymentRecordModel>> getPaymentRecordsBySubscriber(String subscriberId) async {
    try {
      final recordsData = await _firebaseService.getPaymentRecordsBySubscriber(subscriberId);
      return recordsData.map((data) => PaymentRecordModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting payment records by subscriber: $e');
      return [];
    }
  }

  // Message Template operations
  Future<List<MessageTemplateModel>> getMessageTemplates() async {
    try {
      final templatesData = await _firebaseService.getMessageTemplates();
      return templatesData.map((data) => MessageTemplateModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting message templates: $e');
      return [];
    }
  }

  Future<String> insertMessageTemplate(MessageTemplateModel template) async {
    try {
      return await _firebaseService.insertMessageTemplate(template.toMap());
    } catch (e) {
      print('Error inserting message template: $e');
      rethrow;
    }
  }

  Future<void> updateMessageTemplate(MessageTemplateModel template) async {
    try {
      await _firebaseService.updateMessageTemplate(template.toMap());
    } catch (e) {
      print('Error updating message template: $e');
      rethrow;
    }
  }

  Future<void> deleteMessageTemplate(String id) async {
    try {
      await _firebaseService.deleteMessageTemplate(id);
    } catch (e) {
      print('Error deleting message template: $e');
      rethrow;
    }
  }

  Future<MessageTemplateModel?> getMessageTemplateById(String id) async {
    try {
      final templateData = await _firebaseService.getMessageTemplateById(id);
      return templateData != null ? MessageTemplateModel.fromMap(templateData) : null;
    } catch (e) {
      print('Error getting message template by ID: $e');
      return null;
    }
  }

  Future<List<MessageTemplateModel>> getMessageTemplatesByType(int type) async {
    try {
      final templatesData = await _firebaseService.getMessageTemplatesByType(type);
      return templatesData.map((data) => MessageTemplateModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting message templates by type: $e');
      return [];
    }
  }

  // Expense Category operations
  Future<List<ExpenseCategoryModel>> getExpenseCategories() async {
    try {
      final categoriesData = await _firebaseService.getExpenseCategories();
      return categoriesData.map((data) => ExpenseCategoryModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting expense categories: $e');
      return [];
    }
  }

  Future<String> insertExpenseCategory(ExpenseCategoryModel category) async {
    try {
      return await _firebaseService.insertExpenseCategory(category.toMap());
    } catch (e) {
      print('Error inserting expense category: $e');
      rethrow;
    }
  }

  Future<void> updateExpenseCategory(ExpenseCategoryModel category) async {
    try {
      await _firebaseService.updateExpenseCategory(category.toMap());
    } catch (e) {
      print('Error updating expense category: $e');
      rethrow;
    }
  }

  Future<void> deleteExpenseCategory(String id) async {
    try {
      await _firebaseService.deleteExpenseCategory(id);
    } catch (e) {
      print('Error deleting expense category: $e');
      rethrow;
    }
  }

  Future<ExpenseCategoryModel?> getExpenseCategoryById(String id) async {
    try {
      final categoryData = await _firebaseService.getExpenseCategoryById(id);
      return categoryData != null ? ExpenseCategoryModel.fromMap(categoryData) : null;
    } catch (e) {
      print('Error getting expense category by ID: $e');
      return null;
    }
  }

  // Expense operations
  Future<List<ExpenseModel>> getExpenses() async {
    try {
      final expensesData = await _firebaseService.getExpenses();
      return expensesData.map((data) => ExpenseModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting expenses: $e');
      return [];
    }
  }

  Future<String> insertExpense(ExpenseModel expense) async {
    try {
      return await _firebaseService.insertExpense(expense.toMap());
    } catch (e) {
      print('Error inserting expense: $e');
      rethrow;
    }
  }

  Future<List<ExpenseModel>> getExpensesByCategoryId(String categoryId) async {
    try {
      final expensesData = await _firebaseService.getExpensesByCategoryId(categoryId);
      return expensesData.map((data) => ExpenseModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting expenses by category: $e');
      return [];
    }
  }

  Future<void> deleteExpense(String expenseId) async {
    try {
      await _firebaseService.deleteExpense(expenseId);
    } catch (e) {
      print('Error deleting expense: $e');
      rethrow;
    }
  }

  // SAS Server operations
  Future<List<SasServerModel>> getSasServers() async {
    try {
      final serversData = await _firebaseService.getSasServers();
      return serversData.map((data) => SasServerModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting SAS servers: $e');
      return [];
    }
  }

  Future<String> insertSasServer(SasServerModel server) async {
    try {
      return await _firebaseService.insertSasServer(server.toMap());
    } catch (e) {
      print('Error inserting SAS server: $e');
      rethrow;
    }
  }

  Future<void> updateSasServer(SasServerModel server) async {
    try {
      await _firebaseService.updateSasServer(server.toMap());
    } catch (e) {
      print('Error updating SAS server: $e');
      rethrow;
    }
  }

  Future<void> deleteSasServer(String id) async {
    try {
      await _firebaseService.deleteSasServer(id);
    } catch (e) {
      print('Error deleting SAS server: $e');
      rethrow;
    }
  }

  Future<SasServerModel?> getSasServerById(String id) async {
    try {
      final serverData = await _firebaseService.getSasServerById(id);
      return serverData != null ? SasServerModel.fromMap(serverData) : null;
    } catch (e) {
      print('Error getting SAS server by ID: $e');
      return null;
    }
  }

  Future<SasServerModel?> getConnectedSasServer() async {
    try {
      final serverData = await _firebaseService.getConnectedSasServer();
      return serverData != null ? SasServerModel.fromMap(serverData) : null;
    } catch (e) {
      print('Error getting connected SAS server: $e');
      return null;
    }
  }

  // Mikrotik Device operations
  Future<List<MikrotikDeviceModel>> getMikrotikDevices() async {
    try {
      final devicesData = await _firebaseService.getMikrotikDevices();
      return devicesData.map((data) => MikrotikDeviceModel.fromMap(data)).toList();
    } catch (e) {
      print('Error getting Mikrotik devices: $e');
      return [];
    }
  }

  Future<String> insertMikrotikDevice(MikrotikDeviceModel device) async {
    try {
      return await _firebaseService.insertMikrotikDevice(device.toMap());
    } catch (e) {
      print('Error inserting Mikrotik device: $e');
      rethrow;
    }
  }

  Future<void> updateMikrotikDevice(MikrotikDeviceModel device) async {
    try {
      await _firebaseService.updateMikrotikDevice(device.toMap());
    } catch (e) {
      print('Error updating Mikrotik device: $e');
      rethrow;
    }
  }

  Future<void> deleteMikrotikDevice(String id) async {
    try {
      await _firebaseService.deleteMikrotikDevice(id);
    } catch (e) {
      print('Error deleting Mikrotik device: $e');
      rethrow;
    }
  }

  Future<MikrotikDeviceModel?> getMikrotikDeviceById(String id) async {
    try {
      final deviceData = await _firebaseService.getMikrotikDeviceById(id);
      return deviceData != null ? MikrotikDeviceModel.fromMap(deviceData) : null;
    } catch (e) {
      print('Error getting Mikrotik device by ID: $e');
      return null;
    }
  }

  Future<MikrotikDeviceModel?> getConnectedMikrotikDevice() async {
    try {
      final deviceData = await _firebaseService.getConnectedMikrotikDevice();
      return deviceData != null ? MikrotikDeviceModel.fromMap(deviceData) : null;
    } catch (e) {
      print('Error getting connected Mikrotik device: $e');
      return null;
    }
  }

  // Database utilities
  Future<void> clearAllData() async {
    try {
      await _firebaseService.clearAllTables();
    } catch (e) {
      print('Error clearing all data: $e');
      rethrow;
    }
  }

  // Generate unique ID
  String generateId() => _uuid.v4();

  // Migration utilities
  Future<bool> isMigrationCompleted() async {
    return await _migrationService.isMigrationCompleted();
  }

  Future<bool> verifyMigration() async {
    return await _migrationService.verifyMigration();
  }

  Future<Map<String, int>> getMigrationStatistics() async {
    return await _migrationService.getMigrationStatistics();
  }

  Future<void> resetMigrationStatus() async {
    await _migrationService.resetMigrationStatus();
  }
} 