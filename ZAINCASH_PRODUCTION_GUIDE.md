# ZainCash API Documentation

[Source](https://docs.zaincash.iq/ "Permalink to ZainCash API Documentation")

## API Integrations

Thank you for your interest in ZainCash. ZainCash offers a simple robust payment gateway to transfer money instantly from anywhere to everywhere inside Iraq, in an easy, safe, and fast way

This documentation helps you integrate and implement ZainCash into your website or mobile application to allow your customers to pay you over ZainCash.

* **Version:** 1.0
* **Updated:** 6 Mar, 2023

* **Test API:** test.zaincash.iq
* **Live API:** api.zaincash.iq

If you have any suggestions to help in the improvement of this documentation, Please feel free to email us via [ZainCash's tech support](mailto:<EMAIL>).

## Requirements

To start with your ZainCash implementation you will need zaincash integration credentials to get you started, they include the following:

1. `Active wallet` - You will need an active wallet to start receiving money on.
2. `Merchant ID` - You can request a Merchant ID from ZainCash's support.
3. `Merchant secret` - This is used to decode and encode JWT during requests. also must be requested from ZainCash.

If you need to test without requesting credentials you can use our [test credentials](https://docs.zaincash.iq/#test_credentials).

If you do not have a wallet yet you can learn to create one at [Register a Wallet](https://docs.zaincash.iq/#create_wallet).

## Register a Wallet

To integrate your site application with ZainCash in order to start getting paid online, you first have to register a wallet, choosing from one of the three types of wallets available:

1. **Special Wallet** - for small business and startups, with a maximum of 100 million transactions per month and no daily limit.
2. **Corporate Wallet** - for large businesses, the maximum amount of transactions per month depends on the size of the company.
3. **Government Wallet** - for government entities.

You can create a wallet by going to the nearest ZainCash agent or using the ZainCash app on your phone, you can learn more on creating your own wallet on [Here](https://www.zaincash.iq/ekyc).

If you already have a wallet and want to request the Merchant ID for going live you can learn how to on [Going live](https://docs.zaincash.iq/#going_live).

## Transaction flow

The flow of the transaction of ZainCash can be simplified into the following points:

1. **FE** - Customer start the payment process from website or mobile.
2. **BE** - A request is being sent from the backend to ZainCash to create a Transaction ID ( [example](https://docs.zaincash.iq/#zaincash_request_example) ). containing the following parameters: 

  ParameterTypeDescription`amount`integer The amount for the transaction, IQD only and min is 250 `serviceType`string Title or info for the transaction, example: "a book" `msisdn`long integer The mobile phone number for your wallet, example format: 9647802999569. `orderId`string a reference for your system to update your tables on, example: "229" or "ss22". `redirectUrl`string a link for your website to receive the user on after he finish his payment. this is used as a webhook as well for the indication of the user's transaction status.
3. ZainCash return a Transaction ID to the backend
4. **BE** - The Customer is redirected to the link `https://ZAIN_CASH_API/transaction/pay?id=TRANSACTION_ID`, replace TRANSACTION\_ID with the received Transaction ID and ZAIN\_CASH\_API with the api of zaincash for if it is on test or live, the APIs mentioned previously.
5. **FE** - The Customer finish the transaction and is then automatically redirected to the `redirectUrl` of the transaction.
6. **BE** - The backend decodes the `token` parameter received for the `redirectUrl` link to indicate the state of the transaction. ( [example](https://docs.zaincash.iq/#zaincash_redirect_example) ) 

  Token data decoded example: 

  * `status` - (string) The status of the transaction, can be of of the following `pending`, `success`, `completed` or `failed`.
  * `orderid` - (string) The same orderid used in the transaction creation, used as a reference for your system tables.
  * `id` - (string) The ID for the transaction, can be used to check on the transaction manually from backend.
7. **BE** - System tables updated
8. **FE** - Customer notified

The below diagram explains the flow.

![](https://docs.zaincash.iq/assets/images/flow1.png)

## APIs and Methods

ZainCash provides the following endpoints you can use in your implementation:

1. `API/transaction/init` - this method is used to create new transaction IDs to be submitted by the customers. ( [example](https://docs.zaincash.iq/#zaincash_request_example) )
2. `API/transaction/get` - this method is used to check on the state of the transaction IDs submitted by the customers. ( [example](https://docs.zaincash.iq/#zaincash_check_example) )

## Create transaction example

This following is a simple example of creating a transaction ID for the ccustomer to submit

      // ----------------- Order Details --------------------------
      //The total price of your order in Iraqi Dinar only like 1000 (if in dollar, multiply it by dollar-dinar exchange rate, like 1*1300=1300)
      //Please note that it MUST BE MORE THAN 1000 IQD
      $amount = 250;

      //Type of service you provide, like 'Books', 'ecommerce cart', 'Hosting services', ...
      $service_type = "A book";

      //Order id, you can use it to help you in tagging transactions with your website IDs, if you have no order numbers in your website, leave it 1
      //Variable Type is STRING, MAX: 512 chars
      $order_id = "Bill_1234567890";

      //after a successful or failed order, the user will redirect to this url
      $redirection_url = 'http://localhost/redirect.php';

      /* ------------------------------------------------------------------------------
      Notes about $redirection_url: 
      in this url, the api will add a new parameter (token) to its end like:
      https://example.com/redirect.php?token=XXXXXXXXXXXXXX
      ------------------------------------------------------------------------------  */

      //building data
      $data = [
      'amount'  => $amount,        
      'serviceType'  => $service_type,          
      'msisdn'  => $msisdn, // Your wallet phone number
      'orderId'  => $order_id,
      'redirectUrl'  => $redirection_url,
      'iat'  => time(),
      'exp'  => time()+60*60*4
      ];

      //Encoding Token
      $newtoken = JWT::encode(
      $data,      //Data to be encoded in the JWT
      $secret ,'HS256' // secret is requested from ZainCash
      );

      $tUrl = 'https://test.zaincash.iq/transaction/init';
      $rUrl = 'https://test.zaincash.iq/transaction/pay?id=';

      //POSTing data to ZainCash API
      $data_to_post = array();
      $data_to_post['token'] = urlencode($newtoken);
      $data_to_post['merchantId'] = $merchantid; // Your merchant ID is requested from ZainCash
      $data_to_post['lang'] = $language;
      $options = array(
      'http' => array(
      'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
      'method'  => 'POST',
      'content' => http_build_query($data_to_post),
      ),
      );
      $context  = stream_context_create($options);
      $response= file_get_contents($tUrl, false, $context);

      //Parsing response
      $array = json_decode($response, true);
      $transaction_id = $array['id'];
      $newurl=$rUrl.$transaction_id;
      header('Location: '.$newurl);

If you have any questions about the example, please email us at: <<EMAIL>>

## Redirect handling example

This following is a simple example of handling the redirect and decode of the token received after transaction complete.

      require_once('credentials.php');
      require_once('includes/autoload.php');
      use \Firebase\JWT\JWT;

      /* ------------------------------------------------------------------------------
      Notes about $redirection_url: 
      in this url, the api will add a new parameter (token) to its end like:
      https://example.com/redirect.php?token=XXXXXXXXXXXXXX
      */

      if (isset($_GET['token'])){

        //you can decode the token by this PHP code:
        $result= JWT::decode($_GET['token'], $secret, array('HS256'));
        echo json_encode($result);
        $result= (array) $result;

        //And to check for status of the transaction, use $result['status'], like this:
        if ($result['status']=='success'){
          //Successful transaction

          //$result will be like this example:
          /*
          array(5) {
            ["status"]=>
            string(7) "success"
            ["orderid"]=>
            string(9) "Bill12345"
            ["id"]=>
            string(24) "58650f0f90c6362288da08cf"
            ["iat"]=>
            int(1483018052)
            ["exp"]=>
            int(1483032452)
          }
          */
        }
        if ($result['status']=='failed'){
          //Failed transaction and its reason
          $reason=$result['msg'];		
          //$result will be like this example:
          /*
          array(6) {
            ["status"]=>
            string(6) "failed"
            ["msg"]=>
            string(33) "Invalid credentials for requester"
            ["orderid"]=>
            string(9) "Bill12345"
            ["id"]=>
            string(24) "58650ca990c6362288da08c8"
            ["iat"]=>
            int(1483017397)
            ["exp"]=>
            int(1483020997)
          }
          */

        }
      } else {
        //Cancelled transaction (if he clicked "Cancel and go back"
        //NO TOKEN HERE
      }

The below diagram explains the flow of the examples.

![](https://docs.zaincash.iq/assets/images/flow2.png)

If you have any questions about the example, please email us at: <<EMAIL>>

## Check transaction example

Sometimes transactions go pending due to connectivity issue ove the customer's end, in such cases you can check for the status of the transaction as explained in the following simple example.

      require_once('credentials.php');
      require_once('includes/autoload.php');
      use \Firebase\JWT\JWT;

      // ----------------- Request Details --------------------------
      // The ID for the transaction you want to check
      $id = '61ac94f43c22082431672b27';

      //building data
      $data = [
        'id'  => $id,                
        'msisdn'  => $msisdn,
        'iat'  => time(),
        'exp'  => time()+60*60*4
      ];

      //Encoding Token
      $newtoken = JWT::encode(
      $data, //Data to be encoded in the JWT
      $secret ,'HS256'
      );

      //Check if test or production mode
      if($production_cred){
        $rUrl = 'https://api.zaincash.iq/transaction/get';
      }else{
        $rUrl = 'https://test.zaincash.iq/transaction/get';
      }

      //POST data to ZainCash API
      $data_to_post = array();
      $data_to_post['token'] = urlencode($newtoken);
      $data_to_post['merchantId'] = $merchantid;
      $options = array(
      'http' => array(
      'header' => "Content-type: application/x-www-form-urlencoded\r\n",
      'method' => 'POST',
      'content' => http_build_query($data_to_post),
      ),
      );
      $context = stream_context_create($options);
      $response = file_get_contents($rUrl, false, $context);

      echo $response;

If you have any questions about the example, please email us at: <<EMAIL>>

## Test credentials

Test credentials used to make tests in safe environment to try and handle all possible responses before going live.

## Merchant

Use one of the following merchant credentials to test the creation of transaction IDs and checking status, (make sure to copy each with no spaces. also note that the secret is one line).

\#MSISDNMerchant IDMerchant Secret196478350778935ffacf6612b5777c6d44266f $2y$10$hBbAZo2GfSSvyqAyV2SaqOfYewgYpfR1O19gIh4SqyGWdmySZYPuS 

## Customer

Use one of the following customer credentials to test the submition of transactions.

\#MSISDNPINOTP19647802999569123411112964780699926768471111

## Going live

This is the step that comes after finishing the test and you are ready to go live, if so then congratulations and let us discuss the going live part.

To go live you must have an active wallet as we mentioned before. if you already have one then you have to send us an email asking for going live so that we finish your registeration and deliver you with live credentials (**merchant ID** and **Merchant Secret**and **Merchant password**)

## Going live request

Please send an email to any of the following list with each subject as showing to request a live credentials.

\#TOSUBJECTContent1<<EMAIL>>LIVE CREDENTIALS REQUEST write here your notes about the request 

## Merchant Dashboard

ZainCash provides a dashboard you can search and view your transactions on, can be accessed here ([https://merchant.zaincash.iq](https://merchant.zaincash.iq/)).

You can use the Merchant password received from the ZainCash integration team to login.

## FAQ

This is a simple list of frequently asked questions (FAQs) and answers, if yours is not among those and the documents does not answer it then you may email us directly at [Support](mailto:<EMAIL>).

#####  [Do I need to check the transactions every time to check their status?](https://docs.zaincash.iq/#) 

#####  [How long is an operation stay pending?](https://docs.zaincash.iq/#) 

#####  [For how long will I still be able to check on transaction's status?](https://docs.zaincash.iq/#) 

#####  [I'm not sure if my use is covered. What should I do?](https://docs.zaincash.iq/#) 

#####  [Do users need the application installed to pay?](https://docs.zaincash.iq/#) 

#####  [I can not find my development language or framwork provided?](https://docs.zaincash.iq/#) 

#####  [What if I faced an issue I can not pass?](https://docs.zaincash.iq/#) 

#####  [Am I allowed to modify the transaction after its submitted?](https://docs.zaincash.iq/#) 

## Support

If this documentation doesn't answer your questions, Please send us Email to [Support team](mailto:<EMAIL>)

 We are available in from 9AM till 5PM baghdad time to answer all questions within 12-24 hours in workdays. In some rare cases the waiting time can be extended under heavy load but we will try our best.

Note: While we aim to provide the best support possible, please keep in mind that it only extends to the support in resolving technical matters, know that we will not implement the integration to you nor fix your other issues unless it's related we are going to suggest fixes however we will not provide any code help.

 If you want some code samples or ready plugins that might help you can find them at the top right part.
 Go to **Code Samples** \> **Your favourite code/plugin** \> & then You can download it and test it.

 If you are more comfortable to the old documentation version its still can be viewed [here](https://docs.zaincash.iq/old) as our main goal is to help you in all possible ways.
 Thank You for using ZainCash.
