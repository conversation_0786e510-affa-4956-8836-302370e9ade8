package com.example.isp_manager.isp_manager

import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MainActivity : FlutterFragmentActivity() {
    private val CHANNEL = "com.isp_manager.mikrotik_api"
    private lateinit var mikrotikApiHandler: MikrotikApiHandler

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        mikrotikApiHandler = MikrotikApiHandler()

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
            call, result ->
            val ip = call.argument<String>("ip")
            val username = call.argument<String>("username")
            val password = call.argument<String>("password")
            val command = call.argument<String>("command")

            when (call.method) {
                "connectToMikrotik" -> {
                    if (ip != null && username != null && password != null) {
                        CoroutineScope(Dispatchers.IO).launch {
                            try {
                                val details = mikrotikApiHandler.connect(ip, username, password).get()
                                withContext(Dispatchers.Main) {
                                    result.success(mapOf("success" to true, "details" to details))
                                }
                            } catch (e: Exception) {
                                withContext(Dispatchers.Main) {
                                    result.success(mapOf("success" to false, "error" to e.message))
                                }
                            }
                        }
                    } else {
                        result.error("INVALID_ARGUMENTS", "IP, username, or password cannot be null", null)
                    }
                }
                "sendMikrotikCommand" -> {
                    if (ip != null && username != null && password != null && command != null) {
                        CoroutineScope(Dispatchers.IO).launch {
                            try {
                                val response = mikrotikApiHandler.sendCommand(ip, username, password, command).get()
                                withContext(Dispatchers.Main) {
                                    result.success(mapOf("success" to true, "response" to response))
                                }
                            } catch (e: Exception) {
                                withContext(Dispatchers.Main) {
                                    result.success(mapOf("success" to false, "error" to e.message))
                                }
                            }
                        }
                    } else {
                        result.error("INVALID_ARGUMENTS", "IP, username, password, or command cannot be null", null)
                    }
                }
                "rebootMikrotik" -> {
                    if (ip != null && username != null && password != null) {
                        CoroutineScope(Dispatchers.IO).launch {
                            try {
                                val response = mikrotikApiHandler.reboot(ip, username, password).get()
                                withContext(Dispatchers.Main) {
                                    result.success(mapOf("success" to true, "response" to response))
                                }
                            } catch (e: Exception) {
                                withContext(Dispatchers.Main) {
                                    result.success(mapOf("success" to false, "error" to e.message))
                                }
                            }
                        }
                    } else {
                        result.error("INVALID_ARGUMENTS", "IP, username, or password cannot be null", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
