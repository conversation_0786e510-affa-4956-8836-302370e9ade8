import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import '../models/device_subscription_model.dart';
import 'firebase_app_subscription_packages_service.dart';

class FirebaseSubscriptionService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final SharedPreferences _prefs;
  static const String _deviceIdKey = 'device_id';

  FirebaseSubscriptionService(this._prefs);

  /// Retrieves device information.
  Future<Map<String, String?>> _getDeviceInfo() async {
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    String? deviceName;
    String? deviceModel;
    String? deviceBrand;
    String? androidVersion;

    if (defaultTargetPlatform == TargetPlatform.android) {
      final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceName = androidInfo.model;
      deviceModel = androidInfo.model;
      deviceBrand = androidInfo.brand;
      androidVersion = androidInfo.version.release;
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      final IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceName = iosInfo.name;
      deviceModel = iosInfo.model;
      deviceBrand = iosInfo.utsname.machine;
      androidVersion = iosInfo.systemVersion;
    } else {
      deviceName = 'Unknown Device';
      deviceModel = 'Unknown Model';
      deviceBrand = 'Unknown Brand';
      androidVersion = 'Unknown OS Version';
    }

    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    final String appVersion = packageInfo.version;

    return {
      'device_name': deviceName,
      'device_model': deviceModel,
      'device_brand': deviceBrand,
      'android_version': androidVersion,
      'app_version': appVersion,
    };
  }

  /// Generates a unique device ID and stores it locally.
  Future<String> _getOrCreateDeviceId() async {
    String? deviceId = _prefs.getString(_deviceIdKey);
    if (deviceId == null) {
      deviceId = const Uuid().v4();
      await _prefs.setString(_deviceIdKey, deviceId);
    }
    return deviceId;
  }

  /// Generates a unique account number
  Future<String> _generateAccountNumber() async {
    // Generate a random 8-digit number
    final random = Random();
    String accountNumber;
    bool isUnique = false;
    
    do {
      accountNumber = (******** + random.nextInt(********)).toString();
      
      // Check if this account number already exists
      final existing = await _firestore
          .collection('device_subscriptions')
          .where('account_number', isEqualTo: accountNumber)
          .limit(1)
          .get();
      
      isUnique = existing.docs.isEmpty;
    } while (!isUnique);
    
    return accountNumber;
  }

  /// Registers or fetches the device subscription.
  Future<DeviceSubscription?> registerOrFetchDeviceSubscription(String? email) async {
    try {
      // استخدام الدالة الجديدة لضمان وجود اشتراك
      return await ensureSubscriptionExists(email ?? "");
    } catch (e) {
      debugPrint('Error in registerOrFetchDeviceSubscription: $e');
      return null;
    }
  }

  /// Checks if the device subscription is active.
  Future<bool> isSubscriptionActive() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return false;

    try {
      final userDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists || userDoc.data()?['role'] != "UserRole.admin") {
        return false;
      }

      // استخدام الدالة الجديدة لضمان وجود اشتراك
      final subscription = await ensureSubscriptionExists(user.email ?? "");
      if (subscription == null) return false;

      return subscription.isActive && subscription.subscriptionEndDate.isAfter(DateTime.now());
    } catch (e) {
      debugPrint('Error checking subscription status: $e');
      return false;
    }
  }

  /// Fetches the current device subscription details.
  Future<DeviceSubscription?> getDeviceSubscription() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return null;

    try {
      final userDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists || userDoc.data()?['role'] != "UserRole.admin") {
        return null;
      }

      // استخدام الدالة الجديدة لضمان وجود اشتراك
      return await ensureSubscriptionExists(user.email!);
    } catch (e) {
      print('Error getting device subscription: $e');
      return null;
    }
  }

  /// Migrates old subscription from device_id to user_email
  Future<void> _migrateOldSubscription(Map<String, dynamic> oldSubscription, String userEmail) async {
    try {
      final docId = oldSubscription['id'] ?? '';
      if (docId.isNotEmpty) {
        await _firestore
            .collection('device_subscriptions')
            .doc(docId)
            .update({
          'user_email': userEmail,
          'updated_at': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      print('Error migrating subscription: $e');
    }
  }

  /// Updates subscription access count and last access time
  Future<void> updateAccessInfo(String subscriptionId) async {
    try {
      await _firestore
          .collection('device_subscriptions')
          .doc(subscriptionId)
          .update({
        'last_access': DateTime.now().toIso8601String(),
        'access_count': FieldValue.increment(1),
        'updated_at': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error updating access info: $e');
    }
  }

  /// Fetches the current server time from Firebase
  Future<DateTime?> fetchServerTime() async {
    try {
      final doc = await _firestore
          .collection('server_time')
          .doc('current')
          .get();
      
      if (doc.exists) {
        final timestamp = doc.data()?['timestamp'] as Timestamp?;
        return timestamp?.toDate();
      }
      
      // If no server time document exists, return current time
      return DateTime.now();
    } catch (e) {
      debugPrint('Error fetching server time: $e');
      return DateTime.now();
    }
  }

  /// Advanced subscription check with detailed response
  Future<Map<String, dynamic>> advancedSubscriptionCheck() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      return {'success': false, 'error': 'User not authenticated'};
    }

    try {
      final userDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists || userDoc.data()?['role'] != "UserRole.admin") {
        return {'success': false, 'error': 'User not authorized'};
      }

      final userEmail = user.email ?? "";

      // Check by email first
      var query = await _firestore
          .collection('device_subscriptions')
          .where('user_email', isEqualTo: userEmail)
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        final subscription = query.docs.first.data();
        return {
          'success': true,
          'subscription': subscription,
          'method': 'email_found'
        };
      }

      // Check by device_id
      final String deviceId = await _getOrCreateDeviceId();
      query = await _firestore
          .collection('device_subscriptions')
          .where('device_id', isEqualTo: deviceId)
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        // Migrate the subscription
        await _migrateOldSubscription(query.docs.first.data(), userEmail);
        
        // Fetch the migrated subscription
        final migratedQuery = await _firestore
            .collection('device_subscriptions')
            .where('user_email', isEqualTo: userEmail)
            .limit(1)
            .get();

        if (migratedQuery.docs.isNotEmpty) {
          return {
            'success': true,
            'subscription': migratedQuery.docs.first.data(),
            'method': 'migrated'
          };
        }
      }

      return {'success': false, 'error': 'No valid subscription found'};
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  /// Get device subscription by email
  Future<DeviceSubscription?> getDeviceSubscriptionByEmail(String email) async {
    try {
      final query = await _firestore
          .collection('device_subscriptions')
          .where('user_email', isEqualTo: email)
          .orderBy('subscription_end_date', descending: true)
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        final data = query.docs.first.data();
        data['id'] = query.docs.first.id; // إضافة معرف المستند
        
        // معالجة التواريخ من Firebase
        if (data['created_at'] is Timestamp) {
          data['created_at'] = (data['created_at'] as Timestamp).toDate().toIso8601String();
        }
        if (data['updated_at'] is Timestamp) {
          data['updated_at'] = (data['updated_at'] as Timestamp).toDate().toIso8601String();
        }
        
        return DeviceSubscription.fromJson(data);
      }
      return null;
    } catch (e) {
      print('Error getting device subscription by email: $e');
      return null;
    }
  }

  /// Create subscription for user
  Future<void> createSubscriptionForUser({
    required String userEmail,
    required DeviceSubscription adminSubscription,
  }) async {
    try {
      final Map<String, dynamic> newSubscriptionData = {
        'device_id': adminSubscription.deviceId,
        'device_name': adminSubscription.deviceName,
        'device_model': adminSubscription.deviceModel,
        'device_brand': adminSubscription.deviceBrand,
        'android_version': adminSubscription.androidVersion,
        'app_version': adminSubscription.appVersion,
        'account_number': adminSubscription.accountNumber,
        'subscription_start_date': DateTime.now().toIso8601String(),
        'subscription_end_date': DateTime.now()
            .add(const Duration(days: 30))
            .toIso8601String(),
        'is_active': true,
        'last_access': DateTime.now().toIso8601String(),
        'access_count': 1,
        'user_email': userEmail,
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      };

      await _firestore
          .collection('device_subscriptions')
          .add(newSubscriptionData);
    } catch (e) {
      print('Error creating subscription for user: $e');
    }
  }

  /// Request subscription renewal
  Future<void> requestSubscriptionRenewal() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      await _firestore
          .collection('renewal_requests')
          .add({
        'user_email': user.email,
        'request_date': FieldValue.serverTimestamp(),
        'status': 'pending',
        'notes': 'طلب تجديد تلقائي',
      });
    } catch (e) {
      print('Error requesting subscription renewal: $e');
    }
  }

  /// جلب جميع اشتراكات مدير محدد
  Future<List<DeviceSubscription>> getSubscriptionsByAdmin(String adminId) async {
    try {
      // البحث بواسطة user_email بدلاً من adminId
      final query = await _firestore
          .collection('device_subscriptions')
          .where('user_email', isEqualTo: adminId)
          .get();
      return query.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id; // إضافة معرف المستند
        
        // معالجة التواريخ من Firebase
        if (data['created_at'] is Timestamp) {
          data['created_at'] = (data['created_at'] as Timestamp).toDate().toIso8601String();
        }
        if (data['updated_at'] is Timestamp) {
          data['updated_at'] = (data['updated_at'] as Timestamp).toDate().toIso8601String();
        }
        
        return DeviceSubscription.fromJson(data);
      }).toList();
    } catch (e) {
      print('Error getting subscriptions by admin: $e');
      return [];
    }
  }

  /// إنشاء اشتراك جديد تلقائياً للمستخدم إذا لم يكن موجود
  Future<DeviceSubscription?> createNewSubscriptionIfNotExists(String userEmail) async {
    try {
      // التحقق من وجود اشتراك
      final existingSubscription = await getDeviceSubscriptionByEmail(userEmail);
      if (existingSubscription != null) {
        return existingSubscription;
      }

      // إنشاء اشتراك جديد
      final Map<String, String?> deviceInfo = await _getDeviceInfo();
      final String deviceId = await _getOrCreateDeviceId();
      final String appVersion = deviceInfo['app_version'] ?? 'Unknown';
      final String newAccountNumber = await _generateAccountNumber();

      final Map<String, dynamic> newSubscriptionData = {
        'device_id': deviceId,
        'device_name': deviceInfo['device_name'],
        'device_model': deviceInfo['device_model'],
        'device_brand': deviceInfo['device_brand'],
        'android_version': deviceInfo['android_version'],
        'app_version': appVersion,
        'account_number': newAccountNumber,
        'subscription_start_date': DateTime.now().toIso8601String(),
        'subscription_end_date': DateTime.now()
            .add(const Duration(days: 2)) // يومين كفترة تجريبية
            .toIso8601String(),
        'is_active': true,
        'last_access': DateTime.now().toIso8601String(),
        'access_count': 1,
        'user_email': userEmail,
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      };

      final docRef = await _firestore
          .collection('device_subscriptions')
          .add(newSubscriptionData);

      final newDoc = await docRef.get();
      print('✅ تم إنشاء اشتراك جديد للمستخدم: $userEmail');
      final data = newDoc.data()!;
      data['id'] = newDoc.id; // إضافة معرف المستند
      
      // معالجة التواريخ من Firebase
      if (data['created_at'] is Timestamp) {
        data['created_at'] = (data['created_at'] as Timestamp).toDate().toIso8601String();
      }
      if (data['updated_at'] is Timestamp) {
        data['updated_at'] = (data['updated_at'] as Timestamp).toDate().toIso8601String();
      }
      
      return DeviceSubscription.fromJson(data);
    } catch (e) {
      print('Error creating new subscription: $e');
      return null;
    }
  }

  /// فحص وإنشاء اشتراك جديد إذا لزم الأمر
  Future<DeviceSubscription?> ensureSubscriptionExists(String userEmail) async {
    try {
      // ضمان وجود باقات اشتراكات التطبيق الافتراضية
      await ensureDefaultAppSubscriptionPackagesExist();
      
      // محاولة جلب الاشتراك الموجود
      var subscription = await getDeviceSubscriptionByEmail(userEmail);
      
      // إذا لم يكن موجود، إنشاء واحد جديد
      if (subscription == null) {
        print('🔄 لا يوجد اشتراك للمستخدم $userEmail، جاري إنشاء اشتراك جديد...');
        subscription = await createNewSubscriptionIfNotExists(userEmail);
      }
      
      return subscription;
    } catch (e) {
      print('Error ensuring subscription exists: $e');
      return null;
    }
  }

  /// إنشاء باقات اشتراكات التطبيق الافتراضية إذا لم تكن موجودة
  Future<void> ensureDefaultAppSubscriptionPackagesExist() async {
    try {
      final appPackagesService = FirebaseAppSubscriptionPackagesService();
      final existingPackages = await appPackagesService.getActiveAppSubscriptionPackages();
      
      if (existingPackages.isEmpty) {
        print('🔄 لا توجد باقات اشتراكات تطبيق، جاري إنشاء الباقات الافتراضية...');
        await appPackagesService.createDefaultAppSubscriptionPackages();
      }
    } catch (e) {
      print('Error ensuring default app subscription packages exist: $e');
    }
  }
} 