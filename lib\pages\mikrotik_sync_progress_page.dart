import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Import for Clipboard
import '../models/mikrotik_user_model.dart';
import '../models/sas_server_model.dart';
import '../services/mikrotik_sync_service.dart';
import '../models/sync_operation_model.dart';
import '../models/subscriber_model.dart';
import '../models/package_model.dart';
import '../services/database_service.dart';
import 'package:uuid/uuid.dart';

// Enum to manage the screen's state
enum SyncScreenState { syncing, selectingProfile, showingScript, installing, done }

class MikrotikSyncProgressPage extends StatefulWidget {
  final SasServerModel server;

  const MikrotikSyncProgressPage({super.key, required this.server});

  @override
  State<MikrotikSyncProgressPage> createState() => _MikrotikSyncProgressPageState();
}

class _MikrotikSyncProgressPageState extends State<MikrotikSyncProgressPage> {
  final List<SyncOperation> _operations = [];
  final ScrollController _scrollController = ScrollController();
  final _uuid = Uuid();

  // State management variables
  SyncScreenState _screenState = SyncScreenState.syncing;
  String _overallStatus = 'بدء المزامنة...';
  double _overallProgress = 0.0;
  int _totalSubscribers = 0;
  int _totalPackages = 0;
  int _processedSubscribers = 0;
  int _processedPackages = 0;

  // Variables for profile selection and scheduler creation
  List<PackageModel> _fetchedProfiles = [];
  PackageModel? _selectedExpirationProfile;
  String _generatedScript = '';
  String _statusMessage = '';
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _startSync();
  }

  void _addOperation(SyncOperation operation) {
    if (mounted) {
      setState(() {
        _operations.add(operation);
      });
      _scrollToBottom();
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // This new function groups all sync operations together.
  Future<List<PackageModel>> _performFullSync(MikrotikSyncService api, DatabaseService db) async {
    _addOperation(SyncOperation.connection(message: 'جاري الاتصال وتنزيل الباقات...', status: SyncStatus.running));
    final remotePackages = await _syncPackages(api, db);
    _addOperation(SyncOperation.connection(message: 'تم تنزيل الباقات بنجاح', status: SyncStatus.completed));

    await _syncSubscribers(api, db);
    return remotePackages;
  }

  Future<void> _startSync() async {
    runZonedGuarded(() async {
      if (!mounted) return;
      setState(() {
        _screenState = SyncScreenState.syncing;
        _operations.clear();
        _overallProgress = 0.0;
        _totalSubscribers = 0;
        _totalPackages = 0;
        _processedSubscribers = 0;
        _processedPackages = 0;
      });

      try {
        _addOperation(SyncOperation.info(
          message: 'بدء المزامنة مع خادم MikroTik',
          details: widget.server.name,
        ));

        final api = MikrotikSyncService(widget.server);
        final db = DatabaseService();

        // Step 1: Perform all sync operations and wait for completion.
        final remotePackages = await _performFullSync(api, db);

        _addOperation(SyncOperation.success(
          message: 'اكتملت مزامنة البيانات بنجاح',
          details: 'تم مزامنة $_processedPackages باقة و $_processedSubscribers مشترك',
        ));

        // Step 2: Now that sync is complete, transition to profile selection.
        if (!mounted) return;
        setState(() {
          _fetchedProfiles = remotePackages;
          _screenState = SyncScreenState.selectingProfile;
          _overallStatus = 'اكتملت المزامنة، يرجى تحديد باقة الانتهاء';
          _overallProgress = 1.0;
        });

      } catch (e, stack) {
        print('[Sync Error] $e\n$stack');
        _addOperation(SyncOperation.error(message: 'حدث خطأ أثناء المزامنة', details: '$e'));
        if (!mounted) return;
        setState(() {
          _screenState = SyncScreenState.done;
          _statusMessage = 'فشلت المزامنة: $e';
        });
      }
    }, (error, stack) {
      print('[Unhandled Sync Error] $error\n$stack');
      _addOperation(SyncOperation.error(message: 'حدث خطأ غير متوقع', details: '$error'));
      if (!mounted) return;
      setState(() {
        _screenState = SyncScreenState.done;
        _statusMessage = 'فشلت المزامنة: $error';
      });
    });
  }

  Future<List<PackageModel>> _syncPackages(MikrotikSyncService api, DatabaseService db) async {
    _addOperation(SyncOperation.fetchPackages(message: 'جلب البروفايلات من MikroTik', status: SyncStatus.running));
    final remotePackages = await api.fetchPppProfiles();
    _totalPackages = remotePackages.length;
    _addOperation(SyncOperation.fetchPackages(message: 'تم جلب $_totalPackages بروفايل', status: SyncStatus.completed));

    final localPackages = await db.getPackagesFire();

    for (final package in remotePackages) {
      final existingPackage = localPackages.firstWhere(
        (p) => p.mikrotikProfileName == package.name,
        orElse: () => PackageModel(id: '', name: '', price: 0, durationInDays: 0, speed: '', deviceCount: 0, adminId: '', serverId: '', createdAt: DateTime.now()),
      );

      if (existingPackage.id.isNotEmpty) {
        final updated = existingPackage.copyWith(adminId: db.adminId, price: package.price, speed: package.speed);
        await db.updatePackage(updated);
        _addOperation(SyncOperation.savePackage(packageName: 'تم تحديث: ${package.name}', status: SyncStatus.completed));
      } else {
        final newPackage = package.copyWith(adminId: db.adminId, id: db.generateId(), mikrotikProfileName: package.name);
        await db.addPackage(newPackage);
        _addOperation(SyncOperation.savePackage(packageName: 'تم إضافة: ${package.name}', status: SyncStatus.completed));
      }
      _processedPackages++;
      _updateOverallProgress();
    }
    return remotePackages;
  }

  Future<void> _syncSubscribers(MikrotikSyncService api, DatabaseService db) async {
    _addOperation(SyncOperation.fetchSubscribers(message: 'جلب المشتركين (PPP Secrets) من MikroTik', status: SyncStatus.running));
    final remoteUsers = await api.fetchPppSecrets();
    _totalSubscribers = remoteUsers.length;
    _addOperation(SyncOperation.fetchSubscribers(message: 'تم جلب $_totalSubscribers مشترك', status: SyncStatus.completed));

    final localPackages = await db.getPackagesFire();
    final localSubscribers = await db.getSubscribersFire();

    for (final user in remoteUsers) {
      try {
        final matchingPackage = localPackages.firstWhere(
          (p) => p.mikrotikProfileName == user.profile,
          orElse: () => PackageModel(id: '', name: 'باقة غير معروفة', price: 0, durationInDays: 30, speed: '', deviceCount: 1, adminId: db.adminId, serverId: widget.server.id ?? '', createdAt: DateTime.now()),
        );

        if (matchingPackage.id.isEmpty) {
          _addOperation(SyncOperation.saveSubscriber(subscriberName: user.name, status: SyncStatus.failed, details: 'لم يتم العثور على باقة مطابقة للبروفايل: ${user.profile}'));
          continue;
        }

        final existingSubscriber = localSubscribers.firstWhere(
          (s) => s.username == user.name,
          orElse: () => SubscriberModel(id: '', fullName: '', phoneNumber: '', packageId: '', packageName: '', address: '', paymentStatus: PaymentStatus.pending, subscriptionStart: DateTime.now(), subscriptionEnd: DateTime.now(), macAddress: '', routerName: '', technicalNotes: '', debtAmount: 0, createdAt: DateTime.now(), isActive: false, subscriptionType: SubscriptionType.broadband, username: '', password: '', adminId: db.adminId),
        );

        String subscriberName = user.name;
        final RegExp nameRegex = RegExp(r'"\s*([^\"]+)\s*"');
        final nameMatch = nameRegex.firstMatch(user.comment);
        if (nameMatch != null) {
          subscriberName = nameMatch.group(1)!.trim();
        }

        if (existingSubscriber.id.isNotEmpty) {
          final updated = existingSubscriber.copyWith(
            sasServerId: widget.server.id,
            adminId: db.adminId,
            fullName: subscriberName,
            packageId: matchingPackage.id,
            packageName: matchingPackage.name,
            subscriptionEnd: user.expirationDate ?? existingSubscriber.subscriptionEnd,
            isActive: !user.isExpired,
            password: user.password,
          );
          await db.updateSubscriber(updated, isSyncUpdate: true);
          _addOperation(SyncOperation.saveSubscriber(subscriberName: 'تم تحديث: $subscriberName', status: SyncStatus.completed));
        } else {
          final newSubscriber = SubscriberModel(
            id: _uuid.v4(),
            adminId: db.adminId,
            fullName: subscriberName,
            username: user.name,
            phoneNumber: '',
            packageId: matchingPackage.id,
            packageName: matchingPackage.name,
            address: 'غير محدد',
            paymentStatus: PaymentStatus.pending,
            subscriptionStart: DateTime.now(),
            subscriptionEnd: user.expirationDate ?? DateTime.now().add(Duration(days: 30)),
            isActive: !user.isExpired,
            technicalNotes: 'تمت المزامنة من MikroTik: ${widget.server.name}',
            sasServerId: widget.server.id,
            createdAt: DateTime.now(),
            debtAmount: 0,
            macAddress: '',
            password: user.password,
            routerName: '',
            subscriptionType: SubscriptionType.broadband,
          );
          await db.addSubscriber(newSubscriber, isSyncUpdate: true);
          _addOperation(SyncOperation.saveSubscriber(subscriberName: 'تم إضافة: $subscriberName', status: SyncStatus.completed));
        }
        _processedSubscribers++;
        _updateOverallProgress();
      } catch (e) {
        _addOperation(SyncOperation.saveSubscriber(
          subscriberName: user.name,
          status: SyncStatus.failed,
          details: 'فشل في معالجة المشترك: $e',
        ));
      }
    }
  }

  void _updateOverallProgress() {
    if (mounted) {
      setState(() {
        final totalItems = _totalPackages + _totalSubscribers;
        final processedItems = _processedPackages + _processedSubscribers;
        _overallProgress = totalItems > 0 ? processedItems / totalItems : 0;
      });
    }
  }

  void _generateScript() {
    if (_selectedExpirationProfile == null) return;

    final api = MikrotikSyncService(widget.server);
    final targetProfile = _selectedExpirationProfile!.name;
    final activeProfiles = _fetchedProfiles
        .where((p) => p.name != targetProfile)
        .map((p) => p.name)
        .toList();

    final script = api.generateExpirationScript(targetProfile, activeProfiles);

    if (mounted) {
      setState(() {
        _generatedScript = script;
        _screenState = SyncScreenState.showingScript;
      });
    }
  }

  Future<void> _installScript() async {
    if (_selectedExpirationProfile == null) return;

    if (mounted) {
      setState(() {
        _isProcessing = true;
        _screenState = SyncScreenState.installing;
        _statusMessage = 'جاري تثبيت السكربت في المايكروتك...';
      });
    }

    try {
      final api = MikrotikSyncService(widget.server);
      final targetProfile = _selectedExpirationProfile!.name;
      final activeProfiles = _fetchedProfiles
          .where((p) => p.name != targetProfile)
          .map((p) => p.name)
          .toList();

      await api.createOrUpdateExpirationScheduler(
        targetProfile: targetProfile,
        activeProfiles: activeProfiles,
      );

      if (mounted) {
        setState(() {
          _statusMessage = 'تم تثبيت وتحديث سكربت المؤقت بنجاح!';
          _screenState = SyncScreenState.done;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _statusMessage = 'فشل تثبيت السكربت: $e';
          _screenState = SyncScreenState.done;
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مزامنة وإنشاء سكربت'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildBody() {
    switch (_screenState) {
      case SyncScreenState.syncing:
        return _buildSyncingView();
      case SyncScreenState.selectingProfile:
        return _buildProfileSelector();
      case SyncScreenState.showingScript:
        return _buildScriptView();
      case SyncScreenState.installing:
      case SyncScreenState.done:
        return _buildStatusView();
    }
  }

  Widget _buildSyncingView() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [ 
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [Text(_overallStatus), Text('${(_overallProgress * 100).toInt()}%')]),
              const SizedBox(height: 8),
              LinearProgressIndicator(value: _overallProgress, valueColor: AlwaysStoppedAnimation<Color>(Colors.orange)),
              const SizedBox(height: 8),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [Text('الباقات: $_processedPackages/$_totalPackages'), Text('المشتركين: $_processedSubscribers/$_totalSubscribers')]),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            itemCount: _operations.length,
            itemBuilder: (context, index) {
              final op = _operations[index];
              return Card(
                margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [Row(children: [Icon(op.icon, color: op.color), const SizedBox(width: 8), Expanded(child: Text(op.message)), Text(op.statusText, style: TextStyle(color: op.color))]),
                      if (op.details != null) Text(op.details!, style: TextStyle(color: Colors.grey[600])),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProfileSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('الخطوة 2: اختر باقة انتهاء الصلاحية', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        ),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0),
          child: Text('سيتم نقل المشتركين المنتهية صلاحيتهم إلى هذه الباقة.'),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: _fetchedProfiles.length,
            itemBuilder: (context, index) {
              final profile = _fetchedProfiles[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: RadioListTile<PackageModel>(
                  title: Text(profile.name),
                  value: profile,
                  groupValue: _selectedExpirationProfile,
                  onChanged: (PackageModel? value) {
                    if (mounted) setState(() => _selectedExpirationProfile = value);
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildScriptView() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(8.0),
            child: Text('الخطوة 3: نسخ أو تثبيت السكربت', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          ),
          Expanded(
            child: Card(
              elevation: 2,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12.0),
                child: SingleChildScrollView(
                  child: SelectableText(
                    _generatedScript,
                    style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusView() {
    bool hasError = _statusMessage.contains('فشل');
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_isProcessing)
              const CircularProgressIndicator(),
            if (!_isProcessing)
              Icon(
                hasError ? Icons.error_outline : Icons.check_circle_outline,
                color: hasError ? Colors.red : Colors.green,
                size: 80,
              ),
            const SizedBox(height: 24),
            Text(
              _statusMessage,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 18),
            ),
          ],
        ),
      ),
    );
  }

  Widget? _buildBottomBar() {
    switch (_screenState) {
      case SyncScreenState.selectingProfile:
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: ElevatedButton.icon(
            icon: const Icon(Icons.code),
            label: const Text('إنشاء السكربت'),
            style: ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 16)),
            onPressed: _selectedExpirationProfile == null ? null : _generateScript,
          ),
        );
      case SyncScreenState.showingScript:
        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ElevatedButton.icon(
                icon: const Icon(Icons.copy_all),
                label: const Text('نسخ السكربت'),
                style: ElevatedButton.styleFrom(minimumSize: const Size(double.infinity, 48)),
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: _generatedScript));
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم نسخ السكربت إلى الحافظة!')),
                  );
                },
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                icon: const Icon(Icons.schedule_send),
                label: const Text('تثبيت السكربت في المايكروتك'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 48),
                ),
                onPressed: _installScript,
              ),
            ],
          ),
        );
      case SyncScreenState.done:
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: ElevatedButton(
            child: const Text('إغلاق'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        );
      default:
        return null;
    }
  }
}