import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/mikrotik_user_model.dart';
import '../models/sas_server_model.dart';
import '../services/mikrotik_sync_service.dart';
import '../models/sync_operation_model.dart';
import '../models/subscriber_model.dart';
import '../models/package_model.dart';
import '../services/database_service.dart';
import 'package:uuid/uuid.dart';

class MikrotikSyncProgressPage extends StatefulWidget {
  final SasServerModel server;

  const MikrotikSyncProgressPage({super.key, required this.server});

  @override
  State<MikrotikSyncProgressPage> createState() => _MikrotikSyncProgressPageState();
}

class _MikrotikSyncProgressPageState extends State<MikrotikSyncProgressPage> {
  final List<SyncOperation> _operations = [];
  final ScrollController _scrollController = ScrollController();
  final _uuid = Uuid();
  bool _isSyncing = false;
  String _overallStatus = 'بدء المزامنة...';
  double _overallProgress = 0.0;
  int _totalSubscribers = 0;
  int _totalPackages = 0;
  int _processedSubscribers = 0;
  int _processedPackages = 0;

  // متغيرات لإدارة اختيار باقة انتهاء الاشتراك
  bool _showExpirationSetup = false;
  List<PackageModel> _syncedPackages = [];
  PackageModel? _selectedExpirationPackage;
  String? _generatedScript;

  // متغيرات لإدارة نوع المزامنة
  bool _showSyncTypeSelection = true;
  bool _isReversSync = false; // false = من MikroTik إلى التطبيق, true = من التطبيق إلى MikroTik

  @override
  void initState() {
    super.initState();
    // لا نبدأ المزامنة تلقائياً، بل ننتظر اختيار المستخدم لنوع المزامنة
  }

  void _addOperation(SyncOperation operation) {
    setState(() {
      _operations.add(operation);
    });
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _startSync() async {
    setState(() {
      _isSyncing = true;
      _operations.clear();
      _overallProgress = 0.0;
      _totalSubscribers = 0;
      _totalPackages = 0;
      _processedSubscribers = 0;
      _processedPackages = 0;
      _showSyncTypeSelection = false;
    });

    // Use runZonedGuarded to catch unhandled async errors from the socket stream
    runZonedGuarded(() async {
      setState(() {
        _isSyncing = true;
        _operations.clear();
        _overallProgress = 0.0;
        _totalSubscribers = 0;
        _totalPackages = 0;
        _processedSubscribers = 0;
        _processedPackages = 0;
      });

      try {
        _addOperation(SyncOperation.info(
          message: 'بدء المزامنة مع خادم MikroTik',
          details: widget.server.name,
        ));

        final api = MikrotikSyncService(widget.server);
        final db = DatabaseService();

        // Connection is now handled per API call within MikrotikSyncService
        // No explicit connect() call needed here.
        _addOperation(SyncOperation.connection(message: 'جاري الاتصال بخادم MikroTik...', status: SyncStatus.running));
        // The actual connection success/failure will be reflected by the subsequent fetch operations.
        // For now, we assume it will connect successfully or throw an error during fetch.
        _addOperation(SyncOperation.connection(message: 'تم الاتصال بخادم MikroTik بنجاح (ضمنياً)', status: SyncStatus.completed));

        if (_isReversSync) {
          // المزامنة العكسية: من التطبيق إلى MikroTik
          await _reverseSyncPackages(api, db);
          await _reverseSyncSubscribers(api, db);
        } else {
          // المزامنة العادية: من MikroTik إلى التطبيق
          await _syncPackages(api, db);
          await _syncSubscribers(api, db);
        }

        final syncTypeText = _isReversSync ? 'النقل إلى MikroTik' : 'الجلب من MikroTik';
        _addOperation(SyncOperation.success(
          message: 'تمت المزامنة بنجاح',
          details: 'تم $syncTypeText: $_processedPackages باقة و $_processedSubscribers مشترك',
        ));

        // إظهار واجهة اختيار باقة انتهاء الاشتراك فقط في المزامنة العادية
        if (!_isReversSync) {
          await _showExpirationPackageSelection();
        }

        setState(() {
          _isSyncing = false;
          _overallStatus = 'تمت المزامنة بنجاح';
          _overallProgress = 1.0;
        });
      } catch (e, stack) {
        _addOperation(SyncOperation.error(message: 'حدث خطأ أثناء المزامنة', details: '$e\n$stack'));
        setState(() {
          _isSyncing = false;
          _overallStatus = 'فشلت المزامنة';
        });

      } catch (e) {
        _addOperation(SyncOperation.error(message: 'حدث خطأ أثناء المزامنة', details: e.toString()));
        setState(() { _isSyncing = false; _overallStatus = 'فشلت المزامنة'; });
      }
    }, (error, stack) {
      _addOperation(SyncOperation.error(message: 'حدث خطأ غير متوقع أثناء المزامنة', details: '$error\n$stack'));
      _addOperation(SyncOperation.error(message: 'حدث خطأ فادح وغير متوقع', details: error.toString()));
      setState(() {
        _isSyncing = false;
        _overallStatus = 'فشلت المزامنة';
      });
    });
  }

  Future<void> _syncPackages(MikrotikSyncService api, DatabaseService db) async {
    _addOperation(SyncOperation.fetchPackages(message: 'جلب البروفايلات من MikroTik', status: SyncStatus.running));
    final remotePackages = await api.fetchPppProfiles();
    _totalPackages = remotePackages.length;
    _addOperation(SyncOperation.fetchPackages(message: 'تم جلب $_totalPackages بروفايل', status: SyncStatus.completed));

    final localPackages = await db.getPackagesFire();
    _syncedPackages.clear(); // مسح القائمة السابقة

    for (final package in remotePackages) {
      final existingPackage = localPackages.firstWhere(
        (p) => p.mikrotikProfileName == package.name,
        orElse: () => PackageModel(id: '', name: '', price: 0, durationInDays: 0, speed: '', deviceCount: 0, adminId: '', serverId: '', createdAt: DateTime.now()),
      );

      if (existingPackage.id.isNotEmpty) {
        // Update existing package if needed
        final updated = existingPackage.copyWith(adminId: db.adminId, price: package.price, speed: package.speed);
        await db.updatePackage(updated);
        _syncedPackages.add(updated); // إضافة للقائمة المزامنة
        _addOperation(SyncOperation.savePackage(packageName: 'تم تحديث: ${package.name}', status: SyncStatus.completed));
      } else {
        // Add new package
        final newPackage = package.copyWith(adminId: db.adminId, id: db.generateId(), mikrotikProfileName: package.name);
        await db.addPackage(newPackage);
        _syncedPackages.add(newPackage); // إضافة للقائمة المزامنة
        _addOperation(SyncOperation.savePackage(packageName: 'تم إضافة: ${package.name}', status: SyncStatus.completed));
      }
      _processedPackages++;
      _updateOverallProgress();
    }
  }

  Future<void> _syncSubscribers(MikrotikSyncService api, DatabaseService db) async {
    _addOperation(SyncOperation.fetchSubscribers(message: 'جلب المشتركين (PPP Secrets) من MikroTik', status: SyncStatus.running));
    print('[SyncPage] Fetching PPP secrets for server ID: ${widget.server.id}');
    final remoteUsers = await api.fetchPppSecrets();
    _totalSubscribers = remoteUsers.length;
    print('[SyncPage] Found ${_totalSubscribers} remote users.');
    _addOperation(SyncOperation.fetchSubscribers(message: 'تم جلب $_totalSubscribers مشترك', status: SyncStatus.completed));

    final localPackages = await db.getPackagesFire();
    final localSubscribers = await db.getSubscribersFire();

    print('[SyncPage] Local Subscribers in DB:');
    for (final sub in localSubscribers) {
      print('[SyncPage]   - Username: ${sub.username}, ServerID: ${sub.sasServerId}, ID: ${sub.id}');
    }

    for (final user in remoteUsers) {
      try {
        print('[SyncPage] Processing user: ${user.name}');
        final matchingPackage = localPackages.firstWhere(
          (p) => p.mikrotikProfileName == user.profile,
          orElse: () => PackageModel(id: '', name: 'باقة غير معروفة', price: 0, durationInDays: 30, speed: '', deviceCount: 1, adminId: db.adminId, serverId: widget.server.id ?? '', createdAt: DateTime.now()),
        );

        if (matchingPackage.id.isEmpty) {
          print('[SyncPage] No matching package for profile: ${user.profile}');
          _addOperation(SyncOperation.saveSubscriber(subscriberName: user.name, status: SyncStatus.failed, details: 'لم يتم العثور على باقة مطابقة للبروفايل: ${user.profile}'));
          continue;
        }

        final existingSubscriber = localSubscribers.firstWhere(
          (s) => s.username == user.name, // Match only by username for MikroTik
          orElse: () => SubscriberModel(id: '', fullName: '', phoneNumber: '', packageId: '', packageName: '', address: '', paymentStatus: PaymentStatus.pending, subscriptionStart: DateTime.now(), subscriptionEnd: DateTime.now(), macAddress: '', routerName: '', technicalNotes: '', debtAmount: 0, createdAt: DateTime.now(), isActive: false, subscriptionType: SubscriptionType.broadband, username: '', password: '', adminId: db.adminId),
        );

        // استخراج البيانات من التعليق
        final subscriberName = user.subscriberName ?? user.name; // اسم المشترك من التعليق أو اسم المستخدم
        final phoneNumber = user.phoneNumber ?? existingSubscriber.phoneNumber; // رقم الهاتف من التعليق أو الموجود

        if (existingSubscriber.id.isNotEmpty) {
          print('[SyncPage] Found existing subscriber: ${existingSubscriber.username} (ID: ${existingSubscriber.id}, Current ServerID: ${widget.server.id}, DB ServerID: ${existingSubscriber.sasServerId})');
          // Check if sasServerId needs updating
          if (existingSubscriber.sasServerId != widget.server.id) {
            print('[SyncPage] Updating sasServerId for ${user.name} from ${existingSubscriber.sasServerId} to ${widget.server.id}');
            // Update the existing subscriber's sasServerId
            final updated = existingSubscriber.copyWith(
              sasServerId: widget.server.id,
              adminId: db.adminId,
              fullName: subscriberName,
              phoneNumber: phoneNumber,
              packageId: matchingPackage.id,
              packageName: matchingPackage.name,
              subscriptionEnd: user.expirationDate ?? existingSubscriber.subscriptionEnd,
              isActive: !user.isExpired,
            );
            await db.updateSubscriber(updated, isSyncUpdate: true);
            _addOperation(SyncOperation.saveSubscriber(subscriberName: 'تم تحديث: ${subscriberName} (تحديث معرف الخادم)', status: SyncStatus.completed));
          } else {
            // If sasServerId already matches, just update other fields
            final updated = existingSubscriber.copyWith(
              adminId: db.adminId,
              fullName: subscriberName,
              phoneNumber: phoneNumber,
              packageId: matchingPackage.id,
              packageName: matchingPackage.name,
              subscriptionEnd: user.expirationDate ?? existingSubscriber.subscriptionEnd,
              isActive: !user.isExpired,
            );
            await db.updateSubscriber(updated, isSyncUpdate: true);
            _addOperation(SyncOperation.saveSubscriber(subscriberName: 'تم تحديث: $subscriberName', status: SyncStatus.completed));
          }
        } else {
          print('[SyncPage] No existing subscriber found for ${user.name}. Adding new.');
          // Add new subscriber
          final newSubscriber = SubscriberModel(
            id: _uuid.v4(),
            adminId: db.adminId,
            fullName: subscriberName,
            username: user.name,
            phoneNumber: phoneNumber.isNotEmpty ? phoneNumber : 'غير محدد', // رقم الهاتف من التعليق
            packageId: matchingPackage.id,
            packageName: matchingPackage.name,
            address: 'غير محدد',
            paymentStatus: PaymentStatus.pending,
            subscriptionStart: DateTime.now(), // Or infer from somewhere else if possible
            subscriptionEnd: user.expirationDate ?? DateTime.now().add(Duration(days: 30)),
            isActive: !user.isExpired,
            technicalNotes: 'تمت المزامنة من MikroTik: ${widget.server.name}',
            sasServerId: widget.server.id, // Re-using this field for server ID
            createdAt: DateTime.now(),
            debtAmount: 0,
            macAddress: '',
            password: '',
            routerName: '',
            subscriptionType: SubscriptionType.broadband,
          );
          await db.addSubscriber(newSubscriber, isSyncUpdate: true);
          _addOperation(SyncOperation.saveSubscriber(subscriberName: 'تم إضافة: $subscriberName', status: SyncStatus.completed));
        }
        _processedSubscribers++;
        _updateOverallProgress();
      } catch (e) {
        print('[SyncPage] ERROR processing user ${user.name}: $e');
        _addOperation(SyncOperation.saveSubscriber(
          subscriberName: user.name,
          status: SyncStatus.failed,
          details: 'فشل في معالجة المشترك: $e',
        ));
      }
    }
  }

  void _updateOverallProgress() {
    setState(() {
      final totalItems = _totalPackages + _totalSubscribers;
      final processedItems = _processedPackages + _processedSubscribers;
      _overallProgress = totalItems > 0 ? processedItems / totalItems : 0;
    });
  }

  // المزامنة العكسية للباقات: من التطبيق إلى MikroTik
  Future<void> _reverseSyncPackages(MikrotikSyncService api, DatabaseService db) async {
    _addOperation(SyncOperation.fetchPackages(message: 'جلب الباقات من التطبيق', status: SyncStatus.running));
    final localPackages = await db.getPackagesFire();
    _totalPackages = localPackages.length;
    _addOperation(SyncOperation.fetchPackages(message: 'تم جلب $_totalPackages باقة من التطبيق', status: SyncStatus.completed));

    _addOperation(SyncOperation.info(message: 'جلب البروفايلات الموجودة في MikroTik', details: 'للتحقق من التطابق'));
    final remotePackages = await api.fetchPppProfiles();
    final existingProfiles = remotePackages.map((p) => p.mikrotikProfileName).toSet();

    for (final package in localPackages) {
      try {
        final profileName = package.mikrotikProfileName ?? package.name;

        if (existingProfiles.contains(profileName)) {
          _addOperation(SyncOperation.savePackage(
            packageName: 'موجود مسبقاً: $profileName',
            status: SyncStatus.completed
          ));
        } else {
          // إنشاء بروفايل جديد في MikroTik
          await api.addPppProfile(
            name: profileName,
            rateLimit: package.speed.isNotEmpty ? package.speed : '1M/1M',
          );
          _addOperation(SyncOperation.savePackage(
            packageName: 'تم إنشاء: $profileName',
            status: SyncStatus.completed
          ));
        }

        _syncedPackages.add(package);
        _processedPackages++;
        _updateOverallProgress();
      } catch (e) {
        _addOperation(SyncOperation.savePackage(
          packageName: 'فشل: ${package.name}',
          status: SyncStatus.failed
        ));
        print('[ReversSync] Error creating profile ${package.name}: $e');
      }
    }
  }

  // المزامنة العكسية للمشتركين: من التطبيق إلى MikroTik
  Future<void> _reverseSyncSubscribers(MikrotikSyncService api, DatabaseService db) async {
    _addOperation(SyncOperation.fetchSubscribers(message: 'جلب المشتركين من التطبيق', status: SyncStatus.running));
    final localSubscribers = await db.getSubscribersFire();
    _totalSubscribers = localSubscribers.length;
    _addOperation(SyncOperation.fetchSubscribers(message: 'تم جلب $_totalSubscribers مشترك من التطبيق', status: SyncStatus.completed));

    _addOperation(SyncOperation.info(message: 'جلب المشتركين الموجودين في MikroTik', details: 'للتحقق من التطابق'));
    final remoteUsers = await api.fetchPppSecrets();
    final existingUsers = remoteUsers.map((u) => u.name).toSet();

    for (final subscriber in localSubscribers) {
      try {
        if (subscriber.username.isEmpty) {
          _addOperation(SyncOperation.saveSubscriber(
            subscriberName: 'تم تخطي: ${subscriber.fullName}',
            status: SyncStatus.failed,
            details: 'لا يوجد اسم مستخدم'
          ));
          continue;
        }

        // البحث عن الباقة المطابقة
        final package = _syncedPackages.firstWhere(
          (p) => p.id == subscriber.packageId,
          orElse: () => PackageModel(
            id: '', name: 'default', price: 0, durationInDays: 30,
            speed: '1M/1M', deviceCount: 1, adminId: '', serverId: '',
            createdAt: DateTime.now(), mikrotikProfileName: 'default'
          ),
        );

        final profileName = package.mikrotikProfileName ?? package.name;

        // إنشاء التعليق مع تاريخ انتهاء الاشتراك بصيغة YYYY-MM-DD HH:MM:SS
        final comment = '${subscriber.subscriptionEnd.year.toString().padLeft(4, '0')}-'
                       '${subscriber.subscriptionEnd.month.toString().padLeft(2, '0')}-'
                       '${subscriber.subscriptionEnd.day.toString().padLeft(2, '0')} '
                       '${subscriber.subscriptionEnd.hour.toString().padLeft(2, '0')}:'
                       '${subscriber.subscriptionEnd.minute.toString().padLeft(2, '0')}:'
                       '${subscriber.subscriptionEnd.second.toString().padLeft(2, '0')} | '
                       '${subscriber.fullName} | ${subscriber.phoneNumber}';

        if (existingUsers.contains(subscriber.username)) {
          // تحديث المشترك الموجود
          await api.updatePppSecret(
            username: subscriber.username,
            newProfile: profileName,
            newComment: comment,
          );
          _addOperation(SyncOperation.saveSubscriber(
            subscriberName: 'تم تحديث: ${subscriber.username}',
            status: SyncStatus.completed
          ));
        } else {
          // إنشاء مشترك جديد
          await api.addPppSecret(
            name: subscriber.username,
            password: subscriber.password.isNotEmpty ? subscriber.password : '123456',
            profile: profileName,
            comment: comment,
          );
          _addOperation(SyncOperation.saveSubscriber(
            subscriberName: 'تم إنشاء: ${subscriber.username}',
            status: SyncStatus.completed
          ));
        }

        _processedSubscribers++;
        _updateOverallProgress();
      } catch (e) {
        _addOperation(SyncOperation.saveSubscriber(
          subscriberName: 'فشل: ${subscriber.fullName}',
          status: SyncStatus.failed,
          details: e.toString()
        ));
        print('[ReversSync] Error processing subscriber ${subscriber.username}: $e');
      }
    }
  }

  // عرض واجهة اختيار باقة انتهاء الاشتراك
  Future<void> _showExpirationPackageSelection() async {
    if (_syncedPackages.isEmpty) {
      _addOperation(SyncOperation.info(
        message: 'لا توجد باقات متاحة لإعداد انتهاء الاشتراك',
        details: 'لم يتم العثور على باقات مزامنة',
      ));
      return;
    }

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('إعداد باقة انتهاء الاشتراك'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('اختر الباقة التي سيتم نقل المشتركين المنتهيين إليها:'),
            const SizedBox(height: 16),
            DropdownButtonFormField<PackageModel>(
              value: _selectedExpirationPackage,
              hint: const Text('اختر باقة انتهاء الاشتراك'),
              items: _syncedPackages.map((package) {
                return DropdownMenuItem<PackageModel>(
                  value: package,
                  child: Text('${package.name} (${package.mikrotikProfileName ?? 'غير محدد'})'),
                );
              }).toList(),
              onChanged: (PackageModel? value) {
                setState(() {
                  _selectedExpirationPackage = value;
                });
              },
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                labelText: 'باقة انتهاء الاشتراك',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _addOperation(SyncOperation.info(
                message: 'تم تخطي إعداد باقة انتهاء الاشتراك',
              ));
            },
            child: const Text('تخطي'),
          ),
          ElevatedButton(
            onPressed: _selectedExpirationPackage != null
                ? () {
                    Navigator.of(context).pop();
                    _generateAndShowScript();
                  }
                : null,
            child: const Text('إنشاء السكربت'),
          ),
        ],
      ),
    );
  }

  // إنشاء وعرض السكربت
  void _generateAndShowScript() {
    if (_selectedExpirationPackage == null) return;

    final targetProfile = _selectedExpirationPackage!.mikrotikProfileName ?? _selectedExpirationPackage!.name;
    final api = MikrotikSyncService(widget.server);

    // الحصول على أسماء جميع الباقات المزامنة كباقات نشطة
    final activeProfiles = _syncedPackages
        .where((p) => p.mikrotikProfileName != null && p.mikrotikProfileName!.isNotEmpty)
        .map((p) => p.mikrotikProfileName!)
        .toList();

    // إضافة "default" إذا لم تكن موجودة
    if (!activeProfiles.contains('default')) {
      activeProfiles.add('default');
    }

    // إنشاء السكربت
    _generatedScript = api.generateExpirationScript(targetProfile, activeProfiles);

    _addOperation(SyncOperation.success(
      message: 'تم إنشاء سكربت انتهاء الاشتراك',
      details: 'الباقة المختارة: ${_selectedExpirationPackage!.name}\nالباقات النشطة: ${activeProfiles.join(", ")}',
    ));

    setState(() {
      _showExpirationSetup = true;
    });
  }

  // نسخ السكربت إلى الحافظة
  void _copyScriptToClipboard() {
    if (_generatedScript != null) {
      Clipboard.setData(ClipboardData(text: _generatedScript!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ السكربت إلى الحافظة'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  // تثبيت السكربت مباشرة في MikroTik
  Future<void> _installScriptDirectly() async {
    if (_selectedExpirationPackage == null || _generatedScript == null) return;

    try {
      _addOperation(SyncOperation.info(
        message: 'جاري تثبيت السكربت في MikroTik...',
      ));

      final api = MikrotikSyncService(widget.server);
      final targetProfile = _selectedExpirationPackage!.mikrotikProfileName ?? _selectedExpirationPackage!.name;

      await api.createOrUpdateExpirationScheduler(
        targetProfile: targetProfile,
        activeProfiles: ['default'],
      );

      _addOperation(SyncOperation.success(
        message: 'تم تثبيت السكربت في MikroTik بنجاح',
        details: 'سيتم تشغيل السكربت كل 5 دقائق تلقائياً',
      ));

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تثبيت سكربت انتهاء الاشتراك بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      _addOperation(SyncOperation.error(
        message: 'فشل في تثبيت السكربت',
        details: e.toString(),
      ));

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في تثبيت السكربت: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // بناء واجهة اختيار نوع المزامنة
  Widget _buildSyncTypeSelection() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مزامنة MikroTik'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.sync_alt,
                size: 80,
                color: Colors.orange.shade600,
              ),
              const SizedBox(height: 24),
              Text(
                'اختر نوع المزامنة',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange.shade700,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'حدد اتجاه نقل البيانات',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 32),
              Card(
                elevation: 4,
                child: Column(
                  children: [
                    ListTile(
                      leading: Radio<bool>(
                        value: false,
                        groupValue: _isReversSync,
                        onChanged: (value) => setState(() => _isReversSync = value!),
                        activeColor: Colors.orange,
                      ),
                      title: const Text(
                        'من MikroTik إلى التطبيق',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: const Text('جلب البيانات من جهاز MikroTik وحفظها في التطبيق'),
                      onTap: () => setState(() => _isReversSync = false),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: Radio<bool>(
                        value: true,
                        groupValue: _isReversSync,
                        onChanged: (value) => setState(() => _isReversSync = value!),
                        activeColor: Colors.orange,
                      ),
                      title: const Text(
                        'من التطبيق إلى MikroTik',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: const Text('نقل البيانات من التطبيق إلى جهاز MikroTik'),
                      onTap: () => setState(() => _isReversSync = true),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _startSync,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    _isReversSync ? 'بدء النقل إلى MikroTik' : 'بدء الجلب من MikroTik',
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // إذا كان في مرحلة اختيار نوع المزامنة، عرض واجهة الاختيار
    if (_showSyncTypeSelection) {
      return _buildSyncTypeSelection();
    }

    // إذا بدأت المزامنة، عرض واجهة التقدم
    return Scaffold(
      appBar: AppBar(
        title: const Text('مزامنة MikroTik'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [Text(_overallStatus), Text('${(_overallProgress * 100).toInt()}%')]),
                const SizedBox(height: 8),
                LinearProgressIndicator(value: _overallProgress, valueColor: AlwaysStoppedAnimation<Color>(Colors.orange)),
                const SizedBox(height: 8),
                Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [Text('الباقات: $_processedPackages/$_totalPackages'), Text('المشتركين: $_processedSubscribers/$_totalSubscribers')]),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              itemCount: _operations.length,
              itemBuilder: (context, index) {
                final op = _operations[index];
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(children: [Icon(op.icon, color: op.color), const SizedBox(width: 8), Expanded(child: Text(op.message)), Text(op.statusText, style: TextStyle(color: op.color))]),
                        if (op.details != null) Text(op.details!, style: TextStyle(color: Colors.grey[600])),
                        const SizedBox(height: 4),
                        Text(op.timestamp.toIso8601String(), style: TextStyle(color: Colors.grey[500], fontSize: 10)),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          // إظهار خيارات السكربت إذا تم إنشاؤه
          if (_showExpirationSetup && _generatedScript != null) ...[
            Container(
              margin: const EdgeInsets.all(16.0),
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                border: Border.all(color: Colors.orange.shade200),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.schedule, color: Colors.orange.shade700),
                      const SizedBox(width: 8),
                      Text(
                        'سكربت انتهاء الاشتراك',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'الباقة المختارة: ${_selectedExpirationPackage?.name}',
                    style: TextStyle(color: Colors.grey.shade700),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _copyScriptToClipboard,
                          icon: const Icon(Icons.copy),
                          label: const Text('نسخ السكربت'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _installScriptDirectly,
                          icon: const Icon(Icons.install_desktop),
                          label: const Text('تثبيت مباشر'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  ExpansionTile(
                    title: const Text('عرض السكربت'),
                    children: [
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: SelectableText(
                          _generatedScript!,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ElevatedButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إغلاق')),
          ),
        ],
      ),
    );
  }
}
