import 'package:flutter/material.dart';
import '../models/network_device.dart';
import '../services/network_device_service.dart';

class DeviceInfoPage extends StatefulWidget {
  final NetworkDevice device;

  const DeviceInfoPage({super.key, required this.device});

  @override
  State<DeviceInfoPage> createState() => _DeviceInfoPageState();
}

class _DeviceInfoPageState extends State<DeviceInfoPage> {
  final NetworkDeviceService _deviceService = NetworkDeviceService();
  Map<String, dynamic>? _deviceInfo;
  bool _isLoading = true;
  bool _isRebooting = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadDeviceInfo();
  }
  Future<void> _loadDeviceInfo() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final info = await _deviceService.getDeviceInfo(widget.device);
      setState(() {
        _deviceInfo = info;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _rebootDevice() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد إعادة التشغيل'),
        content: Text('هل تريد إعادة تشغيل الجهاز "${widget.device.name}"؟\n\nسيستغرق الجهاز بضع دقائق ليعود للعمل.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('إعادة تشغيل'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isRebooting = true);

      try {
        await _deviceService.rebootDevice(widget.device);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال أمر إعادة التشغيل بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          // انتظار قليل ثم العودة للصفحة السابقة
          await Future.delayed(const Duration(seconds: 2));
          if (mounted) {
            Navigator.pop(context);
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إعادة تشغيل الجهاز: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isRebooting = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(      appBar: AppBar(
        title: Text('معلومات ${widget.device.name}'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _isRebooting ? null : _loadDeviceInfo,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث المعلومات',
          ),
          IconButton(
            onPressed: _isRebooting ? null : _rebootDevice,
            icon: _isRebooting 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.restart_alt),
            tooltip: 'إعادة تشغيل الجهاز',
          ),
        ],
      ),
      body: Column(
        children: [
          // Device header
          Container(
            width: double.infinity,
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _getDeviceTypeColor(widget.device.type).withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getDeviceTypeIcon(widget.device.type),
                    size: 40,
                    color: _getDeviceTypeColor(widget.device.type),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  widget.device.name,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.device.ipAddress,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getDeviceTypeColor(widget.device.type),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    widget.device.type,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Device info content
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جارٍ تحميل معلومات الجهاز...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 60,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            const Text(
              'خطأ في تحميل معلومات الجهاز',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton.icon(
              onPressed: _loadDeviceInfo,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadDeviceInfo,
      child: ListView(
        padding: const EdgeInsets.all(16),        children: [
          _buildInfoSection('معلومات الاتصال', [
            _buildInfoCard(
              'قوة الإشارة',
              '${_deviceInfo!['signalStrength']}',
              Icons.signal_cellular_alt,
              Colors.green,
            ),
            _buildInfoCard(
              'الضوضاء',
              '${_deviceInfo!['noise']}',
              Icons.graphic_eq,
              Colors.orange,
            ),
            _buildInfoCard(
              'نسبة الإشارة للضوضاء',
              '${_deviceInfo!['snr']}',
              Icons.bar_chart,
              Colors.blue,
            ),
          ]),
          const SizedBox(height: 20),
          _buildInfoSection('معدلات النقل', [
            _buildInfoCard(
              'معدل الإرسال',
              '${_deviceInfo!['txRate']}',
              Icons.upload,
              Colors.purple,
            ),
            _buildInfoCard(
              'معدل الاستقبال',
              '${_deviceInfo!['rxRate']}',
              Icons.download,
              Colors.teal,
            ),
          ]),
          const SizedBox(height: 20),          _buildInfoSection('معلومات عامة', [            _buildUptimeCard(
              'مدة التشغيل',
              _formatUptime('${_deviceInfo!['uptime']}'),
              Icons.schedule,
              Colors.indigo,
            ),
            _buildInfoCard(
              'عنوان MAC',
              '${_deviceInfo!['macAddress']}',
              Icons.computer,
              Colors.brown,
            ),
            _buildInfoCard(
              'إصدار البرنامج',
              '${_deviceInfo!['firmwareVersion']}',
              Icons.system_update,
              Colors.cyan,
            ),
          ]),
          const SizedBox(height: 20),
          _buildInfoSection('معلومات متقدمة', [
            _buildInfoCard(
              'نموذج الجهاز',
              '${_deviceInfo!['deviceModel'] ?? 'غير متوفر'}',
              Icons.devices,
              Colors.purple,
            ),
            _buildInfoCard(
              'اسم الجهاز',
              '${_deviceInfo!['deviceName'] ?? 'غير متوفر'}',
              Icons.device_hub,
              Colors.indigo,
            ),
            _buildInfoCard(
              'وضع الشبكة',
              '${_deviceInfo!['networkMode'] ?? 'غير متوفر'}',
              Icons.network_wifi,
              Colors.blue,
            ),
            _buildInfoCard(
              'الوضع اللاسلكي',
              '${_deviceInfo!['wirelessMode'] ?? 'غير متوفر'}',
              Icons.wifi_tethering,
              Colors.green,
            ),
            _buildInfoCard(
              'SSID',
              '${_deviceInfo!['ssid'] ?? 'غير متوفر'}',
              Icons.wifi_outlined,
              Colors.orange,
            ),            _buildInfoCard(
              'سرعة LAN',
              '${_deviceInfo!['lanSpeed'] ?? 'غير متوفر'}',
              Icons.cable,
              Colors.teal,
            ),
          ]),
          const SizedBox(height: 30),
          // زر إعادة التشغيل
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ElevatedButton.icon(
              onPressed: _isRebooting ? null : _rebootDevice,
              icon: _isRebooting 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Icon(Icons.restart_alt),
              label: Text(_isRebooting ? 'جارٍ إعادة التشغيل...' : 'إعادة تشغيل الجهاز'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> cards) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...cards,
      ],
    );
  }
  Widget _buildInfoCard(String label, String value, IconData icon, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUptimeCard(String label, String value, IconData icon, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        label,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.green.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'متصل',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (value.contains('أيام') && value != 'غير متوفر')
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        _getUptimeStatus(value),
                        style: TextStyle(
                          fontSize: 12,
                          color: _getUptimeStatusColor(value),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getDeviceTypeColor(String type) {
    switch (type) {
      case 'UBNT':
        return Colors.blue;
      case 'MIMOSA':
        return Colors.green;
      case 'TPLINK':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
  IconData _getDeviceTypeIcon(String type) {
    switch (type) {
      case 'UBNT':
        return Icons.wifi;
      case 'MIMOSA':
        return Icons.router;
      case 'TPLINK':
        return Icons.device_hub;
      default:
        return Icons.router_outlined;
    }
  }
  String _formatUptime(String uptime) {
    // إذا كانت مدة التشغيل تحتوي على كلمات عربية، فهي منسقة بالفعل
    if (uptime.contains('أيام') || uptime.contains('ساعات') || uptime.contains('دقائق')) {
      return uptime;
    }

    // محاولة تنسيق إضافي للحالات المختلفة
    try {
      // إزالة النصوص غير المرغوب فيها
      String cleanUptime = uptime
          .replaceAll(RegExp(r',\s*\d+\s*users?.*'), '')
          .replaceAll(RegExp(r'load average.*'), '')
          .trim();

      // إذا كان النص قصير جداً، ربما يكون عدد ثوان
      if (RegExp(r'^\d+$').hasMatch(cleanUptime)) {
        int seconds = int.parse(cleanUptime);
        if (seconds < 60) {
          return '$seconds ثانية';
        } else if (seconds < 3600) {
          int minutes = (seconds / 60).floor();
          int remainingSecs = seconds % 60;
          return '$minutes دقائق${remainingSecs > 0 ? '، $remainingSecs ثانية' : ''}';
        } else if (seconds < 86400) {
          int hours = (seconds / 3600).floor();
          int remainingMins = ((seconds % 3600) / 60).floor();
          return '$hours ساعات${remainingMins > 0 ? '، $remainingMins دقائق' : ''}';
        } else {
          int days = (seconds / 86400).floor();
          int remainingHours = ((seconds % 86400) / 3600).floor();
          return '$days أيام${remainingHours > 0 ? '، $remainingHours ساعات' : ''}';
        }
      }

      return cleanUptime.isEmpty ? 'غير متوفر' : cleanUptime;
    } catch (e) {
      return uptime.isEmpty ? 'غير متوفر' : uptime;
    }
  }

  String _getUptimeStatus(String uptime) {
    if (uptime.contains('أيام')) {
      final daysMatch = RegExp(r'(\d+)\s*أيام').firstMatch(uptime);
      if (daysMatch != null) {
        int days = int.parse(daysMatch.group(1)!);
        if (days > 30) {
          return 'استقرار ممتاز';
        } else if (days > 7) {
          return 'استقرار جيد';
        } else {
          return 'حديث التشغيل';
        }
      }
    } else if (uptime.contains('ساعات')) {
      return 'حديث التشغيل';
    } else if (uptime.contains('دقائق') || uptime.contains('ثانية')) {
      return 'تم التشغيل حديثاً';
    }
    return '';
  }

  Color _getUptimeStatusColor(String uptime) {
    if (uptime.contains('أيام')) {
      final daysMatch = RegExp(r'(\d+)\s*أيام').firstMatch(uptime);
      if (daysMatch != null) {
        int days = int.parse(daysMatch.group(1)!);
        if (days > 30) {
          return Colors.green;
        } else if (days > 7) {
          return Colors.blue;
        } else {
          return Colors.orange;
        }
      }
    } else if (uptime.contains('ساعات')) {
      return Colors.orange;
    } else if (uptime.contains('دقائق') || uptime.contains('ثانية')) {
      return Colors.red;
    }
    return Colors.grey;
  }
}
