class ExpenseModel {
  String adminId;
  final String id;
  final String categoryId;
  final double amount;
  final String notes;
  final DateTime timestamp;

  ExpenseModel({
    required this.id,
    required this.categoryId,
    required this.amount,
    this.notes = '',
    required this.timestamp,
    required this.adminId,
  });

  // Firebase compatibility methods
  Map<String, dynamic> toMap() => {
    'id': id,
    'categoryId': categoryId,
    'amount': amount,
    'notes': notes,
    'timestamp': timestamp.toIso8601String(),
    "adminId":adminId,
  };

  factory ExpenseModel.fromMap(Map<String, dynamic> map) => ExpenseModel(adminId: map['adminId'],
    id: map['id'],
    categoryId: map['categoryId'],
    amount: map['amount']?.toDouble() ?? 0.0,
    notes: map['notes'] ?? '',
    timestamp: DateTime.parse(map['timestamp']),
  );

  // Legacy compatibility methods
  Map<String, dynamic> toJson() => toMap();

  factory ExpenseModel.fromJson(Map<String, dynamic> json) =>
      ExpenseModel.fromMap(json);

  ExpenseModel copyWith({
    String? id,
    String? categoryId,
    double? amount,
    String? notes,
    DateTime? timestamp,
    required String adminId,
  }) {
    return ExpenseModel(adminId: adminId,
      id: id ?? this.id,
      categoryId: categoryId ?? this.categoryId,
      amount: amount ?? this.amount,
      notes: notes ?? this.notes,
      timestamp: timestamp ?? this.timestamp,
    );
  }
}
