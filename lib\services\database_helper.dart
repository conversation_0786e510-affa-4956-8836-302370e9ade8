import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/network_device.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  factory DatabaseHelper() => _instance;

  DatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'network_devices.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE network_devices(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        ipAddress TEXT NOT NULL,
        username TEXT NOT NULL,
        password TEXT NOT NULL,
        type TEXT NOT NULL,
        signalStrength TEXT,
        noise TEXT,
        snr TEXT,
        txRate TEXT,
        rxRate TEXT,
        uptime TEXT,
        macAddress TEXT,
        firmwareVersion TEXT
      )
    ''');
  }

  Future<int> insertDevice(NetworkDevice device) async {
    final db = await database;
    return await db.insert('network_devices', device.toJson());
  }

  Future<List<NetworkDevice>> getDevices() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('network_devices');
    return List.generate(maps.length, (i) => NetworkDevice.fromJson(maps[i]));
  }

  Future<int> updateDevice(NetworkDevice device) async {
    final db = await database;
    return await db.update(
      'network_devices',
      device.toJson(),
      where: 'id = ?',
      whereArgs: [device.id],
    );
  }

  Future<int> deleteDevice(String id) async {
    final db = await database;
    return await db.delete(
      'network_devices',
      where: 'id = ?',
      whereArgs: [id],
    );
  }
} 