class PaymentRecordModel {
  String adminId;
  final String id;
  final String subscriberId;
  final double amount;
  final String paymentMethod;
  final String? notes;
  final DateTime paymentDate;
  final String recordedBy;

  PaymentRecordModel({
    required this.id,
    required this.subscriberId,
    required this.amount,
    required this.paymentMethod,
    this.notes,
    required this.paymentDate,
    required this.recordedBy,
    required this.adminId,
  });

  // Firebase compatibility methods
  Map<String, dynamic> toMap() => {
    'id': id,
    'subscriberId': subscriberId,
    'amount': amount,
    'paymentMethod': paymentMethod,
    'notes': notes,
    'paymentDate': paymentDate.toIso8601String(),
    'recordedBy': recordedBy,
    "adminId": adminId,
  };

  factory PaymentRecordModel.fromMap(Map<String, dynamic> map) =>
      PaymentRecordModel(
        adminId: map['adminId'] ?? "",
        id: map['id'],
        subscriberId: map['subscriberId'],
        amount: (map['amount'] is int)
            ? (map['amount'] as int).toDouble()
            : map['amount'],
        paymentMethod: map['paymentMethod'],
        notes: map['notes'],
        paymentDate: map['paymentDate'] is DateTime
            ? map['paymentDate']
            : DateTime.parse(map['paymentDate']),
        recordedBy: map['recordedBy'],
      );

  // Legacy compatibility methods
  Map<String, dynamic> toJson() => toMap();

  factory PaymentRecordModel.fromJson(Map<String, dynamic> json) =>
      PaymentRecordModel.fromMap(json);

  PaymentRecordModel copyWith({
    String? id,
    String? subscriberId,
    double? amount,
    String? paymentMethod,
    String? notes,
    DateTime? paymentDate,
    String? recordedBy,
    required String adminId,
  }) => PaymentRecordModel(
    adminId: adminId,
    id: id ?? this.id,
    subscriberId: subscriberId ?? this.subscriberId,
    amount: amount ?? this.amount,
    paymentMethod: paymentMethod ?? this.paymentMethod,
    notes: notes ?? this.notes,
    paymentDate: paymentDate ?? this.paymentDate,
    recordedBy: recordedBy ?? this.recordedBy,
  );
}
