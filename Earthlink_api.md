# reseller_api

[Source](https://documenter.getpostman.com/view/1718859/resellerapi/RVfsHYij#0eff92bb-bdf6-4800-4058-b4c8d170a9a8 "Permalink to reseller_api")

Reseller API documentation

this API supports OAuth based authentication, for any request, a `authorization` header must be sent with bearer token, to get a token , use the Token endpoint and provide the credentials for the reseller admin

`https://rapi.earthlink.iq/api/reseller/Token`

To use sub-affiliate scope, an`AffiliateIndex` header can be sent with the value of the affiliate index needed, information from most apis will reflect the sent affiliateindex header value. get list of sub-affiliates, use the below api

`https://rapi.earthlink.iq/api/reseller/affiliates`

### POSTcheck user available

https://rapi.earthlink.iq/api/reseller/user/checkuseravailable

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

UserID

..vbcvغغغ

Example Request

check user available

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/user/checkuseravailable'));
      request.bodyFields = {
        'UserID': '..vbcvغغغ'
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

### POSTrename user

https://rapi.earthlink.iq/api/reseller/user/rename

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

NewUserName

xxxx@xxx

UserIndex

7298755

Example Request

rename user

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/user/c'));
      request.bodyFields = {
        'NewUserName': 'xxxx@xxx',
        'UserIndex': '7298755'
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": false,
      "responseMessage": null,
      "error": {
        "message": " Duplicated user name! ",
        "detailMessage": null,
        "errorCode": 0,
        "validationErrors": []
      }
    }

### POSTchange password

https://rapi.earthlink.iq/api/reseller/user/changepassword

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

NewPassword

1111

UserIndex

7298755

Example Request

change password

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/user/changepassword'));
      request.bodyFields = {
        'NewPassword': '1111',
        'UserIndex': '7298755'
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": true,
      "responseMessage": null,
      "error": null
    }

### GETauto complete

https://rapi.earthlink.iq/api/reseller/user/autocomplete?key=محمد

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

PARAMS

key

محمد

Example Request

auto complete

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/user/autocomplete?key=hus'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    [
      {
        "userIndex": 695814,
        "userId": "admin@hus",
        "displayName": "00:27:22:06:fe:79",
        "status": null,
        "avatar": null,
        "initials": "F"
      },
      {
        "userIndex": 764570,
        "userId": "max@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "M"
      },
      {
        "userIndex": 975292,
        "userId": "firas76@hus",
        "displayName": null,
        "status": null,
        "avatar": null,
        "initials": "F"
      },
      {
        "userIndex": 1080485,
        "userId": "eng.ahmed81@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "E"
      },
      {
        "userIndex": 1098363,
        "userId": "hasen@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "H"
      },
      {
        "userIndex": 1278211,
        "userId": "sudan-m1@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "S"
      },
      {
        "userIndex": 1320569,
        "userId": "hussin-ali@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "H"
      },
      {
        "userIndex": 1339683,
        "userId": "hussam@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "H"
      },
      {
        "userIndex": 1676398,
        "userId": "omar.abd@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "O"
      },
      {
        "userIndex": 1700025,
        "userId": "maitham@hus",
        "displayName": null,
        "status": null,
        "avatar": null,
        "initials": "M"
      },
      {
        "userIndex": 1702865,
        "userId": "musta@hus",
        "displayName": null,
        "status": null,
        "avatar": null,
        "initials": "M"
      },
      {
        "userIndex": 1805098,
        "userId": "cryzimusic-max@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "C"
      },
      {
        "userIndex": 1903677,
        "userId": "faroq.zoher@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "F"
      },
      {
        "userIndex": 1929733,
        "userId": "aliabedi-m@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "A"
      },
      {
        "userIndex": 2247486,
        "userId": "mohmed.reyath@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "M"
      },
      {
        "userIndex": 2376519,
        "userId": "jefara@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "J"
      },
      {
        "userIndex": 2439905,
        "userId": "hader.badry@hus",
        "displayName": null,
        "status": null,
        "avatar": null,
        "initials": "H"
      },
      {
        "userIndex": 2529718,
        "userId": "emad.ray-m@hus",
        "displayName": null,
        "status": null,
        "avatar": null,
        "initials": "E"
      },
      {
        "userIndex": 2587506,
        "userId": "alijalal-m@hus",
        "displayName": null,
        "status": null,
        "avatar": null,
        "initials": "A"
      },
      {
        "userIndex": 2608074,
        "userId": "mohamed@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "M"
      },
      {
        "userIndex": 2686904,
        "userId": "saif.mohamed@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "S"
      },
      {
        "userIndex": 2753288,
        "userId": "ahmed.abbas@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "A"
      },
      {
        "userIndex": 2824952,
        "userId": "Jafrdjaili-m@hus",
        "displayName": null,
        "status": null,
        "avatar": null,
        "initials": "J"
      },
      {
        "userIndex": 2858434,
        "userId": "aliahmed-m@hus",
        "displayName": null,
        "status": null,
        "avatar": null,
        "initials": "A"
      },
      {
        "userIndex": 2865059,
        "userId": "wisam@hus",
        "displayName": "",
        "status": null,
        "avatar": null,
        "initials": "W"
      }
    ]

## 

account types

### GETget all accounts

https://rapi.earthlink.iq/api/reseller/accounts/all

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

get all accounts

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/accounts/all'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

### POSTprepaycard validate

https://rapi.earthlink.iq/api/reseller/prepaycard/validate

this is used to know if the card is used or still avtive for this affiliate

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

Identifier

Sp509726526

Pin

2095

Example Request

prepaycard validate fail

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/prepaycard/validate'));
      request.bodyFields = {
        'Identifier': 'Sp509726526',
        'Pin': '2095'
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": false,
      "responseMessage": null,
      "error": {
        "message": "Invalid or used prepay card !",
        "detailMessage": null,
        "errorCode": 0,
        "validationErrors": []
      }
    }

### POSTprepaycard search

https://rapi.earthlink.iq/api/reseller/prepaycard/all

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

StartSerial

********

start serial number

EndSerial

26566983

end serial

UserId

abomen@hus

CardType

to use filter/type api

AffiliateIndex

selected subaffiliate or null

Status

5

to use filter/status api

StartUsedDate

only when status is used we set date range

EndUsedDate

needed with above

Startindex

0

Rowcount

5

OrderBy

to use filter/orderby api

Example Request

prepaycard search

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/prepaycard/all'));
      request.bodyFields = {
        'StartSerial': '********',
        'EndSerial': '26566983',
        'Status': '5',
        'Startindex': '',
        'Rowcount': ''
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "itemsList": [
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        },
        {
          "dateOfUse": "",
          "serialNumber": ********,
          "status": "Active",
          "affiliateName": "hus",
          "userId": null,
          "onlineCost": " $ 0.00 ",
          "cardType": "Perpaid Card",
          "accountName": "Light+"
        }
      ],
      "totalCount": 48
    }

### GETprepaycard/filter/orderby

https://rapi.earthlink.iq/api/reseller/prepaycard/filter/orderby

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

prepaycard/filter/orderby

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.MultipartRequest('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/prepaycard/filter/orderby'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": [
        "Account Type",
        "Card Status",
        "UserId",
        "Date Of Use",
        "AffiliateName",
        "OnlineCost",
        "CardType",
        "SerialNumber"
      ],
      "responseMessage": null,
      "error": null
    }

### POSTcardinfo

https://rapi.earthlink.iq/api/reseller/prepaycard/cardinfo

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

Identifier

*********

Pin

0663

Example Request

cardinfo

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/prepaycard/cardinfo'));
      request.bodyFields = {
        'Identifier': '*********',
        'Pin': '0663'
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

### POSTrefill user prepaid card

https://rapi.earthlink.iq/api/reseller/user/refillprepay

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

Identifier

Lp00372205

Pin

0111

UserId

3amer89@hus

Example Request

refill user prepaid card

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/user/refillprepay'));
      request.bodyFields = {
        'Identifier': 'Lp00372205',
        'Pin': '0111',
        'UserId': '3amer89@hus'
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

### POSTnewuser with prepay

https://rapi.earthlink.iq/api/reseller/user/newuser

here we create new user, only the following are mandatory

PrepayCard PrepayIdentifier UserID UserPass EarthMaxMAC (if the account was max)

P.S we need to use validate prepay card before coming to this step

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

PrepayIdentifier

Ep66697814

PrepayPassword

2705

UserID

newtestuser@hus

UserPass

1

EarthMaxMAC

FirstName

Firstname

LastName

Last

Company

Address

City

State

Country

Zip

Email

MobileNumber

MobileNumber2

DisplayName

محمد علي

Example Request

newuser with prepay

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/user/newuser'));
      request.bodyFields = {
        'PrepayIdentifier': 'Ep66697814',
        'PrepayPassword': '2705',
        'UserID': 'newtestuser@hus',
        'UserPass': '1',
        'EarthMaxMAC': '',
        'FirstName': 'Firstname',
        'LastName': 'Last',
        'Company': '',
        'Address': '',
        'City': '',
        'State': '',
        'Country': '',
        'Zip': '',
        'Email': '',
        'MobileNumber': '',
        'HomePhone': '',
        'WorkPhone': '',
        'ArName': 'محمد علي',
        'EnName': ''
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": 12554216,
      "responseMessage": null,
      "error": null
    }

## 

active sessions

actives session are the online users for this affiliate and its sub affiliates, we have 2 types of online users, 1st is the normal online users, and the 2nd are the max users, we have diffrent views for each one becuase in the max we have more info about the users and their usage than the normal ones

### GETsupport phones

https://rapi.earthlink.iq/api/reseller/support/phones

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

support phones

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/support/phones'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

## 

ticket

### GETGetRates

https://rapi.earthlink.iq/api/reseller/ticket/GetRates

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

GetRates

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.MultipartRequest('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/ticket/GetRates'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

### GETGetAverageRate

https://rapi.earthlink.iq/api/reseller/ticket/GetAverageRate/3016769

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

TicketID

3016769

Example Request

GetAverageRate

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/ticket/GetAverageRate/3016769'));
      request.bodyFields = {
        'TicketID': '3016769'
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

### GETfilter/departments

https://rapi.earthlink.iq/api/reseller/ticket/filter/departments

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

filter/departments

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.MultipartRequest('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/ticket/filter/departments'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

### GETcanOpenTicket

https://rapi.earthlink.iq/api/reseller/ticket/canOpenTicket

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

canOpenTicket

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/ticket/canOpenTicket'));
      request.bodyFields = {};
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

## 

Payment

### POSTGet users invoices

https://rapi.earthlink.iq/api/reseller/userpayment/usersInvoice

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

AffiliateIndex

2199

UserID

AccountType

Notes

StartIndex

0

RowCount

50

OrderBy

OrderByDesc

Query

max@hus

Example Request

Get users invoices

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/userpayment/usersInvoice'));
      request.bodyFields = {
        'Query': 'max@hus'
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

### POSTquick payment

https://rapi.earthlink.iq/api/reseller/userpayment/invoice/quick

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

UserID

@a

Example Request

quick payment

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/userpayment/invoice/quick'));
      request.bodyFields = {
        'UserID': '@a'
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

## 

dashboard

### GETDashboard Stats

https://rapi.earthlink.iq/api/reseller/home/<USER>

get statistics about the affiliate, i.e online users, active users, will be disabled users, we should be able to link each to a view that displays who are these users (by applying filter preset)

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

Dashboard Stats

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.MultipartRequest('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/GetDashboardStats'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": {
        "activeUsers": 2,
        "expiredUsers": 288,
        "willBeDisabled": 0,
        "disabled": 0,
        "maxHighSignal": 0,
        "onlineUsers": 87
      },
      "responseMessage": null,
      "error": null
    }

### GETPrepaidNeeded

https://rapi.earthlink.iq/api/reseller/home/<USER>

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

PrepaidNeeded

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/dash/PrepaidNeeded'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": [
        {
          "accountName": "Economy +",
          "accountImagePath": null,
          "count": 1,
          "available": 1242,
          "accountCost": 18,
          "needed": 0
        },
        {
          "accountName": "Light +",
          "accountImagePath": null,
          "count": 1,
          "available": 535,
          "accountCost": 13.5,
          "needed": 0
        }
      ],
      "responseMessage": null,
      "error": null,
      "isSuccessful": true
    }

### GETRecentlyExpired

https://rapi.earthlink.iq/api/reseller/dash/RecentlyExpired

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

RecentlyExpired

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/dash/RecentlyExpired'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": [],
      "responseMessage": null,
      "error": null,
      "isSuccessful": true
    }

### GETExpiringUsers

https://rapi.earthlink.iq/api/reseller/dash/ExpiringUsers

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

ExpiringUsers

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/dash/ExpiringUsers'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

### GETOverdue payment

https://rapi.earthlink.iq/api/reseller/dash/OverduePayment

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

Overdue payment

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/dash/OverduePayment'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

### GETUpdated Tickets

https://rapi.earthlink.iq/api/reseller/ticket/updated

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

Updated Tickets

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/ticket/updated'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body

### GETDashboard users list

https://rapi.earthlink.iq/api/reseller/home/<USER>

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

Dashboard Stats

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/GetDashboardStats'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": {
        "activeUsers": 2,
        "expiredUsers": 288,
        "willBeDisabled": 0,
        "disabled": 0,
        "maxHighSignal": 0,
        "onlineUsers": 87
      },
      "responseMessage": null,
      "error": null
    }

### POSTusersSubscriptions

https://rapi.earthlink.iq/api/reseller/reports/usersSubscriptions

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

usersSubscriptions

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/reports/usersSubscriptions'));
      request.bodyFields = {};
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": {
        "activeUsers": 24,
        "expiredUsers": 840,
        "allUsers": 871,
        "newUsers": 11,
        "renewedUsers": 10,
        "manualInactiveUsers": 1
      },
      "responseMessage": null,
      "error": null,
      "isSuccessful": true
    }

## 

UserSession

### POSTget deposit confirmation msg

https://rapi.earthlink.iq/api/reseller/affiliate/deposit/confirmationmsg

used when creating new user or trying to refil one, to give a message of the selected account cost and balance left in the affiliate

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

UserID

used for refil

TargetAffiliateID

4336

from subaffiliate api (refil + new)

AccountID

56

from account type api (refil optionally+ new)

AffiliateTypeID

1

from max tower api (new only for max)

Example Request

deposit confirmationmsg

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/affiliate/deposit/confirmationmsg?userid=3amer89@hus&accountid=60'));
      request.bodyFields = {};
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": true,
      "responseMessage": "Account cost is <b>29.00 $</b> , your current balance is <b>107.5$</b>",
      "error": null
    }

### POSTbalance transfer

https://rapi.earthlink.iq/api/reseller/affiliate/deposit/transferBalance

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

TargetAffiliateIndex

11111

Amount

1

DepositPassword

Elink3

Example Request

deposit/balance transfer

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/affiliate/deposit/transferBalance'));
      request.bodyFields = {
        'TargetAffiliateIndex': '11111',
        'Amount': '1',
        'DepositPassword': ''
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": false,
      "responseMessage": "you can not transfer money to this affiliate !",
      "error": {
        "message": "you can not transfer money to this affiliate !",
        "detailMessage": null,
        "errorCode": 0,
        "validationErrors": []
      },
      "isSuccessful": false
    }

### GETbalance

https://rapi.earthlink.iq/api/reseller/affiliate/deposit/balance

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

TargetAffiliateIndex

11111

Amount

1

DepositPassword

Elink3

Example Request

balance

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/affiliate/deposit/balance'));
      request.bodyFields = {
        'TargetAffiliateIndex': '11111',
        'Amount': '1',
        'DepositPassword': 'Elink3'
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": 229.75,
      "responseMessage": null,
      "error": null,
      "isSuccessful": true
    }

### POSTAccount cost

https://rapi.earthlink.iq/api/reseller/affiliate/deposit/accountCost

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

UserID

used for refil

TargetAffiliateID

4336

from subaffiliate api (refil + new)

AccountID

56

from account type api (refil optionally+ new)

AffiliateTypeID

1

from max tower api (new only for max)

Example Request

Account cost

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/affiliate/deposit/accountCost'));
      request.bodyFields = {
        'UserID': '',
        'TargetAffiliateID': '4336',
        'AccountID': '56',
        'AffiliateTypeID': '1'
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": {
        "value": 16.5,
        "currencySign": "$",
        "currencyISOCode": "USD"
      },
      "responseMessage": null,
      "error": null,
      "isSuccessful": true
    }

### POSTnewuser with deposit

https://rapi.earthlink.iq/api/reseller/user/newuserdeposit

here we need to provide DepositPassword with the request to be accepted, the we should have selected what affiliate (or subaffiliate) this user will be created for and if it was max then we need to choose if it was max 25 or max 35 (for the selected affiliate)

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

DepositPassword

Elink3

AgentIndex

2199

in case the user will be created on another affiliate

AffiliateIndex

2199

different when max is used

AccountIndex

60

selected from a list and provided that user has enough balance to create this account

UserID

newtestuser11@hus

UserPass

1

EarthMaxMAC

for max user only

AffiliateTypeID

for max the selected tower type

FirstName

Firstname

LastName

Last

Company

Address

City

State

Country

Zip

Email

MobileNumber

MobileNumber2

DisplayName

Example Request

newuser with deposit success

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/user/newuserdeposit'));
      request.bodyFields = {
        'DepositPassword': 'Elink3',
        'AgentIndex': '2199',
        'AffiliateIndex': '2199',
        'AccountIndex': '60',
        'UserID': 'newtestuser11@hus',
        'UserPass': '1',
        'EarthMaxMAC': '',
        'FirstName': 'Firstname',
        'LastName': 'Last',
        'Company': '',
        'Address': '',
        'City': '',
        'State': '',
        'Country': '',
        'Zip': '',
        'Email': '',
        'MobileNumber': '',
        'HomePhone': '',
        'WorkPhone': '',
        'ArName': 'محمد علي',
        'EnName': ''
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": ********,
      "responseMessage": null,
      "error": null
    }

### GETaffiliate info

https://rapi.earthlink.iq/api/reseller/affiliate

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

affiliate info

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/affiliate'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": {
        "mainAffiliate": "hus",
        "companyLogo": "https://user.earthlink.iq/assets/img/EarthlinkLogo.png"
      },
      "responseMessage": null,
      "error": null,
      "isSuccessful": true
    }

## 

Tests

### GETtest users count

https://rapi.earthlink.iq/api/reseller/testcount

to display to the user howmany test users is left for him to create

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Example Request

test users count

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.MultipartRequest('GET', Uri.parse('https://rapi.earthlink.iq/api/reseller/testcount'));

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    26

### POSTnew testuser

https://rapi.earthlink.iq/api/reseller/user/newtestuser

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

UserID

newtestuser1@hus

UserPass

1

AccountIndex

57

required to set the account type of the new user

MaxTowerType

use the reselt from a GetResellerMaxTowers selection (if the user was max type, the possible values are 1,2)

EarthMaxMAC

required if the user is from max type

FirstName

Firstname

LastName

Last

Company

Address

City

State

Country

Zip

Email

MobileNumber

MobileNumber2

DisplayName

migrated from ArName and EnName

Example Request

new testuser

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/user/newtestuser'));
      request.bodyFields = {
        'UserID': 'newtestuser1@hus',
        'UserPass': '1',
        'AccountIndex': '57',
        'MaxTowerType': '',
        'EarthMaxMAC': '',
        'FirstName': 'Firstname',
        'LastName': 'Last',
        'Company': '',
        'Address': '',
        'City': '',
        'State': '',
        'Country': '',
        'Zip': '',
        'Email': '',
        'MobileNumber': '',
        'HomePhone': '',
        'WorkPhone': '',
        'ArName': 'محمد علي',
        'EnName': ''
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": ********,
      "responseMessage": null,
      "error": null
    }

### POSTTransfer Tests

https://rapi.earthlink.iq/api/reseller/TransferTests

HEADERS

Content-Type

application/x-www-form-urlencoded

Accept

application/json

Authorization

Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed\_m-Ds

Bodyurlencoded

TargetAffiliateIndex

2199

Amount

1

Example Request

Transfer Tests

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HoaQ2edL46lrjfkoScyXvBwONgZlpiKrRKz7ed_m-Ds'
      };
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/TransferTests'));
      request.bodyFields = {
        'TargetAffiliateIndex': '2199',
        'Amount': '1'
      };
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

200 OK

Example Response

json

    {
      "value": false,
      "responseMessage": null,
      "error": {
        "message": "Target affiliate is not in the tree of the main affiliate",
        "detailMessage": null,
        "errorCode": 0,
        "validationErrors": []
      },
      "isSuccessful": false
    }

### POSTget Token

https://rapi.earthlink.iq/api/reseller/Token

you need to run this before all the other apis to get the token in an env variable

Bodyurlencoded

username

btt@bttz9

password

11220099

loginType

1

grant\_type

password

Example Request

get Token

dart

    import 'package:http/http.dart' as http;

    void main() async {
      var request = http.Request('POST', Uri.parse('https://rapi.earthlink.iq/api/reseller/Token'));
      request.bodyFields = {
        'username': 'btt@bttz9',
        'password': '11220099',
        'loginType': '1',
        'grant_type': 'password'
      };

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        print(await response.stream.bytesToString());
      }
      else {
        print(response.reasonPhrase);
      }

    }

Example Response

No response body

This request doesn't return any response body
