import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import '../models/expense_category_model.dart';
import '../models/expense_model.dart';
import '../services/database_service.dart';
import '../services/app_settings_service.dart';

class AddExpensePage extends StatefulWidget {
  const AddExpensePage({super.key});

  @override
  State<AddExpensePage> createState() => _AddExpensePageState();
}

class _AddExpensePageState extends State<AddExpensePage> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  ExpenseCategoryModel? _selectedCategory;
  List<ExpenseCategoryModel> _categories = [];
  bool _isLoading = true;
  String _currencySymbol = 'د.ع';

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _loadCurrencySymbol();
  }

  Future<void> _loadCurrencySymbol() async {
    final symbol = await AppSettingsService.getCurrencySymbol();
    setState(() {
      _currencySymbol = symbol;
    });
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
    });
    try {
      _categories = await DatabaseService().getExpenseCategoriesFire();
      if (_categories.isEmpty) {
        _categories = await DatabaseService().getExpenseCategories();
      }
      if (_categories.isNotEmpty) {
        _selectedCategory = _categories.first;
      }
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل فئات المصاريف: $e')),
        );
      });
    }
  }

  final FirebaseFirestore firestore = FirebaseFirestore.instance;

  void _addExpense() async {
    if (_formKey.currentState!.validate()) {
      if (_selectedCategory == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('يرجى اختيار فئة للمصروف.')),
        );
        return;
      }

      setState(() {
        _isLoading = true;
      });

      try {
        final amount = double.parse(_amountController.text);
        final notes = _notesController.text.trim();

        final newExpense = ExpenseModel(
          adminId: DatabaseService().adminId,
          id: firestore.collection('expenses').doc().id,
          categoryId: _selectedCategory!.id,
          amount: amount,
          notes: notes,
          timestamp: DateTime.now(),
        );

        await DatabaseService().addExpense(newExpense);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إضافة المصروف بنجاح.')),
          );
          Navigator.pop(context, true); // Pop with true to indicate success
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في إضافة المصروف: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إضافة مصروف جديد')),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    TextFormField(
                      controller: _amountController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: 'المبلغ المصروف ($_currencySymbol)',
                        border: const OutlineInputBorder(),
                        prefixIcon: const Icon(Icons.money),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال المبلغ.';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال رقم صحيح.';
                        }
                        if (double.parse(value) <= 0) {
                          return 'يجب أن يكون المبلغ أكبر من صفر.';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),
                    DropdownButtonFormField<ExpenseCategoryModel>(
                      value: _selectedCategory,
                      decoration: const InputDecoration(
                        labelText: 'فئة المصروف',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.category),
                      ),
                      items: _categories.map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(category.name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار فئة.';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),
                    TextFormField(
                      controller: _notesController,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات (اختياري)',
                        alignLabelWithHint: true,
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.notes),
                      ),
                    ),
                    const SizedBox(height: 30),
                    ElevatedButton.icon(
                      onPressed: _isLoading ? null : _addExpense,
                      icon: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : const Icon(Icons.save),
                      label: Text(_isLoading ? 'جاري الحفظ...' : 'حفظ المصروف'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        textStyle: const TextStyle(fontSize: 18),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
