# MikroTik Server Integration

## Overview

This document describes the implementation of MikroTik server support in the ISP Manager application. The implementation follows the same pattern as SAS and Earthlink servers, with specific adaptations for MikroTik's RouterOS API.

## Server Types

The application now supports three server types:
1. **SAS** - Standard SAS Radius servers
2. **Earthlink** - Earthlink Reseller API servers
3. **Mikrotik** - MikroTik RouterOS API servers

## Implementation Details

### 1. Server Model Updates

The [SasServerModel](file:///C:/Users/<USER>/StudioProjects/isp-manger-main/lib/models/sas_server_model.dart#L11-L55) has been extended to support MikroTik servers with the following additions:
- `port` field (optional int) - For specifying the API port (default: 8728)
- [ServerType.Mikrotik](file:///C:/Users/<USER>/StudioProjects/isp-manger-main/lib/models/sas_server_model.dart#L3-L3) enum value

### 2. Server Management UI

The server management interface has been updated to:
- Include MikroTik as a server type option
- Show port configuration field only for MikroTik servers
- Validate port numbers (1-65535)
- Display server information with port for MikroTik servers

### 3. MikroTik Service

A new [MikrotikServerService](file:///C:/Users/<USER>/StudioProjects/isp-manger-main/lib/services/mikrotik_server_service.dart#L7-L250) has been created with the following capabilities:
- Authentication with MikroTik devices
- Fetching PPP secrets (subscribers)
- Fetching PPP profiles (packages)
- System information retrieval
- Connection diagnostics

### 4. Data Synchronization

A new [MikrotikSyncProgressPage](file:///C:/Users/<USER>/StudioProjects/isp-manger-main/lib/pages/mikrotik_sync_progress_page.dart#L7-L258) has been implemented to handle:
- Connection to MikroTik devices
- Fetching and processing PPP secrets as subscribers
- Fetching and processing PPP profiles as packages
- Progress tracking and logging

### 5. Subscriber Management

Subscriber detail pages have been updated to:
- Handle MikroTik subscribers
- Display MikroTik-specific information
- Support renewal operations for MikroTik subscribers

## Technical Specifications

### Connection Parameters
- **Host**: IP address or hostname of the MikroTik device
- **Port**: API port (default 8728)
- **Username**: API user with appropriate permissions
- **Password**: Password for the API user

### Data Mapping

#### PPP Secrets → Subscribers
- `name` → username
- `password` → password
- `profile` → package name
- `comment` → comment (may contain expiration date)
- `disabled` → isActive status

#### PPP Profiles → Packages
- `name` → package name
- `rate-limit` → download/upload speeds

### Expiration Date Parsing

Expiration dates are parsed from the comment field of PPP secrets:
- Expected format: `YYYY-MM-DD HH:MM:SS "Name" "Phone"`
- Only the date part (`YYYY-MM-DD`) is extracted
- Invalid formats are handled gracefully

## API Endpoints Used

- `/system/resource/print` - System information
- `/ppp/secret/print` - PPP secrets (subscribers)
- `/ppp/profile/print` - PPP profiles (packages)

## Error Handling

The service implements comprehensive error handling for:
- Connection failures
- Authentication errors
- API communication issues
- Data parsing errors

## Future Enhancements

1. **Online Status Detection**: Implement checking if a PPP user is currently online
2. **User Management**: Add capabilities to create/modify/delete PPP secrets
3. **Package Management**: Add capabilities to create/modify/delete PPP profiles
4. **Advanced Filtering**: Implement filtering options for large subscriber lists
5. **Performance Optimization**: Add caching mechanisms for frequently accessed data

## Testing

Unit tests have been created for the [MikrotikServerService](file:///C:/Users/<USER>/StudioProjects/isp-manger-main/lib/services/mikrotik_server_service.dart#L7-L250) to verify:
- Initialization and default values
- Login failure handling
- Protected method access control
- Error message translation
- Diagnostic functionality

## Usage Instructions

1. **Adding a MikroTik Server**:
   - Navigate to Server Management
   - Click "Add Server"
   - Select "Mikrotik" as server type
   - Enter server details (host, port, credentials)
   - Save the configuration

2. **Connecting to a MikroTik Server**:
   - Click the "Connect" button for the server
   - Wait for connection verification
   - Check diagnostics if connection fails

3. **Synchronizing Data**:
   - Ensure the server is connected
   - Click the "Sync" button
   - Monitor progress in the sync dialog
   - Review logs for any issues

4. **Managing Subscribers**:
   - View subscriber details in the main subscriber list
   - Access additional information in the subscriber detail page
   - Perform renewal operations as needed