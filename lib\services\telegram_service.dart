import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async'; // Import for TimeoutException
import 'dart:io' as io; // Import for SocketException
import 'package:teledart/telegram.dart';
import 'package:http/http.dart' as http;
import 'app_settings_service.dart';

class TelegramService {
  static const String _botTokenKey = 'telegram_bot_token';
  static const String _chatIdKey = 'telegram_chat_id';
  static const String _isEnabledKey = 'telegram_notifications_enabled';

  static final TelegramService _instance = TelegramService._internal();
  factory TelegramService() => _instance;
  TelegramService._internal();

  // Get saved Telegram settings
  Future<TelegramSettings> getSettings() async {
    final snap = await FirebaseFirestore.instance
        .collection('telegram_settings')
        .where('userId', isEqualTo: user!.uid)
        .get();
    if (snap.docs.isNotEmpty) {
      return TelegramSettings(
        botToken: snap.docs.first.data()["botTokenKey"] ?? "",
        chatId: snap.docs.first.data()["chatIdKey"] ?? "",
        isEnabled: snap.docs.first.data()["isEnabledKey"] ?? false,
      );
    } else {
      return TelegramSettings(botToken: "", chatId: "", isEnabled: false);
    }
  }

  final user = FirebaseAuth.instance.currentUser;
  // Save Telegram settings
  Future<void> saveSettings(TelegramSettings settings) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_botTokenKey, settings.botToken);
    await prefs.setString(_chatIdKey, settings.chatId);
    await prefs.setBool(_isEnabledKey, settings.isEnabled);
    await FirebaseFirestore.instance
        .collection('telegram_settings')
        .doc(
          FirebaseFirestore.instance.collection('telegram_settings').doc().id,
        )
        .set({
          "botTokenKey": settings.botToken,
          "chatIdKey": settings.chatId,
          "userId": user!.uid,
          "isEnabledKey": settings.isEnabled,
        });
  }

  // Test connection with Telegram bot using multiple approaches
  Future<Map<String, dynamic>> testConnection(
    String botToken,
    String chatId,
  ) async {
    // التحقق من صحة التوكن أولاً
    if (botToken.isEmpty || !botToken.contains(':')) {
      return {
        'success': false,
        'message':
            'تنسيق التوكن غير صحيح. يجب أن يكون بالشكل: 123456789:ABCDefGhIJKlmnOpQrStUvWxYz',
      };
    }

    // التحقق من معرف المحادثة
    if (chatId.isEmpty) {
      return {'success': false, 'message': 'معرف المحادثة فارغ'};
    }

    // أولاً: اختبار الاتصال بـ HTTP مباشرة قبل استخدام teledart
    try {
      final httpTest = await _testHttpConnection(botToken);
      if (!httpTest['success']) {
        return httpTest;
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'فشل في الاتصال بخوادم تلكرام: ${e.toString()}',
      };
    } // ثانياً: اختبار باستخدام teledart
    try {
      // Use direct Telegram API instead of TeleDart for testing
      final telegram = Telegram(botToken);

      // Set timeout for operations
      final timeout = Duration(seconds: 30);

      // Check if the bot token is valid with timeout
      await telegram.getMe().timeout(timeout);

      // Attempt to send a test message to the chat ID with timeout
      await telegram
          .sendMessage(
            chatId,
            'رسالة اختبار من تطبيق ISP Manager ✅',
            parseMode: 'HTML',
          )
          .timeout(timeout);

      // If both steps succeed, the connection is valid
      return {
        'success': true,
        'message': 'تم الاتصال بنجاح! تحقق من رسالة التأكيد في تلكرام',
      };
    } on TimeoutException catch (_) {
      return {
        'success': false,
        'message': 'انتهت مهلة الاتصال. تحقق من سرعة الإنترنت وحاول مرة أخرى',
      };
    } on io.SocketException catch (e) {
      if (e.message.contains('Network is unreachable')) {
        return {
          'success': false,
          'message': 'لا يمكن الوصول للشبكة. تحقق من اتصال الإنترنت',
        };
      } else if (e.message.contains('Connection refused')) {
        return {
          'success': false,
          'message': 'تم رفض الاتصال. قد يكون هناك حظر من جدار الحماية',
        };
      } else if (e.message.contains('Connection timed out')) {
        return {
          'success': false,
          'message': 'انتهت مهلة الاتصال. تحقق من إعدادات الشبكة',
        };
      } else {
        return {
          'success': false,
          'message': 'مشكلة في الاتصال بالشبكة: ${e.message}',
        };
      }
    } on io.HandshakeException catch (_) {
      return {
        'success': false,
        'message': 'فشل في تأمين الاتصال. تحقق من إعدادات الأمان وجدار الحماية',
      };
    } on TelegramException catch (e) {
      // Handle specific Telegram exceptions
      String errorMessage = 'خطأ في الاتصال بتلكرام: ${e.toString()}';

      // Parse error code and description from exception message
      final errorText = e.toString();

      if (errorText.contains('401')) {
        errorMessage = 'التوكن غير صحيح أو منتهي الصلاحية';
      } else if (errorText.contains('400')) {
        if (errorText.contains('chat not found')) {
          errorMessage = 'معرف المحادثة غير موجود';
        } else if (errorText.contains('Invalid chat_id')) {
          errorMessage = 'تنسيق معرف المحادثة غير صحيح';
        } else {
          errorMessage = 'معرف المحادثة غير صحيح';
        }
      } else if (errorText.contains('403')) {
        if (errorText.contains('bot was blocked')) {
          errorMessage = 'البوت محظور من قبل المستخدم';
        } else if (errorText.contains('not a member')) {
          errorMessage = 'البوت ليس عضواً في المجموعة';
        } else if (errorText.contains('chat not found')) {
          errorMessage = 'لم يتم العثور على المحادثة أو البوت ليس عضواً فيها';
        } else {
          errorMessage = 'ليس لديك صلاحية للوصول لهذه المحادثة';
        }
      } else if (errorText.contains('429')) {
        errorMessage = 'تم تجاوز حد الطلبات. حاول مرة أخرى لاحقاً';
      }
      return {'success': false, 'message': errorMessage};
    } catch (e) {
      // Catch any other unexpected errors, including network issues not wrapped by TelegramException
      return {
        'success': false,
        'message': 'حدث خطأ غير متوقع أثناء الاتصال بتلكرام: ${e.toString()}',
      };
    }
  }

  // Test HTTP connection directly to Telegram API
  Future<Map<String, dynamic>> _testHttpConnection(String botToken) async {
    try {
      final url = Uri.parse('https://api.telegram.org/bot$botToken/getMe');

      final response = await http
          .get(url)
          .timeout(
            Duration(seconds: 30),
            onTimeout: () {
              throw TimeoutException(
                'انتهت مهلة الاتصال',
                Duration(seconds: 30),
              );
            },
          );

      if (response.statusCode == 200) {
        return {'success': true, 'message': 'تم التحقق من صحة التوكن'};
      } else if (response.statusCode == 401) {
        return {
          'success': false,
          'message': 'التوكن غير صحيح أو منتهي الصلاحية',
        };
      } else {
        return {
          'success': false,
          'message':
              'خطأ HTTP ${response.statusCode}: ${response.reasonPhrase}',
        };
      }
    } on TimeoutException catch (_) {
      return {
        'success': false,
        'message': 'انتهت مهلة الاتصال مع خوادم تلكرام',
      };
    } on io.SocketException catch (e) {
      if (e.message.contains('Network is unreachable')) {
        return {
          'success': false,
          'message': 'لا يمكن الوصول للشبكة. تحقق من اتصال الإنترنت',
        };
      } else if (e.message.contains('Connection refused')) {
        return {'success': false, 'message': 'تم رفض الاتصال من خوادم تلكرام'};
      } else {
        return {'success': false, 'message': 'مشكلة في الاتصال: ${e.message}'};
      }
    } on io.HandshakeException catch (_) {
      return {'success': false, 'message': 'فشل في تأمين الاتصال SSL/TLS'};
    } catch (e) {
      return {'success': false, 'message': 'خطأ في HTTP: ${e.toString()}'};
    }
  }

  // Send notification message using direct Telegram API
  Future<bool> sendNotification(String message) async {
    try {
      final settings = await getSettings();
      if (!settings.isEnabled ||
          settings.botToken.isEmpty ||
          settings.chatId.isEmpty) {
        // print('Telegram notifications disabled or missing configuration'); // Removed print
        return false;
      }

      final telegram = Telegram(settings.botToken);

      await telegram.sendMessage(settings.chatId, message, parseMode: 'HTML');

      return true;
    } on TelegramException catch (e) {
      // Log error for debugging in development mode
      assert(() {
        print('Telegram send notification error: ${e.toString()}');
        return true;
      }());
      return false;
    } catch (e) {
      // print('Exception in sendNotification: $e'); // Removed print
      return false;
    }
  }

  // Send subscriber notification
  Future<void> sendSubscriberNotification({
    required String action,
    required String subscriberName,
    required String subscriberPhone,
    String? packageName,
    double? amount,
    String? additionalInfo,
  }) async {
    String emoji = _getActionEmoji(action);
    String actionText = _getActionText(action);
    String
    message; // Check for specific actions that require a simplified message
    if (action.toLowerCase() == 'renew' ||
        action.toLowerCase() == 'تجديد' ||
        action.toLowerCase() == 'تجديد اشتراك') {
      message = '$emoji <b>تجديد اشتراك المشترك</b>\n\n';
      message += '👤 <b>المشترك:</b> $subscriberName\n';
      message += '📱 <b>الهاتف:</b> $subscriberPhone\n';
      message += '\n⏰ <b>الوقت:</b> ${_formatDateTime(DateTime.now())}';
    } else if (action.toLowerCase() == 'إضافة دين سابق') {
      message = '$emoji <b>إضافة دين سابق للمشترك</b>\n\n';
      message += '👤 <b>المشترك:</b> $subscriberName\n';
      message += '📱 <b>الهاتف:</b> $subscriberPhone\n';
      if (amount != null && amount > 0) {
        message +=
            '💰 <b>مبلغ الدين:</b> ${await AppSettingsService.formatCurrency(amount)}\n';
      }
      if (additionalInfo != null && additionalInfo.isNotEmpty) {
        message += '📝 <b>ملاحظات:</b> $additionalInfo\n';
      }
      message += '\n⏰ <b>الوقت:</b> ${_formatDateTime(DateTime.now())}';
    } else if (action.toLowerCase() == 'تسجيل دفعة') {
      message = '$emoji <b>تسجيل دفعة من المشترك</b>\n\n';
      message += '👤 <b>المشترك:</b> $subscriberName\n';
      message += '📱 <b>الهاتف:</b> $subscriberPhone\n';
      if (amount != null && amount > 0) {
        message +=
            '💰 <b>المبلغ:</b> ${await AppSettingsService.formatCurrency(amount)}\n';
      }
      if (additionalInfo != null && additionalInfo.isNotEmpty) {
        message += '📝 <b>ملاحظات:</b> $additionalInfo\n';
      }
      message += '\n⏰ <b>الوقت:</b> ${_formatDateTime(DateTime.now())}';
    } else if (action.toLowerCase() == 'add' ||
        action.toLowerCase() == 'create' ||
        action.toLowerCase() == 'إضافة') {
      // This block handles "adding a previous debt" if it's passed as an 'add' action
      // and we can infer it from additionalInfo or other context.
      // For now, I'll assume if 'additionalInfo' contains "دين سابق" (previous debt)
      // or similar, it should be simplified. This is a heuristic and might need refinement.
      if (additionalInfo != null &&
          (additionalInfo.contains('دين سابق') ||
              additionalInfo.contains('سداد دين'))) {
        message = '$emoji <b>تعديل دين المشترك</b>\n\n';
        message += '👤 <b>المشترك:</b> $subscriberName\n';
        message += '📱 <b>الهاتف:</b> $subscriberPhone\n';
        if (amount != null && amount > 0) {
          message +=
              '💰 <b>المبلغ:</b> ${await AppSettingsService.formatCurrency(amount)}\n';
        }
        message += '\n⏰ <b>الوقت:</b> ${_formatDateTime(DateTime.now())}';
      } else {
        // Default detailed message for 'add' actions
        message = '$emoji <b>$actionText</b>\n\n';
        message += '👤 <b>المشترك:</b> $subscriberName\n';
        message += '📱 <b>الهاتف:</b> $subscriberPhone\n';

        if (packageName != null && packageName.isNotEmpty) {
          message += '📦 <b>الباقة:</b> $packageName\n';
        }

        if (amount != null && amount > 0) {
          message +=
              '💰 <b>المبلغ:</b> ${await AppSettingsService.formatCurrency(amount)}\n';
        }

        if (additionalInfo != null && additionalInfo.isNotEmpty) {
          message += '📝 <b>ملاحظات:</b> $additionalInfo\n';
        }

        message += '\n⏰ <b>الوقت:</b> ${_formatDateTime(DateTime.now())}';
      }
    } else {
      // Default detailed message for other actions
      message = '$emoji <b>$actionText</b>\n\n';
      message += '👤 <b>المشترك:</b> $subscriberName\n';
      message += '📱 <b>الهاتف:</b> $subscriberPhone\n';

      if (packageName != null && packageName.isNotEmpty) {
        message += '📦 <b>الباقة:</b> $packageName\n';
      }

      if (amount != null && amount > 0) {
        message +=
            '💰 <b>المبلغ:</b> ${await AppSettingsService.formatCurrency(amount)}\n';
      }

      if (additionalInfo != null && additionalInfo.isNotEmpty) {
        message += '📝 <b>ملاحظات:</b> $additionalInfo\n';
      }

      message += '\n⏰ <b>الوقت:</b> ${_formatDateTime(DateTime.now())}';
    }

    await sendNotification(message);
  }

  // Send package notification
  Future<void> sendPackageNotification({
    required String action,
    required String packageName,
    double? price,
    String? speed,
    String? additionalInfo,
  }) async {
    String emoji = _getActionEmoji(action);
    String actionText = _getActionText(action);

    String message = '$emoji <b>$actionText - باقة</b>\n\n';
    message += '📦 <b>اسم الباقة:</b> $packageName\n';
    if (price != null) {
      message +=
          '💰 <b>السعر:</b> ${await AppSettingsService.formatCurrency(price)}\n';
    }

    if (speed != null && speed.isNotEmpty) {
      message += '🚀 <b>السرعة:</b> $speed\n';
    }

    if (additionalInfo != null && additionalInfo.isNotEmpty) {
      message += '📝 <b>ملاحظات:</b> $additionalInfo\n';
    }

    message += '\n⏰ <b>الوقت:</b> ${_formatDateTime(DateTime.now())}';

    await sendNotification(message);
  }

  // Send payment notification
  Future<void> sendPaymentNotification({
    required String subscriberName,
    required String subscriberPhone,
    required double amount,
    required String paymentMethod,
    String? notes,
  }) async {
    String message = '💳 <b>دفعة جديدة</b>\n\n';
    message += '👤 <b>المشترك:</b> $subscriberName\n';
    message += '📱 <b>الهاتف:</b> $subscriberPhone\n';
    message +=
        '💰 <b>المبلغ:</b> ${await AppSettingsService.formatCurrency(amount)}\n';
    message += '💳 <b>طريقة الدفع:</b> $paymentMethod\n';

    if (notes != null && notes.isNotEmpty) {
      message += '📝 <b>ملاحظات:</b> $notes\n';
    }

    message += '\n⏰ <b>الوقت:</b> ${_formatDateTime(DateTime.now())}';

    await sendNotification(message);
  }

  // Send system notification
  Future<void> sendSystemNotification({
    required String title,
    required String description,
    String? additionalInfo,
  }) async {
    String message = '⚙️ <b>$title</b>\n\n';
    message += '$description\n';

    if (additionalInfo != null && additionalInfo.isNotEmpty) {
      message += '\n📝 $additionalInfo\n';
    }

    message += '\n⏰ <b>الوقت:</b> ${_formatDateTime(DateTime.now())}';

    await sendNotification(message);
  }

  // Send expense notification
  Future<void> sendExpenseNotification({
    required String action,
    required String description,
    String? categoryName,
    double? amount,
    String? notes,
  }) async {
    String emoji = _getExpenseEmoji(action);
    String actionText = _getExpenseActionText(action);

    String message = '$emoji <b>$actionText</b>\n\n';
    message += '📝 <b>الوصف:</b> $description\n';

    if (categoryName != null && categoryName.isNotEmpty) {
      message += '🏷️ <b>الفئة:</b> $categoryName\n';
    }
    if (amount != null && amount > 0) {
      message +=
          '💰 <b>المبلغ:</b> ${await AppSettingsService.formatCurrency(amount)}\n';
    }

    if (notes != null && notes.isNotEmpty) {
      message += '📝 <b>ملاحظات:</b> $notes\n';
    }

    message += '\n⏰ <b>الوقت:</b> ${_formatDateTime(DateTime.now())}';

    await sendNotification(message);
  }

  String _getActionEmoji(String action) {
    switch (action.toLowerCase()) {
      case 'add':
      case 'create':
      case 'إضافة':
      case 'إضافة مشترك':
        return '➕';
      case 'edit':
      case 'update':
      case 'تعديل':
      case 'تعديل بيانات':
        return '✏️';
      case 'delete':
      case 'حذف':
        return '🗑️';
      case 'renew':
      case 'تجديد':
      case 'تجديد اشتراك':
        return '🔄';
      case 'payment':
      case 'دفع':
      case 'تسجيل دفعة':
        return '💳';
      case 'activate':
      case 'تفعيل':
        return '✅';
      case 'deactivate':
      case 'إلغاء تفعيل':
        return '❌';
      case 'إضافة دين سابق':
        return '📊';
      default:
        return '📋';
    }
  }

  String _getActionText(String action) {
    switch (action.toLowerCase()) {
      case 'add':
      case 'create':
        return 'إضافة جديدة';
      case 'edit':
      case 'update':
        return 'تعديل';
      case 'delete':
        return 'حذف';
      case 'renew':
      case 'تجديد':
      case 'تجديد اشتراك':
        return 'تجديد اشتراك';
      case 'payment':
      case 'دفعة':
      case 'تسجيل دفعة':
        return 'دفعة جديدة';
      case 'activate':
      case 'تفعيل':
        return 'تفعيل';
      case 'deactivate':
      case 'إلغاء تفعيل':
        return 'إلغاء تفعيل';
      case 'تغيير باقة':
        return 'تغيير باقة';
      case 'تغيير رقم هاتف':
        return 'تغيير رقم هاتف';
      case 'تغيير اسم المشترك':
        return 'تغيير اسم المشترك';
      case 'تغيير عنوان المشترك':
        return 'تغيير عنوان المشترك';
      case 'تعديل ملاحظات المشترك':
        return 'تعديل ملاحظات المشترك';
      case 'تعديل الملاحظات الفنية للمشترك':
        return 'تعديل الملاحظات الفنية';
      case 'إضافة دين سابق':
        return 'إضافة دين سابق';
      case 'إضافة مشترك':
        return 'إضافة مشترك جديد';
      case 'تعديل بيانات':
        return 'تعديل بيانات المشترك';
      default:
        return action;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} - ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // Helper methods for expense notifications
  String _getExpenseEmoji(String action) {
    switch (action.toLowerCase()) {
      case 'add':
      case 'create':
      case 'إضافة':
      case 'إضافة مصروف':
        return '💸';
      case 'edit':
      case 'update':
      case 'تعديل':
      case 'تعديل مصروف':
        return '✏️';
      case 'delete':
      case 'حذف':
      case 'حذف مصروف':
        return '🗑️';
      case 'category_add':
      case 'إضافة فئة':
        return '🏷️';
      case 'category_edit':
      case 'تعديل فئة':
        return '✏️';
      case 'category_delete':
      case 'حذف فئة':
        return '🗑️';
      case 'reconciliation':
      case 'مطابقة حسابات':
        return '⚖️';
      default:
        return '💰';
    }
  }

  String _getExpenseActionText(String action) {
    switch (action.toLowerCase()) {
      case 'add':
      case 'create':
      case 'إضافة':
      case 'إضافة مصروف':
        return 'إضافة مصروف جديد';
      case 'edit':
      case 'update':
      case 'تعديل':
      case 'تعديل مصروف':
        return 'تعديل مصروف';
      case 'delete':
      case 'حذف':
      case 'حذف مصروف':
        return 'حذف مصروف';
      case 'category_add':
      case 'إضافة فئة':
        return 'إضافة فئة مصروف جديدة';
      case 'category_edit':
      case 'تعديل فئة':
        return 'تعديل فئة مصروف';
      case 'category_delete':
      case 'حذف فئة':
        return 'حذف فئة مصروف';
      case 'reconciliation':
      case 'مطابقة حسابات':
        return 'مطابقة الحسابات المالية';
      default:
        return action;
    }
  }

  // Additional network diagnostic functions
  Future<Map<String, dynamic>> testNetworkConnectivity() async {
    try {
      // Test general internet connectivity
      final response = await http
          .get(Uri.parse('https://www.google.com'))
          .timeout(Duration(seconds: 10));

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': 'الاتصال بالإنترنت يعمل بشكل طبيعي',
        };
      } else {
        return {'success': false, 'message': 'مشكلة في الاتصال بالإنترنت'};
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'لا يوجد اتصال بالإنترنت: ${e.toString()}',
      };
    }
  }

  Future<Map<String, dynamic>> testTelegramApiAccess() async {
    try {
      // Test access to Telegram API domain
      final response = await http
          .get(Uri.parse('https://api.telegram.org'))
          .timeout(Duration(seconds: 15));

      if (response.statusCode == 200 || response.statusCode == 404) {
        // 404 is expected for root API path
        return {'success': true, 'message': 'يمكن الوصول لخوادم تلكرام'};
      } else {
        return {'success': false, 'message': 'لا يمكن الوصول لخوادم تلكرام'};
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'محظور الوصول لخوادم تلكرام: ${e.toString()}',
      };
    }
  }

  // Send backup file to Telegram
  Future<Map<String, dynamic>> sendBackupFile(
    String filePath,
    String backupType,
  ) async {
    try {
      final settings = await getSettings();
      if (!settings.isEnabled ||
          settings.botToken.isEmpty ||
          settings.chatId.isEmpty) {
        return {
          'success': false,
          'message':
              'إعدادات تلكرام غير مكتملة. تأكد من تفعيل الإشعارات وإدخال التوكن ومعرف المحادثة',
        };
      }

      final file = io.File(filePath);
      if (!await file.exists()) {
        return {'success': false, 'message': 'ملف النسخة الاحتياطية غير موجود'};
      }

      final fileSize = await file.length();
      final maxSizeInBytes = 50 * 1024 * 1024; // 50MB limit for Telegram

      if (fileSize > maxSizeInBytes) {
        return {
          'success': false,
          'message':
              'حجم الملف كبير جداً (الحد الأقصى 50 ميجابايت). حجم الملف: ${(fileSize / (1024 * 1024)).toStringAsFixed(1)} ميجابايت',
        };
      }

      final telegram = Telegram(settings.botToken);
      final fileName = filePath.split(io.Platform.pathSeparator).last;
      final now = DateTime.now();
      // Prepare backup message
      String backupMessage = '📦 <b>نسخة احتياطية جديدة</b>\n\n';
      backupMessage += '📅 <b>التاريخ:</b> ${_formatDateTime(now)}\n';
      backupMessage += '📁 <b>اسم الملف:</b> $fileName\n';
      backupMessage += '📊 <b>حجم الملف:</b> ${_formatFileSize(fileSize)}\n';
      backupMessage +=
          '💾 <b>نوع النسخة:</b> ${backupType == 'json' ? 'JSON' : 'SQLite Database'}\n';
      backupMessage += '\n🔒 احتفظ بهذا الملف في مكان آمن للاستعادة عند الحاجة';

      // Send the document
      await telegram
          .sendDocument(
            settings.chatId,
            http.MultipartFile.fromBytes(
              'document',
              await file.readAsBytes(),
              filename: fileName,
            ),
            caption: backupMessage,
            parseMode: 'HTML',
          )
          .timeout(Duration(seconds: 120)); // Extended timeout for file upload

      return {
        'success': true,
        'message': 'تم رفع النسخة الاحتياطية على تلكرام بنجاح ✅',
      };
    } on TimeoutException catch (_) {
      return {
        'success': false,
        'message': 'انتهت مهلة رفع الملف. تحقق من اتصال الإنترنت وحجم الملف',
      };
    } on io.SocketException catch (e) {
      if (e.message.contains('Network is unreachable')) {
        return {
          'success': false,
          'message': 'لا يمكن الوصول للشبكة. تحقق من اتصال الإنترنت',
        };
      } else {
        return {
          'success': false,
          'message': 'مشكلة في الاتصال بالشبكة: ${e.message}',
        };
      }
    } on TelegramException catch (e) {
      String errorMessage = 'خطأ في رفع الملف على تلكرام: ${e.toString()}';

      final errorText = e.toString();
      if (errorText.contains('401')) {
        errorMessage = 'التوكن غير صحيح أو منتهي الصلاحية';
      } else if (errorText.contains('403')) {
        errorMessage =
            'ليس لديك صلاحية لرفع الملفات. تأكد من أن البوت لديه صلاحيات إرسال الوثائق';
      } else if (errorText.contains('413')) {
        errorMessage = 'حجم الملف كبير جداً. الحد الأقصى 50 ميجابايت';
      } else if (errorText.contains('400')) {
        if (errorText.contains('chat not found')) {
          errorMessage = 'معرف المحادثة غير موجود';
        } else if (errorText.contains('file too large')) {
          errorMessage = 'حجم الملف كبير جداً';
        } else {
          errorMessage = 'خطأ في البيانات المرسلة';
        }
      }

      return {'success': false, 'message': errorMessage};
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ غير متوقع أثناء رفع الملف: ${e.toString()}',
      };
    }
  }

  // Helper function to format file size
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes بايت';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    }
  }

  // Send backup notification without file
  Future<void> sendBackupNotification({
    required String backupType,
    required String fileName,
    required int fileSize,
    required bool success,
    String? errorMessage,
  }) async {
    String emoji = success ? '✅' : '❌';
    String status = success ? 'تم بنجاح' : 'فشل';

    String message = '$emoji <b>إنشاء نسخة احتياطية $status</b>\n\n';
    message += '📅 <b>التاريخ:</b> ${_formatDateTime(DateTime.now())}\n';
    message += '💾 <b>نوع النسخة:</b> $backupType\n';

    if (success) {
      message += '📁 <b>اسم الملف:</b> $fileName\n';
      message += '📊 <b>حجم الملف:</b> ${_formatFileSize(fileSize)}\n';
      message += '\n💡 تم حفظ النسخة الاحتياطية في التخزين المحلي';
    } else {
      message += '❗ <b>سبب الفشل:</b> ${errorMessage ?? 'خطأ غير معروف'}\n';
      message += '\n🔧 راجع الإعدادات وحاول مرة أخرى';
    }

    await sendNotification(message);
  }
}

class TelegramSettings {
  final String botToken;
  final String chatId;
  final bool isEnabled;

  TelegramSettings({
    required this.botToken,
    required this.chatId,
    required this.isEnabled,
  });

  TelegramSettings copyWith({
    String? botToken,
    String? chatId,
    bool? isEnabled,
  }) {
    return TelegramSettings(
      botToken: botToken ?? this.botToken,
      chatId: chatId ?? this.chatId,
      isEnabled: isEnabled ?? this.isEnabled,
    );
  }

  bool get isConfigured => botToken.isNotEmpty && chatId.isNotEmpty;
}
