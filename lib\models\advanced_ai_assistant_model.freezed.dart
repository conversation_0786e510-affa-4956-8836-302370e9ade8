// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'advanced_ai_assistant_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

AdvancedAIAssistantModel _$AdvancedAIAssistantModelFromJson(
  Map<String, dynamic> json,
) {
  return _AdvancedAIAssistantModel.fromJson(json);
}

/// @nodoc
mixin _$AdvancedAIAssistantModel {
  String get id => throw _privateConstructorUsedError;
  String get deviceId => throw _privateConstructorUsedError;
  String get prompt => throw _privateConstructorUsedError;
  String get response => throw _privateConstructorUsedError;
  Map<String, dynamic> get context => throw _privateConstructorUsedError;
  Map<String, dynamic> get analysis => throw _privateConstructorUsedError;
  Map<String, dynamic> get recommendations =>
      throw _privateConstructorUsedError;
  Map<String, dynamic> get commands => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  String get error => throw _privateConstructorUsedError;
  int get priority => throw _privateConstructorUsedError;

  /// Serializes this AdvancedAIAssistantModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AdvancedAIAssistantModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AdvancedAIAssistantModelCopyWith<AdvancedAIAssistantModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdvancedAIAssistantModelCopyWith<$Res> {
  factory $AdvancedAIAssistantModelCopyWith(
    AdvancedAIAssistantModel value,
    $Res Function(AdvancedAIAssistantModel) then,
  ) = _$AdvancedAIAssistantModelCopyWithImpl<$Res, AdvancedAIAssistantModel>;
  @useResult
  $Res call({
    String id,
    String deviceId,
    String prompt,
    String response,
    Map<String, dynamic> context,
    Map<String, dynamic> analysis,
    Map<String, dynamic> recommendations,
    Map<String, dynamic> commands,
    DateTime timestamp,
    String status,
    String error,
    int priority,
  });
}

/// @nodoc
class _$AdvancedAIAssistantModelCopyWithImpl<
  $Res,
  $Val extends AdvancedAIAssistantModel
>
    implements $AdvancedAIAssistantModelCopyWith<$Res> {
  _$AdvancedAIAssistantModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdvancedAIAssistantModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? deviceId = null,
    Object? prompt = null,
    Object? response = null,
    Object? context = null,
    Object? analysis = null,
    Object? recommendations = null,
    Object? commands = null,
    Object? timestamp = null,
    Object? status = null,
    Object? error = null,
    Object? priority = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            deviceId: null == deviceId
                ? _value.deviceId
                : deviceId // ignore: cast_nullable_to_non_nullable
                      as String,
            prompt: null == prompt
                ? _value.prompt
                : prompt // ignore: cast_nullable_to_non_nullable
                      as String,
            response: null == response
                ? _value.response
                : response // ignore: cast_nullable_to_non_nullable
                      as String,
            context: null == context
                ? _value.context
                : context // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            analysis: null == analysis
                ? _value.analysis
                : analysis // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            recommendations: null == recommendations
                ? _value.recommendations
                : recommendations // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            commands: null == commands
                ? _value.commands
                : commands // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as String,
            error: null == error
                ? _value.error
                : error // ignore: cast_nullable_to_non_nullable
                      as String,
            priority: null == priority
                ? _value.priority
                : priority // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AdvancedAIAssistantModelImplCopyWith<$Res>
    implements $AdvancedAIAssistantModelCopyWith<$Res> {
  factory _$$AdvancedAIAssistantModelImplCopyWith(
    _$AdvancedAIAssistantModelImpl value,
    $Res Function(_$AdvancedAIAssistantModelImpl) then,
  ) = __$$AdvancedAIAssistantModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String deviceId,
    String prompt,
    String response,
    Map<String, dynamic> context,
    Map<String, dynamic> analysis,
    Map<String, dynamic> recommendations,
    Map<String, dynamic> commands,
    DateTime timestamp,
    String status,
    String error,
    int priority,
  });
}

/// @nodoc
class __$$AdvancedAIAssistantModelImplCopyWithImpl<$Res>
    extends
        _$AdvancedAIAssistantModelCopyWithImpl<
          $Res,
          _$AdvancedAIAssistantModelImpl
        >
    implements _$$AdvancedAIAssistantModelImplCopyWith<$Res> {
  __$$AdvancedAIAssistantModelImplCopyWithImpl(
    _$AdvancedAIAssistantModelImpl _value,
    $Res Function(_$AdvancedAIAssistantModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AdvancedAIAssistantModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? deviceId = null,
    Object? prompt = null,
    Object? response = null,
    Object? context = null,
    Object? analysis = null,
    Object? recommendations = null,
    Object? commands = null,
    Object? timestamp = null,
    Object? status = null,
    Object? error = null,
    Object? priority = null,
  }) {
    return _then(
      _$AdvancedAIAssistantModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        deviceId: null == deviceId
            ? _value.deviceId
            : deviceId // ignore: cast_nullable_to_non_nullable
                  as String,
        prompt: null == prompt
            ? _value.prompt
            : prompt // ignore: cast_nullable_to_non_nullable
                  as String,
        response: null == response
            ? _value.response
            : response // ignore: cast_nullable_to_non_nullable
                  as String,
        context: null == context
            ? _value._context
            : context // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        analysis: null == analysis
            ? _value._analysis
            : analysis // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        recommendations: null == recommendations
            ? _value._recommendations
            : recommendations // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        commands: null == commands
            ? _value._commands
            : commands // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as String,
        error: null == error
            ? _value.error
            : error // ignore: cast_nullable_to_non_nullable
                  as String,
        priority: null == priority
            ? _value.priority
            : priority // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AdvancedAIAssistantModelImpl implements _AdvancedAIAssistantModel {
  const _$AdvancedAIAssistantModelImpl({
    required this.id,
    required this.deviceId,
    required this.prompt,
    required this.response,
    required final Map<String, dynamic> context,
    required final Map<String, dynamic> analysis,
    required final Map<String, dynamic> recommendations,
    required final Map<String, dynamic> commands,
    required this.timestamp,
    required this.status,
    required this.error,
    required this.priority,
  }) : _context = context,
       _analysis = analysis,
       _recommendations = recommendations,
       _commands = commands;

  factory _$AdvancedAIAssistantModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdvancedAIAssistantModelImplFromJson(json);

  @override
  final String id;
  @override
  final String deviceId;
  @override
  final String prompt;
  @override
  final String response;
  final Map<String, dynamic> _context;
  @override
  Map<String, dynamic> get context {
    if (_context is EqualUnmodifiableMapView) return _context;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_context);
  }

  final Map<String, dynamic> _analysis;
  @override
  Map<String, dynamic> get analysis {
    if (_analysis is EqualUnmodifiableMapView) return _analysis;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_analysis);
  }

  final Map<String, dynamic> _recommendations;
  @override
  Map<String, dynamic> get recommendations {
    if (_recommendations is EqualUnmodifiableMapView) return _recommendations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_recommendations);
  }

  final Map<String, dynamic> _commands;
  @override
  Map<String, dynamic> get commands {
    if (_commands is EqualUnmodifiableMapView) return _commands;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_commands);
  }

  @override
  final DateTime timestamp;
  @override
  final String status;
  @override
  final String error;
  @override
  final int priority;

  @override
  String toString() {
    return 'AdvancedAIAssistantModel(id: $id, deviceId: $deviceId, prompt: $prompt, response: $response, context: $context, analysis: $analysis, recommendations: $recommendations, commands: $commands, timestamp: $timestamp, status: $status, error: $error, priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdvancedAIAssistantModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId) &&
            (identical(other.prompt, prompt) || other.prompt == prompt) &&
            (identical(other.response, response) ||
                other.response == response) &&
            const DeepCollectionEquality().equals(other._context, _context) &&
            const DeepCollectionEquality().equals(other._analysis, _analysis) &&
            const DeepCollectionEquality().equals(
              other._recommendations,
              _recommendations,
            ) &&
            const DeepCollectionEquality().equals(other._commands, _commands) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.priority, priority) ||
                other.priority == priority));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    deviceId,
    prompt,
    response,
    const DeepCollectionEquality().hash(_context),
    const DeepCollectionEquality().hash(_analysis),
    const DeepCollectionEquality().hash(_recommendations),
    const DeepCollectionEquality().hash(_commands),
    timestamp,
    status,
    error,
    priority,
  );

  /// Create a copy of AdvancedAIAssistantModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AdvancedAIAssistantModelImplCopyWith<_$AdvancedAIAssistantModelImpl>
  get copyWith =>
      __$$AdvancedAIAssistantModelImplCopyWithImpl<
        _$AdvancedAIAssistantModelImpl
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdvancedAIAssistantModelImplToJson(this);
  }
}

abstract class _AdvancedAIAssistantModel implements AdvancedAIAssistantModel {
  const factory _AdvancedAIAssistantModel({
    required final String id,
    required final String deviceId,
    required final String prompt,
    required final String response,
    required final Map<String, dynamic> context,
    required final Map<String, dynamic> analysis,
    required final Map<String, dynamic> recommendations,
    required final Map<String, dynamic> commands,
    required final DateTime timestamp,
    required final String status,
    required final String error,
    required final int priority,
  }) = _$AdvancedAIAssistantModelImpl;

  factory _AdvancedAIAssistantModel.fromJson(Map<String, dynamic> json) =
      _$AdvancedAIAssistantModelImpl.fromJson;

  @override
  String get id;
  @override
  String get deviceId;
  @override
  String get prompt;
  @override
  String get response;
  @override
  Map<String, dynamic> get context;
  @override
  Map<String, dynamic> get analysis;
  @override
  Map<String, dynamic> get recommendations;
  @override
  Map<String, dynamic> get commands;
  @override
  DateTime get timestamp;
  @override
  String get status;
  @override
  String get error;
  @override
  int get priority;

  /// Create a copy of AdvancedAIAssistantModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AdvancedAIAssistantModelImplCopyWith<_$AdvancedAIAssistantModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}
