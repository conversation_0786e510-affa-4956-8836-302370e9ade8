import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/tower_model.dart';
import '../models/service_request_model.dart';
import 'package:uuid/uuid.dart';
import 'dart:convert'; // Added for jsonEncode
import 'package:http/http.dart' as http; // Added for http

class TowerService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // جلب معرف المدير الحالي
  String? get currentAdminId {
    return _auth.currentUser?.uid;
  }

  // إضافة برج جديد
  Future<void> addTower(TowerModel tower) async {
    await _firestore
        .collection('towers')
        .doc(tower.id)
        .set(tower.toMap());
  }

  // جلب جميع أبراج المدير
  Future<List<TowerModel>> getTowersByAdmin(String adminId) async {
    final snapshot = await _firestore
        .collection('towers')
        .where('adminId', isEqualTo: adminId)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .get();

    return snapshot.docs
        .map((doc) => TowerModel.fromMap(doc.data()))
        .toList();
  }

  // جلب برج بواسطة المعرف
  Future<TowerModel?> getTowerById(String towerId) async {
    final doc = await _firestore
        .collection('towers')
        .doc(towerId)
        .get();

    if (doc.exists) {
      return TowerModel.fromMap(doc.data()!);
    }
    return null;
  }

  // تحديث برج
  Future<void> updateTower(TowerModel tower) async {
    await _firestore
        .collection('towers')
        .doc(tower.id)
        .update(tower.toMap());
  }

  // حذف برج
  Future<void> deleteTower(String towerId) async {
    await _firestore
        .collection('towers')
        .doc(towerId)
        .delete();
  }

  // إضافة طلب خدمة
  Future<void> addServiceRequest(ServiceRequestModel request) async {
    await _firestore
        .collection('service_requests')
        .doc(request.id)
        .set(request.toMap());
    // إرسال إشعار FCM للمدير المباشر إذا كان لديه fcmToken
    if (request.adminId != null && request.adminId!.isNotEmpty) {
      final adminDoc = await _firestore.collection('users').doc(request.adminId).get();
      final fcmToken = adminDoc.data()?['fcmToken'];
      if (fcmToken != null && fcmToken.toString().isNotEmpty) {
        await _sendFcmNotification(
          token: fcmToken,
          title: 'طلب خدمة جديد',
          body: 'طلب جديد من ${request.subscriberName} (${request.subscriberPhone}) لبرج ${request.towerName}',
        );
      }
    }
  }

  Future<void> _sendFcmNotification({
    required String token,
    required String title,
    required String body,
  }) async {
    const String serverKey = 'nYql-mcvbi40f4wE8MknlduIhWNbzo7jEihNF_Aeeqk'; // FCM Server Key
    final url = Uri.parse('https://fcm.googleapis.com/fcm/send');
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'key=$serverKey',
    };
    final payload = {
      'to': token,
      'notification': {
        'title': title,
        'body': body,
        'sound': 'default',
      },
      'data': {
        'click_action': 'FLUTTER_NOTIFICATION_CLICK',
      }
    };
    await http.post(url, headers: headers, body: jsonEncode(payload));
  }

  // جلب طلبات الخدمة للمدير
  Future<List<ServiceRequestModel>> getServiceRequestsByAdmin(String adminId) async {
    final snapshot = await _firestore
        .collection('service_requests')
        .where('adminId', isEqualTo: adminId)
        .orderBy('createdAt', descending: true)
        .get();

    return snapshot.docs
        .map((doc) => ServiceRequestModel.fromMap(doc.data()))
        .toList();
  }

  // جلب طلبات الخدمة لبرج معين
  Future<List<ServiceRequestModel>> getServiceRequestsByTower(String towerId) async {
    final snapshot = await _firestore
        .collection('service_requests')
        .where('towerId', isEqualTo: towerId)
        .orderBy('createdAt', descending: true)
        .get();

    return snapshot.docs
        .map((doc) => ServiceRequestModel.fromMap(doc.data()))
        .toList();
  }

  // تحديث حالة طلب الخدمة
  Future<void> updateServiceRequestStatus(String requestId, RequestStatus status, String? notes) async {
    await _firestore
        .collection('service_requests')
        .doc(requestId)
        .update({
          'status': status.name,
          'notes': notes,
          'updatedAt': FieldValue.serverTimestamp(),
        });
  }

  // البحث عن أقرب برج لموقع معين
  Future<TowerModel?> findNearestTower(double latitude, double longitude, String adminId) async {
    final towers = await getTowersByAdmin(adminId);
    
    if (towers.isEmpty) return null;

    TowerModel? nearestTower;
    double minDistance = double.infinity;

    for (final tower in towers) {
      final distance = tower.distanceTo(latitude, longitude);
      if (distance < minDistance && tower.isWithinCoverage(latitude, longitude)) {
        minDistance = distance;
        nearestTower = tower;
      }
    }

    return nearestTower;
  }

  // جلب جميع الأبراج ضمن نطاق معين
  Future<List<TowerModel>> getTowersInRange(double latitude, double longitude, String adminId, double maxDistance) async {
    final towers = await getTowersByAdmin(adminId);
    
    return towers.where((tower) {
      final distance = tower.distanceTo(latitude, longitude);
      return distance <= maxDistance;
    }).toList();
  }

  // جلب جميع الأبراج في منطقة التغطية مع ترتيب حسب المسافة
  Future<List<TowerModel>> getTowersInCoverageArea(double latitude, double longitude, String adminId) async {
    final towers = await getTowersByAdmin(adminId);
    
    // فلترة الأبراج التي تغطي الموقع المحدد
    final coveringTowers = towers.where((tower) {
      return tower.isWithinCoverage(latitude, longitude);
    }).toList();
    
    // ترتيب الأبراج حسب المسافة (الأقرب أولاً)
    coveringTowers.sort((a, b) {
      final distanceA = a.distanceTo(latitude, longitude);
      final distanceB = b.distanceTo(latitude, longitude);
      return distanceA.compareTo(distanceB);
    });
    
    return coveringTowers;
  }

  // البحث عن الأبراج القريبة مع معلومات مفصلة
  Future<Map<String, dynamic>> searchNearbyTowers(double latitude, double longitude, String adminId) async {
    final allTowers = await getTowersByAdmin(adminId);
    final results = <Map<String, dynamic>>[];
    
    for (final tower in allTowers) {
      final distance = tower.distanceTo(latitude, longitude);
      final isInCoverage = tower.isWithinCoverage(latitude, longitude);
      
      results.add({
        'tower': tower,
        'distance': distance,
        'isInCoverage': isInCoverage,
        'coverageRadius': tower.coverageRadius,
      });
    }
    
    // ترتيب النتائج حسب المسافة
    results.sort((a, b) => (a['distance'] as double).compareTo(b['distance'] as double));
    
    return {
      'towers': results,
      'totalTowers': allTowers.length,
      'coveringTowers': results.where((r) => r['isInCoverage'] as bool).length,
      'nearestTower': results.isNotEmpty ? results.first['tower'] : null,
      'nearestDistance': results.isNotEmpty ? results.first['distance'] : null,
    };
  }

  // إنشاء معرف فريد
  String generateId() {
    return const Uuid().v4();
  }

  // جلب إحصائيات الطلبات
  Future<Map<String, int>> getRequestStats(String adminId) async {
    final requests = await getServiceRequestsByAdmin(adminId);
    
    final stats = <String, int>{};
    for (final status in RequestStatus.values) {
      stats[status.name] = requests.where((r) => r.status == status).length;
    }
    
    return stats;
  }
} 