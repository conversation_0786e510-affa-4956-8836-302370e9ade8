import 'package:cloud_firestore/cloud_firestore.dart';

class FirebaseAppSettingsService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// إنشاء رقم واتساب افتراضي (واحد فقط)
  static Future<void> ensureDefaultWhatsAppNumber() async {
    final docRef = _firestore.collection('whatsapp_numbers').doc('main');
    final doc = await docRef.get();
    if (!doc.exists) {
      await docRef.set({
        'number': '+9647700000000', // ضع هنا الرقم الافتراضي الذي تريده
        'is_active': true,
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });
      print('✅ تم إنشاء رقم واتساب افتراضي');
    } else {
      print('رقم الواتساب الافتراضي موجود بالفعل');
    }
  }

  /// جلب رقم الواتساب الافتراضي
  static Future<String?> getDefaultWhatsAppNumber() async {
    final doc = await _firestore.collection('whatsapp_numbers').doc('main').get();
    return doc.data()?['number'] as String?;
  }

  /// إنشاء إعدادات التحديث الإجباري الافتراضية (واحدة فقط)
  static Future<void> ensureDefaultUpdateSettings() async {
    final docRef = _firestore.collection('update_settings').doc('main');
    final doc = await docRef.get();
    if (!doc.exists) {
      await docRef.set({
        'is_active': true,
        'version': '1.0.0', // ضع هنا رقم الإصدار الافتراضي
        'message': 'يرجى تحديث التطبيق إلى آخر إصدار.',
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });
      print('✅ تم إنشاء إعدادات التحديث الافتراضية');
    } else {
      print('إعدادات التحديث الافتراضية موجودة بالفعل');
    }
  }

  /// جلب إعدادات التحديث الإجباري الافتراضية
  static Future<Map<String, dynamic>?> getDefaultUpdateSettings() async {
    final doc = await _firestore.collection('update_settings').doc('main').get();
    return doc.data();
  }
} 