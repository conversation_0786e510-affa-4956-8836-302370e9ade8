import 'dart:async';
import 'dart:convert';
import 'package:isp_manager/models/mikrotik_device_model.dart';
import 'package:router_os_client/router_os_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MikrotikApiService {
  final MikrotikDeviceModel device;
  late RouterOSClient client;

  MikrotikApiService({required this.device}) {
    // إنشاء العميل مع المعلمات الصحيحة
    client = RouterOSClient(
      address: device.host,
      user: device.username,
      password: device.password,
      useSsl: false,
      verbose: false,
    );
  }

  Future<void> connect() async {
    try {
      // استخدام الدالة الصحيحة للاتصال
      bool loginSuccess = await client.login();
      if (!loginSuccess) {
        throw Exception('فشل تسجيل الدخول إلى جهاز MikroTik');
      }
    } catch (e) {
      throw Exception('فشل الاتصال بجهاز MikroTik: $e');
    }
  }

  Future<void> disconnect() async {
    try {
      // استخدام الدالة الصحيحة للقطع
      client.close();
    } catch (e) {
      print('خطأ في قطع الاتصال بجهاز MikroTik: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getActiveHotspotUsers() async {
    try {
      await connect();
      final response = await client.talk(['/ip/hotspot/active/print']);
      return response.map((e) => e.map((k, v) => MapEntry(k, v))).toList();
    } catch (e) {
      print('خطأ في جلب المستخدمين النشطين في Hotspot: $e');
      throw Exception('فشل في جلب المستخدمين النشطين في Hotspot: $e');
    } finally {
      await disconnect();
    }
  }

  Future<List<Map<String, dynamic>>> getUsers() async {
    try {
      await connect();
      final response = await client.talk(['/user', 'print', '=detail']);
      return response.map((e) => e.map((k, v) => MapEntry(k, v))).toList();
    } catch (e) {
      print('خطأ في جلب المستخدمين: $e');
      throw Exception('فشل في جلب المستخدمين: $e');
    } finally {
      await disconnect();
    }
  }

  Future<Map<String, dynamic>> getSystemResources() async {
    try {
      await connect();
      final response = await client.talk(['/system/resource/print']);
      if (response.isEmpty) {
        throw Exception('لم يتم استلام بيانات موارد النظام');
      }
      return response.first.map((k, v) => MapEntry(k, v));
    } catch (e) {
      print('خطأ في جلب موارد النظام: $e');
      throw Exception('فشل في جلب موارد النظام: $e');
    } finally {
      await disconnect();
    }
  }

  Future<List<Map<String, dynamic>>> monitorInterfaceTraffic(String interface) async {
    try {
      await connect();
      final response = await client.talk([
        '/interface/monitor-traffic',
        '=interface=$interface',
        '=once='
      ]);
      return response.map((e) => e.map((k, v) => MapEntry(k, v))).toList();
    } catch (e) {
      print('خطأ في مراقبة حركة المرور على الواجهة: $e');
      throw Exception('فشل في مراقبة حركة المرور على الواجهة: $e');
    } finally {
      await disconnect();
    }
  }

  Future<List<Map<String, dynamic>>> pingAddress(String address) async {
    try {
      await connect();
      final response = await client.talk([
        '/ping',
        '=address=$address',
        '=count=4'
      ]);
      return response.map((e) => e.map((k, v) => MapEntry(k, v))).toList();
    } catch (e) {
      print('خطأ في ping العنوان: $e');
      throw Exception('فشل في ping العنوان: $e');
    } finally {
      await disconnect();
    }
  }

  // الحصول على إحصائيات حركة المرور
  Future<Map<String, dynamic>> getTrafficStatistics({String timeRange = 'day'}) async {
    try {
      await connect();
      final response = await client.talk([
        '/interface/traffic-monitor/print',
        '=once='
      ]);
      
      if (response.isEmpty) {
        throw Exception('لم يتم استلام بيانات حركة المرور');
      }
      
      return response.first.map((k, v) => MapEntry(k, v));
    } catch (e) {
      print('خطأ في جلب إحصائيات حركة المرور: $e');
      throw Exception('فشل في جلب إحصائيات حركة المرور: $e');
    } finally {
      await disconnect();
    }
  }

  // الحصول على قائمة الواجهات المتاحة
  Future<List<Map<String, dynamic>>> getInterfaces() async {
    try {
      await connect();
      final response = await client.talk(['/interface/print']);
      return response.map((e) => e.map((k, v) => MapEntry(k, v))).toList();
    } catch (e) {
      print('خطأ في جلب قائمة الواجهات: $e');
      throw Exception('فشل في جلب قائمة الواجهات: $e');
    } finally {
      await disconnect();
    }
  }

  // الحصول على إعدادات الأمان
  Future<Map<String, dynamic>> getSecuritySettings() async {
    try {
      await connect();
      final response = await client.talk(['/ip/firewall/print']);
      
      if (response.isEmpty) {
        return {'message': 'لا يوجد إعدادات أمان مكتوبة'};
      }
      
      return response.first.map((k, v) => MapEntry(k, v));
    } catch (e) {
      print('خطأ في جلب إعدادات الأمان: $e');
      throw Exception('فشل في جلب إعدادات الأمان: $e');
    } finally {
      await disconnect();
    }
  }

  // الحصول على معلومات الشبكة
  Future<Map<String, dynamic>> getNetworkInfo() async {
    try {
      await connect();
      final response = await client.talk(['/ip/address/print']);
      
      if (response.isEmpty) {
        throw Exception('لم يتم استلام معلومات الشبكة');
      }
      
      return {
        'addresses': response.map((e) => e.map((k, v) => MapEntry(k, v))).toList(),
        'interfaces': await getInterfaces(),
      };
    } catch (e) {
      print('خطأ في جلب معلومات الشبكة: $e');
      throw Exception('فشل في جلب معلومات الشبكة: $e');
    } finally {
      await disconnect();
    }
  }

  // الحصول على سجلات النظام
  Future<List<Map<String, dynamic>>> getSystemLogs({int lines = 100}) async {
    try {
      await connect();
      final response = await client.talk([
        '/log/print',
        '=count=$lines',
        '=follow=false'
      ]);
      return response.map((e) => e.map((k, v) => MapEntry(k, v))).toList();
    } catch (e) {
      print('خطأ في جلب سجلات النظام: $e');
      throw Exception('فشل في جلب سجلات النظام: $e');
    } finally {
      await disconnect();
    }
  }

  // الحصول على إحصائيات الذاكرة
  Future<Map<String, dynamic>> getMemoryStats() async {
    try {
      await connect();
      final response = await client.talk(['/memory/print']);
      
      if (response.isEmpty) {
        throw Exception('لم يتم استلام بيانات الذاكرة');
      }
      
      return response.first.map((k, v) => MapEntry(k, v));
    } catch (e) {
      print('خطأ في جلب بيانات الذاكرة: $e');
      throw Exception('فشل في جلب بيانات الذاكرة: $e');
    } finally {
      await disconnect();
    }
  }
}
