import 'package:flutter/material.dart';
import '../services/database_service.dart';
import '../models/activity_log_model.dart';
import '../services/firebase_auth_service.dart';
import '../models/user_model.dart';

class ActivityLogPage extends StatefulWidget {
  const ActivityLogPage({super.key});

  @override
  State<ActivityLogPage> createState() => _ActivityLogPageState();
}

class _ActivityLogPageState extends State<ActivityLogPage> {
  List<ActivityLogModel> _logs = [];
  Map<String, String> _subscriberNames = {};
  bool _isLoading = true;
  UserModel? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadUser();
    _loadLogs();
  }

  Future<void> _loadUser() async {
    final authService = FirebaseAuthService();
    final firebaseUser = authService.currentUser;
    if (firebaseUser != null) {
      final userData = await authService.getUserData(firebaseUser.uid);
      setState(() {
        _currentUser = userData;
      });
    }
  }

  Future<void> _loadLogs() async {
    setState(() => _isLoading = true);
    final logs = await DatabaseService().getActivityLogsFire();
    await DatabaseService().syncActivityToFirebase();
    logs.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    // جلب أسماء المشتركين دفعة واحدة
    final subscribers = await DatabaseService().getSubscribers();
    final namesMap = {for (var s in subscribers) s.id: s.fullName};
    setState(() {
      _logs = logs;
      _subscriberNames = namesMap;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!(_currentUser?.hasPermission(Permission.viewActivityLogs) ?? false)) {
      return Scaffold(
        appBar: AppBar(title: const Text('سجل النشاطات')),
        body: const Center(child: Text('ليس لديك صلاحية لعرض سجل النشاطات')),
      );
    }
    return Scaffold(
      appBar: AppBar(
        title: const Text('سجل العمليات لجميع المشتركين'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _logs.isEmpty
          ? _buildEmptyState(context)
          : RefreshIndicator(
              onRefresh: _loadLogs,
              child: ListView.builder(
                padding: const EdgeInsets.all(20),
                itemCount: _logs.length,
                itemBuilder: (context, index) {
                  final log = _logs[index];
                  return _buildLogCard(context, log, index);
                },
              ),
            ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد أي عمليات مسجلة بعد.',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogCard(BuildContext context, ActivityLogModel log, int index) {
    final color = Theme.of(
      context,
    ).colorScheme.primary.withOpacity(0.08 + (index % 2) * 0.04);
    final iconData = _getActionIcon(log.actionType);
    final iconColor = _getActionColor(context, log.actionType);
    final subscriberName = _subscriberNames[log.subscriberId] ?? 'غير معروف';
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 10),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(18),
          gradient: LinearGradient(
            colors: [color, Colors.white],
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
          ),
        ),
        child: ListTile(
          leading: CircleAvatar(
            backgroundColor: iconColor.withOpacity(0.15),
            radius: 26,
            child: Icon(iconData, color: iconColor, size: 28),
          ),
          title: Text(
            log.actionDescription,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 6),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      size: 16,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        subscriberName,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.calendar_today, size: 15, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      _formatDate(log.timestamp),
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                FutureBuilder<String?>(
                  future: log.getDetailsWithCurrency(),
                  builder: (context, snapshot) {
                    final details = snapshot.data;
                    if (details != null && details.isNotEmpty) {
                      return Column(
                        children: [
                          const SizedBox(height: 8),
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Theme.of(
                                context,
                              ).colorScheme.surfaceContainerHighest.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              details,
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onSurface.withOpacity(0.8),
                                  ),
                            ),
                          ),
                        ],
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
          trailing: AnimatedContainer(
            duration: const Duration(milliseconds: 400),
            curve: Curves.easeInOut,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.12),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              _getActionLabel(log.actionType),
              style: TextStyle(
                color: iconColor,
                fontWeight: FontWeight.bold,
                fontSize: 13,
              ),
            ),
          ),
        ),
      ),
    );
  }

  IconData _getActionIcon(String? type) {
    switch (type) {
      case 'add':
        return Icons.add_circle_outline;
      case 'edit':
        return Icons.edit_outlined;
      case 'delete':
        return Icons.delete_outline;
      case 'payment':
        return Icons.attach_money;
      case 'renew':
        return Icons.refresh;
      default:
        return Icons.info_outline;
    }
  }

  Color _getActionColor(BuildContext context, String? type) {
    switch (type) {
      case 'add':
        return Colors.green;
      case 'edit':
        return Colors.blue;
      case 'delete':
        return Colors.red;
      case 'payment':
        return Colors.orange;
      case 'renew':
        return Colors.teal;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  String _getActionLabel(String? type) {
    switch (type) {
      case 'add':
        return 'إضافة';
      case 'edit':
        return 'تعديل';
      case 'delete':
        return 'حذف';
      case 'payment':
        return 'دفع';
      case 'renew':
        return 'تجديد';
      default:
        return 'عملية';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')} - ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}
