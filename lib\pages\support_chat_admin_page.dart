import 'package:flutter/material.dart';
import '../models/support_chat_model.dart';
import '../models/support_message_model.dart';
import '../services/support_chat_service.dart';
import 'package:uuid/uuid.dart';

class SupportChatAdminPage extends StatefulWidget {
  final SupportChatModel chat;
  final String subscriberName;
  final String subscriberPhone;
  const SupportChatAdminPage({Key? key, required this.chat, required this.subscriberName, required this.subscriberPhone}) : super(key: key);

  @override
  State<SupportChatAdminPage> createState() => _SupportChatAdminPageState();
}

class _SupportChatAdminPageState extends State<SupportChatAdminPage> {
  final SupportChatService _chatService = SupportChatService();
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _sending = false;

  void _scrollToEnd() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendMessage() async {
    if (_controller.text.trim().isEmpty || _sending) return;
    setState(() => _sending = true);
    final msg = SupportMessageModel(
      id: const Uuid().v4(),
      chatId: widget.chat.id,
      senderId: widget.chat.adminId,
      senderType: 'admin',
      text: _controller.text.trim(),
      timestamp: DateTime.now(),
      isRead: false,
    );
    await _chatService.sendMessage(
      chatId: widget.chat.id,
      message: msg,
      lastSender: widget.chat.adminId,
    );
    _controller.clear();
    setState(() => _sending = false);
    _scrollToEnd();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            CircleAvatar(
              backgroundColor: Colors.blue.shade100,
              child: const Icon(Icons.person, color: Colors.blue),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(widget.subscriberName, style: theme.textTheme.titleMedium),
                  Text(widget.subscriberPhone, style: theme.textTheme.bodySmall?.copyWith(color: Colors.grey)),
                ],
              ),
            ),
          ],
        ),
        actions: [
          if (widget.chat.isOpen)
            IconButton(
              icon: const Icon(Icons.lock_outline, color: Colors.red),
              tooltip: 'إغلاق المحادثة',
              onPressed: () async {
                final confirm = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('تأكيد إغلاق المحادثة'),
                    content: const Text('هل أنت متأكد أنك تريد إغلاق هذه المحادثة؟ لن يتمكن أي طرف من إرسال رسائل جديدة.'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('إلغاء'),
                      ),
                      ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                        child: const Text('إغلاق'),
                      ),
                    ],
                  ),
                );
                if (confirm == true) {
                  await SupportChatService.firebaseFirestore
                      .collection(SupportChatService().chatsCollection)
                      .doc(widget.chat.id)
                      .update({'isOpen': false});
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم إغلاق المحادثة.')),
                    );
                    Navigator.of(context).pop();
                  }
                }
              },
            ),
        ],
        backgroundColor: theme.colorScheme.surface,
      ),
      body: Column(
        children: [
          if (!widget.chat.isOpen)
            Container(
              width: double.infinity,
              color: Colors.red.shade50,
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  const Icon(Icons.lock, color: Colors.red),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text('تم إغلاق هذه المحادثة. لا يمكن إرسال رسائل جديدة.', style: TextStyle(color: Colors.red)),
                  ),
                ],
              ),
            ),
          Expanded(
            child: StreamBuilder<List<SupportMessageModel>>(
              stream: _chatService.getMessagesStream(widget.chat.id),
              builder: (context, snapshot) {
                final messages = snapshot.data ?? [];
                WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToEnd());
                return Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [theme.colorScheme.surface, theme.colorScheme.primary.withOpacity(0.04)],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
                    itemCount: messages.length,
                    itemBuilder: (context, i) {
                      final msg = messages[i];
                      final isMe = msg.senderType == 'admin';
                      final showAvatar = i == 0 || messages[i - 1].senderId != msg.senderId;
                      return Column(
                        crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                        children: [
                          if (showAvatar)
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                              child: Row(
                                mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
                                children: [
                                  if (!isMe)
                                    CircleAvatar(
                                      backgroundColor: Colors.green.shade100,
                                      child: const Icon(Icons.person, color: Colors.green),
                                    ),
                                  if (isMe)
                                    CircleAvatar(
                                      backgroundColor: Colors.blue.shade100,
                                      child: const Icon(Icons.support_agent, color: Colors.blue),
                                    ),
                                  const SizedBox(width: 8),
                                  Text(
                                    isMe ? 'أنت (الدعم)' : 'المشترك',
                                    style: theme.textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                            ),
                          Align(
                            alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
                            child: Container(
                              margin: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 14),
                              constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.75),
                              decoration: BoxDecoration(
                                color: isMe
                                    ? theme.colorScheme.primary.withOpacity(0.15)
                                    : theme.colorScheme.surfaceVariant,
                                borderRadius: BorderRadius.only(
                                  topLeft: const Radius.circular(16),
                                  topRight: const Radius.circular(16),
                                  bottomLeft: Radius.circular(isMe ? 16 : 4),
                                  bottomRight: Radius.circular(isMe ? 4 : 16),
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    msg.text,
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(Icons.access_time, size: 12, color: Colors.grey.shade500),
                                      const SizedBox(width: 2),
                                      Text(
                                        _formatTime(msg.timestamp),
                                        style: theme.textTheme.labelSmall?.copyWith(color: Colors.grey.shade500),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                );
              },
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.primary.withOpacity(0.07),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              top: false,
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _controller,
                      minLines: 1,
                      maxLines: 4,
                      decoration: const InputDecoration(
                        hintText: 'اكتب ردك هنا...'
                      ),
                      onSubmitted: (_) => widget.chat.isOpen ? _sendMessage() : null,
                      enabled: widget.chat.isOpen,
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: _sending
                        ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2))
                        : const Icon(Icons.send, color: Colors.blue),
                    onPressed: _sending || !widget.chat.isOpen ? null : _sendMessage,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final min = time.minute.toString().padLeft(2, '0');
    return '$hour:$min';
  }
} 