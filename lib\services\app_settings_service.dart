// App Settings Service for managing currency and country preferences
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/app_settings_model.dart';

class AppSettingsService {
  static const String _settingsKey = 'app_settings';
  static AppSettings? _cachedSettings;

  // Get current app settings
  static Future<AppSettings> getSettings() async {
    if (_cachedSettings != null) {
      return _cachedSettings!;
    }

    final prefs = await SharedPreferences.getInstance();
    final settingsJson = prefs.getString(_settingsKey);
    
    if (settingsJson != null) {
      try {
        final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
        _cachedSettings = AppSettings.fromMap(settingsMap);
        return _cachedSettings!;
      } catch (e) {
        // If there's an error parsing, return default settings
        _cachedSettings = AppSettings.defaultSettings();
        await saveSettings(_cachedSettings!);
        return _cachedSettings!;
      }
    }

    // If no settings found, create default settings
    _cachedSettings = AppSettings.defaultSettings();
    await saveSettings(_cachedSettings!);
    return _cachedSettings!;
  }

  // Save app settings
  static Future<void> saveSettings(AppSettings settings) async {
    final prefs = await SharedPreferences.getInstance();
    final settingsJson = json.encode(settings.toMap());
    await prefs.setString(_settingsKey, settingsJson);
    _cachedSettings = settings;
  }

  // Update currency settings
  static Future<void> updateCurrency({
    required String currencyCode,
    required String currencySymbol,
    required String currencyName,
  }) async {
    final currentSettings = await getSettings();
    final updatedSettings = currentSettings.copyWith(
      currencyCode: currencyCode,
      currencySymbol: currencySymbol,
      currencyName: currencyName,
    );
    await saveSettings(updatedSettings);
  }

  // Update country settings
  static Future<void> updateCountry({
    required String countryCode,
    required String countryName,
    required String countryFlag,
    required String phoneCode,
  }) async {
    final currentSettings = await getSettings();
    final updatedSettings = currentSettings.copyWith(
      countryCode: countryCode,
      countryName: countryName,
      countryFlag: countryFlag,
      phoneCode: phoneCode,
    );
    await saveSettings(updatedSettings);
  }

  // Reset to default settings
  static Future<void> resetToDefault() async {
    _cachedSettings = AppSettings.defaultSettings();
    await saveSettings(_cachedSettings!);
  }

  // Helper methods for common use cases
  static Future<String> getCurrencySymbol() async {
    final settings = await getSettings();
    return settings.currencySymbol;
  }

  static Future<String> getCurrencyCode() async {
    final settings = await getSettings();
    return settings.currencyCode;
  }

  static Future<String> getPhoneCode() async {
    final settings = await getSettings();
    return settings.phoneCode;
  }

  static Future<String> getCountryCode() async {
    final settings = await getSettings();
    return settings.countryCode;
  }

  static Future<String> getCountryFlag() async {
    final settings = await getSettings();
    return settings.countryFlag;
  }

  // Format currency amount with symbol
  static Future<String> formatCurrency(double amount) async {
    final settings = await getSettings();
    return '${amount.toStringAsFixed(0)} ${settings.currencySymbol}';
  }

  // Format currency amount with symbol (synchronous version)
  static String formatCurrencySync(double amount, String currencySymbol) {
    return '${amount.toStringAsFixed(0)} $currencySymbol';
  }

  // Get formatted currency for packages
  static Future<String> formatPackagePrice(double price) async {
    final settings = await getSettings();
    return formatCurrencySync(price, settings.currencySymbol);
  }
  // Format phone number with country code
  static Future<String> formatPhoneNumber(String phoneNumber) async {
    final settings = await getSettings();
    
    // بناء pattern لإزالة مفتاح الدولة الحالي من الإعدادات
    final cleanPhoneCode = settings.phoneCode.replaceAll('+', '');
    final regexPattern = '^(\\+$cleanPhoneCode|$cleanPhoneCode|0)';
    
    // Remove any existing country code or leading zeros
    String cleanNumber = phoneNumber.replaceAll(RegExp(regexPattern), '');
    return '${settings.phoneCode}$cleanNumber';
  }
}
