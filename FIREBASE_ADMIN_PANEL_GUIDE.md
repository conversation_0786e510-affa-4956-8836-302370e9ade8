# دليل لوحة تحكم Firebase - نظام إدارة مزودي الإنترنت

## نظرة عامة

بعد نقل النظام من Supabase إلى Firebase، تم إنشاء لوحة تحكم شاملة لإدارة:
- **الاشتراكات** (Device Subscriptions)
- **الباقات والخدمات** (Packages & Services)
- **تحديثات التطبيق** (App Updates)
- **إعدادات WhatsApp** (WhatsApp Settings)

---

## 1. إعداد Firebase Console

### 1.1 إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع"
3. أدخل اسم المشروع: `isp-manager`
4. اختر "تمكين Google Analytics" (اختياري)
5. انقر "إنشاء مشروع"

### 1.2 إعداد Firestore Database
1. في القائمة الجانبية، اختر "Firestore Database"
2. انقر "إنشاء قاعدة بيانات"
3. اختر "بدء في وضع الاختبار"
4. اختر موقع قاعدة البيانات (يفضل `europe-west1` للشرق الأوسط)

### 1.3 إعداد Authentication
1. اختر "Authentication" من القائمة
2. انقر "بدء"
3. اختر "البريد الإلكتروني/كلمة المرور"
4. انقر "تمكين"

---

## 2. هيكل قاعدة البيانات

### 2.1 مجموعات البيانات (Collections)

#### A. `device_subscriptions`
```json
{
  "id": "auto-generated",
  "adminId": "admin_user_id",
  "deviceId": "unique_device_identifier",
  "packageId": "package_reference",
  "packageName": "اسم الباقة",
  "price": 50000,
  "currency": "د.ع",
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-12-31T23:59:59Z",
  "status": "active",
  "autoRenew": true,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

#### B. `app_subscription_packages`
```json
{
  "id": "auto-generated",
  "adminId": "admin_user_id",
  "name": "باقة الإنترنت الأساسية",
  "description": "وصف الباقة",
  "price": 50000,
  "currency": "د.ع",
  "duration": 30,
  "durationUnit": "days",
  "speed": "10 Mbps",
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

#### C. `app_updates`
```json
{
  "id": "auto-generated",
  "version": "1.0.0",
  "buildNumber": 1,
  "title": "تحديث جديد",
  "description": "وصف التحديث",
  "downloadUrl": "https://example.com/app.apk",
  "isForceUpdate": false,
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00Z"
}
```

#### D. `whatsapp_settings`
```json
{
  "id": "auto-generated",
  "adminId": "admin_user_id",
  "apiKey": "your_whatsapp_api_key",
  "phoneNumber": "+964700000000",
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

---

## 3. الخدمات والوظائف

### 3.1 FirebaseSubscriptionService

#### الوظائف الرئيسية:

```dart
// إنشاء اشتراك جديد
Future<void> createSubscription(DeviceSubscriptionModel subscription)

// جلب جميع اشتراكات المدير
Future<List<DeviceSubscriptionModel>> getSubscriptionsByAdmin(String adminId)

// جلب اشتراك محدد
Future<DeviceSubscriptionModel?> getSubscriptionById(String id)

// تحديث اشتراك
Future<void> updateSubscription(DeviceSubscriptionModel subscription)

// حذف اشتراك
Future<void> deleteSubscription(String id)

// تجديد اشتراك
Future<void> renewSubscription(String id, int durationDays)

// جلب الاشتراكات المنتهية
Future<List<DeviceSubscriptionModel>> getExpiredSubscriptions(String adminId)

// جلب الاشتراكات النشطة
Future<List<DeviceSubscriptionModel>> getActiveSubscriptions(String adminId)
```

#### مثال على الاستخدام:
```dart
final subscriptionService = FirebaseSubscriptionService();

// إنشاء اشتراك جديد
final subscription = DeviceSubscriptionModel(
  adminId: 'admin123',
  deviceId: 'device456',
  packageId: 'package789',
  packageName: 'باقة الإنترنت الأساسية',
  price: 50000,
  currency: 'د.ع',
  startDate: DateTime.now(),
  endDate: DateTime.now().add(Duration(days: 30)),
  status: 'active',
  autoRenew: true,
);

await subscriptionService.createSubscription(subscription);
```

### 3.2 FirebasePackagesService

#### الوظائف الرئيسية:

```dart
// إنشاء باقة جديدة
Future<void> createPackage(PackageModel package)

// جلب جميع باقات المدير
Future<List<PackageModel>> getPackagesByAdmin(String adminId)

// جلب باقة محددة
Future<PackageModel?> getPackageById(String id)

// تحديث باقة
Future<void> updatePackage(PackageModel package)

// حذف باقة
Future<void> deletePackage(String id)

// تفعيل/إلغاء تفعيل باقة
Future<void> togglePackageStatus(String id, bool isActive)
```

#### مثال على الاستخدام:
```dart
final packagesService = FirebasePackagesService();

// إنشاء باقة جديدة
final package = PackageModel(
  adminId: 'admin123',
  name: 'باقة الإنترنت المتقدمة',
  description: 'باقة إنترنت عالية السرعة',
  price: 75000,
  currency: 'د.ع',
  duration: 30,
  durationUnit: 'days',
  speed: '25 Mbps',
  isActive: true,
);

await packagesService.createPackage(package);
```

### 3.3 FirebaseUpdateService

#### الوظائف الرئيسية:

```dart
// إنشاء تحديث جديد
Future<void> createUpdate(AppUpdateModel update)

// جلب آخر تحديث
Future<AppUpdateModel?> getLatestUpdate()

// جلب جميع التحديثات
Future<List<AppUpdateModel>> getAllUpdates()

// تحديث معلومات التحديث
Future<void> updateAppUpdate(AppUpdateModel update)

// حذف تحديث
Future<void> deleteUpdate(String id)

// تفعيل/إلغاء تفعيل تحديث
Future<void> toggleUpdateStatus(String id, bool isActive)
```

#### مثال على الاستخدام:
```dart
final updateService = FirebaseUpdateService();

// إنشاء تحديث جديد
final update = AppUpdateModel(
  version: '1.1.0',
  buildNumber: 2,
  title: 'تحديث جديد مع ميزات محسنة',
  description: 'إصلاحات وتحسينات جديدة',
  downloadUrl: 'https://example.com/app-v1.1.0.apk',
  isForceUpdate: false,
  isActive: true,
);

await updateService.createUpdate(update);
```

### 3.4 FirebaseWhatsAppService

#### الوظائف الرئيسية:

```dart
// حفظ إعدادات WhatsApp
Future<void> saveWhatsAppSettings(WhatsAppSettingsModel settings)

// جلب إعدادات WhatsApp للمدير
Future<WhatsAppSettingsModel?> getWhatsAppSettings(String adminId)

// تحديث إعدادات WhatsApp
Future<void> updateWhatsAppSettings(WhatsAppSettingsModel settings)

// تفعيل/إلغاء تفعيل WhatsApp
Future<void> toggleWhatsAppStatus(String adminId, bool isActive)
```

#### مثال على الاستخدام:
```dart
final whatsappService = FirebaseWhatsAppService();

// حفظ إعدادات WhatsApp
final settings = WhatsAppSettingsModel(
  adminId: 'admin123',
  apiKey: 'your_whatsapp_api_key',
  phoneNumber: '+964700000000',
  isActive: true,
);

await whatsappService.saveWhatsAppSettings(settings);
```

---

## 4. إدارة البيانات من Firebase Console

### 4.1 إدارة الاشتراكات

1. **عرض الاشتراكات:**
   - اذهب إلى Firestore Database
   - اختر مجموعة `device_subscriptions`
   - ستجد جميع الاشتراكات مع تفاصيلها

2. **إضافة اشتراك جديد:**
   - انقر على "إضافة مستند"
   - أدخل البيانات المطلوبة
   - انقر "حفظ"

3. **تعديل اشتراك:**
   - انقر على المستند المطلوب
   - عدل البيانات
   - انقر "تحديث"

4. **حذف اشتراك:**
   - انقر على المستند
   - انقر على أيقونة الحذف
   - أكد الحذف

### 4.2 إدارة الباقات

1. **عرض الباقات:**
   - اختر مجموعة `app_subscription_packages`
   - ستجد جميع الباقات المتاحة

2. **إضافة باقة جديدة:**
   - انقر "إضافة مستند"
   - أدخل:
     - `name`: اسم الباقة
     - `description`: وصف الباقة
     - `price`: السعر
     - `currency`: العملة
     - `duration`: المدة بالأيام
     - `speed`: السرعة
     - `isActive`: true/false

3. **تعديل باقة:**
   - انقر على الباقة المطلوبة
   - عدل البيانات
   - احفظ التغييرات

### 4.3 إدارة التحديثات

1. **عرض التحديثات:**
   - اختر مجموعة `app_updates`
   - ستجد جميع إصدارات التطبيق

2. **إضافة تحديث جديد:**
   - انقر "إضافة مستند"
   - أدخل:
     - `version`: رقم الإصدار (مثل "1.0.0")
     - `buildNumber`: رقم البناء
     - `title`: عنوان التحديث
     - `description`: وصف التحديث
     - `downloadUrl`: رابط التحميل
     - `isForceUpdate`: true/false
     - `isActive`: true/false

### 4.4 إدارة إعدادات WhatsApp

1. **عرض الإعدادات:**
   - اختر مجموعة `whatsapp_settings`
   - ستجد إعدادات WhatsApp لكل مدير

2. **إضافة إعدادات جديدة:**
   - انقر "إضافة مستند"
   - أدخل:
     - `adminId`: معرف المدير
     - `apiKey`: مفتاح API الخاص بـ WhatsApp
     - `phoneNumber`: رقم الهاتف
     - `isActive`: true/false

---

## 🔄 تحديث منطق باقات الاشتراك الافتراضية

- **أسماء الباقات الافتراضية:**
  - الباقة الشهرية
  - الباقة نصف السنوية
  - الباقة السنوية

- **كل باقة افتراضية تحتوي على:**
  - `details`: وصف مختصر
  - `features`: قائمة مميزات

- **منطق الإنشاء:**
  - يتم التحقق من وجود الباقات الافتراضية الثلاثة عند الحاجة.
  - إذا كانت أي باقة ناقصة، يتم إنشاؤها تلقائيًا.
  - لا يتم حذف أي باقة موجودة (مخصصة أو قديمة)، ولا تتكرر الباقات الافتراضية.

## مثال بيانات باقة افتراضية (Firestore):

```json
{
  "name": "الباقة الشهرية",
  "duration_days": 30,
  "price": 5000,
  "is_active": true,
  "is_recommended": true,
  "details": "أفضل خيار للتجربة أو الاستخدام القصير.",
  "features": [
    "دعم فني سريع",
    "تحديثات مجانية",
    "إمكانية الترقية في أي وقت"
  ]
}
```

---

## 5. قواعد الأمان (Security Rules)

### 5.1 قواعد Firestore

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد الاشتراكات
    match /device_subscriptions/{document} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.adminId;
    }
    
    // قواعد الباقات
    match /app_subscription_packages/{document} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.adminId;
    }
    
    // قواعد التحديثات (للقراءة فقط)
    match /app_updates/{document} {
      allow read: if true;
      allow write: if request.auth != null && 
        request.auth.token.admin == true;
    }
    
    // قواعد إعدادات WhatsApp
    match /whatsapp_settings/{document} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.adminId;
    }
  }
}
```

### 5.2 تطبيق القواعد

1. اذهب إلى Firestore Database
2. انقر على تبويب "Rules"
3. انسخ والصق القواعد أعلاه
4. انقر "Publish"

---

## 6. مراقبة الأداء والاستخدام

### 6.1 إحصائيات الاستخدام

1. **عرض الإحصائيات:**
   - اذهب إلى "Usage" في Firebase Console
   - ستجد إحصائيات القراءة والكتابة

2. **مراقبة الأداء:**
   - اذهب إلى "Performance"
   - راقب سرعة الاستعلامات

### 6.2 سجلات الأخطاء

1. **عرض السجلات:**
   - اذهب إلى "Crashlytics"
   - ستجد تقارير الأخطاء

---

## 7. النسخ الاحتياطي والاستعادة

### 7.1 تصدير البيانات

```bash
# تصدير جميع البيانات
gcloud firestore export gs://your-bucket/backup

# تصدير مجموعة محددة
gcloud firestore export gs://your-bucket/backup --collection-ids=device_subscriptions,app_subscription_packages
```

### 7.2 استيراد البيانات

```bash
# استيراد البيانات
gcloud firestore import gs://your-bucket/backup
```

---

## 8. أفضل الممارسات

### 8.1 تنظيم البيانات

1. **استخدم معرفات فريدة:**
   - استخدم `adminId` لربط البيانات بالمدير
   - استخدم timestamps للتواريخ

2. **تحسين الاستعلامات:**
   - استخدم الفهارس (Indexes) للاستعلامات المعقدة
   - تجنب الاستعلامات المكلفة

### 8.2 الأمان

1. **حماية البيانات:**
   - استخدم قواعد الأمان دائماً
   - تحقق من صلاحيات المستخدم

2. **إدارة المفاتيح:**
   - احفظ المفاتيح الحساسة في متغيرات البيئة
   - لا تشارك المفاتيح في الكود

### 8.3 الأداء

1. **تحسين الاستعلامات:**
   - استخدم `limit()` لتقييد النتائج
   - استخدم `where()` للفلترة

2. **إدارة الاتصالات:**
   - أغلق الاتصالات بعد الاستخدام
   - استخدم `listen()` للتحديثات المباشرة

---

## 9. استكشاف الأخطاء

### 9.1 الأخطاء الشائعة

1. **خطأ في الصلاحيات:**
   - تحقق من قواعد الأمان
   - تأكد من تسجيل الدخول

2. **خطأ في الاتصال:**
   - تحقق من إعدادات الشبكة
   - تأكد من تكوين Firebase

3. **خطأ في البيانات:**
   - تحقق من تنسيق البيانات
   - تأكد من وجود الحقول المطلوبة

### 9.2 أدوات التشخيص

1. **Firebase Debug View:**
   - اذهب إلى "Debug View"
   - راقب الأحداث في الوقت الفعلي

2. **سجلات Firebase:**
   - اذهب إلى "Logs"
   - ابحث عن الأخطاء

---

## 10. التحديثات المستقبلية

### 10.1 الميزات المقترحة

1. **نظام الإشعارات:**
   - إشعارات انتهاء الاشتراكات
   - إشعارات التحديثات الجديدة

2. **تقارير متقدمة:**
   - تقارير الإيرادات
   - إحصائيات الاستخدام

3. **تكامل مع خدمات أخرى:**
   - PayPal للدفع
   - SMS للإشعارات

### 10.2 التحسينات التقنية

1. **تحسين الأداء:**
   - استخدام Cloud Functions
   - تحسين الاستعلامات

2. **تحسين الأمان:**
   - تشفير البيانات الحساسة
   - تحسين قواعد الأمان

---

## خاتمة

هذا الدليل يغطي جميع جوانب لوحة تحكم Firebase بعد النقل من Supabase. تأكد من اتباع أفضل الممارسات للحصول على أداء وأمان مثاليين.

للمساعدة الإضافية، راجع:
- [وثائق Firebase](https://firebase.google.com/docs)
- [دليل Firestore](https://firebase.google.com/docs/firestore)
- [أفضل الممارسات](https://firebase.google.com/docs/firestore/best-practices) 