import 'dart:async';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

class FirebaseMessagingService {
  static final FirebaseMessagingService _instance = FirebaseMessagingService._internal();
  factory FirebaseMessagingService() => _instance;
  FirebaseMessagingService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  String? _fcmToken;
  StreamSubscription<RemoteMessage>? _messageSubscription;

  // Stream للاستماع للإشعارات
  final StreamController<RemoteMessage> _messageController = StreamController<RemoteMessage>.broadcast();
  Stream<RemoteMessage> get messageStream => _messageController.stream;

  // تهيئة الخدمة
  Future<void> initialize() async {
    try {
      print('==== [FCM] بدء تهيئة Firebase Messaging ====');
      
      // طلب الأذونات
      await _requestPermissions();
      
      // الحصول على FCM Token
      await _getFCMToken();
      
      // إعداد معالجات الإشعارات
      await _setupMessageHandlers();
      
      // إعداد معالج الإشعارات في الخلفية
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
      
      print('==== [FCM] تم تهيئة Firebase Messaging بنجاح ====');
    } catch (e) {
      print('==== [FCM] خطأ في تهيئة Firebase Messaging: $e ====');
    }
  }

  // طلب أذونات الإشعارات
  Future<void> _requestPermissions() async {
    try {
      NotificationSettings settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      print('==== [FCM] حالة أذونات الإشعارات: ${settings.authorizationStatus} ====');
      
      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('==== [FCM] تم منح أذونات الإشعارات ====');
      } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
        print('==== [FCM] تم منح أذونات الإشعارات المؤقتة ====');
      } else {
        print('==== [FCM] تم رفض أذونات الإشعارات ====');
      }
    } catch (e) {
      print('==== [FCM] خطأ في طلب أذونات الإشعارات: $e ====');
    }
  }

  // الحصول على FCM Token
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();
      print('==== [FCM] FCM Token: $_fcmToken ====');

      // الاستماع لتغييرات Token
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        print('==== [FCM] تم تحديث FCM Token: $newToken ====');
      });
    } catch (e) {
      print('==== [FCM] خطأ في الحصول على FCM Token: $e ====');
    }
  }

  // إعداد معالجات الإشعارات
  Future<void> _setupMessageHandlers() async {
    try {
      // معالج الإشعارات عندما يكون التطبيق مفتوح
      _messageSubscription = FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        print('==== [FCM] تم استلام إشعار في المقدمة ====');
        print('==== [FCM] البيانات: ${message.data} ====');
        print('==== [FCM] العنوان: ${message.notification?.title} ====');
        print('==== [FCM] المحتوى: ${message.notification?.body} ====');
        
        // إرسال الإشعار إلى Stream
        _messageController.add(message);
      });

      // معالج النقر على الإشعار عندما يكون التطبيق في الخلفية
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        print('==== [FCM] تم النقر على إشعار في الخلفية ====');
        print('==== [FCM] البيانات: ${message.data} ====');
        
        // إرسال الإشعار إلى Stream
        _messageController.add(message);
      });

      // معالج الإشعارات المفتوحة عند بدء التطبيق
      RemoteMessage? initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        print('==== [FCM] تم فتح التطبيق من إشعار ====');
        print('==== [FCM] البيانات: ${initialMessage.data} ====');
        
        // إرسال الإشعار إلى Stream
        _messageController.add(initialMessage);
      }

      print('==== [FCM] تم إعداد معالجات الإشعارات ====');
    } catch (e) {
      print('==== [FCM] خطأ في إعداد معالجات الإشعارات: $e ====');
    }
  }

  // الحصول على FCM Token الحالي
  String? get fcmToken => _fcmToken;

  // الاشتراك في موضوع معين
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      print('==== [FCM] تم الاشتراك في الموضوع: $topic ====');
    } catch (e) {
      print('==== [FCM] خطأ في الاشتراك في الموضوع: $e ====');
    }
  }

  // إلغاء الاشتراك من موضوع معين
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      print('==== [FCM] تم إلغاء الاشتراك من الموضوع: $topic ====');
    } catch (e) {
      print('==== [FCM] خطأ في إلغاء الاشتراك من الموضوع: $e ====');
    }
  }

  // تنظيف الموارد
  void dispose() {
    _messageSubscription?.cancel();
    _messageController.close();
  }
}

// معالج الإشعارات في الخلفية (يجب أن يكون دالة مستقلة)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print('==== [FCM] تم استلام إشعار في الخلفية ====');
  print('==== [FCM] البيانات: ${message.data} ====');
  print('==== [FCM] العنوان: ${message.notification?.title} ====');
  print('==== [FCM] المحتوى: ${message.notification?.body} ====');
} 