import 'package:flutter/material.dart';
import '../services/receipt_image_service.dart';
import '../services/printer_service.dart';
import '../models/printer_settings_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class ReceiptTestPage extends StatefulWidget {
  const ReceiptTestPage({super.key});

  @override
  State<ReceiptTestPage> createState() => _ReceiptTestPageState();
}

class _ReceiptTestPageState extends State<ReceiptTestPage> {
  bool _isLoading = false;
  String? _error;
  String? _success;

  // بيانات تجريبية للإيصال
  final Map<String, dynamic> _testData = {
    'companyInfo': 'شركة المنصة للخدمات الذكية',
    'operationType': 'تجديد اشتراك',
    'subscriberName': 'أحمد محمد علي',
    'subscriptionNumber': '12345678',
    'paymentAmount': '150',
    'dateTime': DateTime.now().toString().substring(0, 16),
    'employeeName': 'محمد أحمد',
  };
  // إعدادات الطابعة التجريبية
  PrinterSettingsModel _testSettings = PrinterSettingsModel(
    showCompanyInfo: true,
    showOperationType: true,
    showSubscriberName: true,
    showSubscriptionNumber: true,
    showPaymentAmount: true,
    showDateTime: true,
    showEmployeeName: true,
    companyName: 'شركة المنصة للخدمات الذكية',
    connectedDeviceAddress: null, // سيتم تعيينه لاحقاً
  );

  @override
  void initState() {
    super.initState();
    _loadPrinterSettings();
  }

  /// تحميل إعدادات الطابعة المحفوظة
  Future<void> _loadPrinterSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString('printer_settings');
      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson);
        setState(() {
          _testSettings = PrinterSettingsModel.fromJson(settingsMap);
        });
      }
    } catch (e) {
      // استخدام الإعدادات الافتراضية في حالة الخطأ
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار طباعة الإيصالات'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رسائل الحالة
                if (_error != null) ...[
                  Card(
                    color: Colors.red[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Icon(Icons.error, color: Colors.red[700]),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _error!,
                              style: TextStyle(color: Colors.red[700]),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                if (_success != null) ...[
                  Card(
                    color: Colors.green[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Icon(Icons.check_circle, color: Colors.green[700]),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _success!,
                              style: TextStyle(color: Colors.green[700]),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // إعدادات سريعة
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'إعدادات الإيصال',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // تحرير اسم الشركة
                        TextFormField(
                          initialValue: _testData['companyInfo'],
                          decoration: const InputDecoration(
                            labelText: 'اسم الشركة',
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (value) {
                            setState(() {
                              _testData['companyInfo'] = value;
                              _testSettings = _testSettings.copyWith(companyName: value);
                            });
                          },
                        ),
                        const SizedBox(height: 12),

                        // تحرير اسم المشترك
                        TextFormField(
                          initialValue: _testData['subscriberName'],
                          decoration: const InputDecoration(
                            labelText: 'اسم المشترك',
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (value) {
                            setState(() {
                              _testData['subscriberName'] = value;
                            });
                          },
                        ),
                        const SizedBox(height: 12),                        // تحرير المبلغ
                        TextFormField(
                          initialValue: _testData['paymentAmount'],
                          decoration: const InputDecoration(
                            labelText: 'المبلغ',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            setState(() {
                              _testData['paymentAmount'] = value;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // معاينة الإيصال
                Text(
                  'معاينة الإيصال',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),

                // عرض معاينة الإيصال مع أزرار الطباعة
                ReceiptImageService.buildReceiptPreview(
                  settings: _testSettings,
                  data: _testData,
                  operationType: _testData['operationType'],
                  onPrintAsText: _printAsText,
                  onPrintAsImage: _printAsImage,
                ),

                const SizedBox(height: 16),            // معلومات إضافية
                Card(
                  color: Colors.blue[50],
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue[700]),
                            const SizedBox(width: 8),
                            Text(
                              'معلومات مفيدة',
                              style: TextStyle(
                                color: Colors.blue[700],
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• الطباعة كصورة توفر أفضل جودة للنصوص العربية\n'
                          '• الطباعة كنص أسرع ولكن قد تواجه مشاكل مع بعض الأحرف العربية\n'
                          '• تأكد من اتصال الطابعة قبل بدء الطباعة\n'
                          '• يمكنك تعديل البيانات أعلاه لاختبار حالات مختلفة\n'
                          '• في حالة توقف الطابعة عن السحب، استخدم زر الإيقاف الطارئ',
                          style: TextStyle(color: Colors.blue[700]),
                        ),
                        const SizedBox(height: 8),
                        // معلومات حالة الطابعة
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: _testSettings.connectedDeviceAddress != null 
                                ? Colors.green[100] 
                                : Colors.orange[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                _testSettings.connectedDeviceAddress != null 
                                    ? Icons.bluetooth_connected 
                                    : Icons.bluetooth_disabled,
                                color: _testSettings.connectedDeviceAddress != null 
                                    ? Colors.green[700] 
                                    : Colors.orange[700],
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _testSettings.connectedDeviceAddress != null 
                                      ? 'الطابعة متصلة: ${_testSettings.connectedDeviceName ?? "غير معروف"}'
                                      : 'لا توجد طابعة متصلة - يرجى الذهاب لإعدادات الطابعة أولاً',
                                  style: TextStyle(
                                    color: _testSettings.connectedDeviceAddress != null 
                                        ? Colors.green[700] 
                                        : Colors.orange[700],
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),            // أزرار إضافية
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isLoading ? null : _resetData,
                        icon: const Icon(Icons.refresh),
                        label: const Text('إعادة ضبط البيانات'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isLoading ? null : _showPrinterSettings,
                        icon: const Icon(Icons.settings),
                        label: const Text('إعدادات الطابعة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // زر إيقاف طوارئ
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _emergencyStop,
                    icon: const Icon(Icons.stop),
                    label: const Text('إيقاف طوارئ للطابعة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // مؤشر التحميل
          if (_isLoading)
            Container(
              color: Colors.black54,
              child: Center(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const CircularProgressIndicator(),
                        const SizedBox(height: 16),
                        Text(
                          'جارٍ الطباعة...\nيرجى عدم إغلاق التطبيق',
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
  void _printAsText() async {
    if (_testSettings.connectedDeviceAddress == null) {
      setState(() {
        _error = 'لا توجد طابعة متصلة. يرجى الذهاب لإعدادات الطابعة أولاً.';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
      _success = null;
    });

    try {
      await PrinterService.printReceiptUnified(
        settings: _testSettings,
        data: _testData,
        operationType: _testData['operationType'],
        asImage: false, // طباعة كنص
      );

      setState(() {
        _success = 'تم طباعة الإيصال كنص بنجاح! 📝';
      });
    } catch (e) {
      setState(() {
        _error = 'فشل في طباعة الإيصال كنص: ${e.toString()}';
      });
      // محاولة إيقاف طوارئ في حالة الخطأ
      try {
        await PrinterService.emergencyStop();
      } catch (stopError) {
        // تجاهل أخطاء الإيقاف
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _printAsImage() async {
    if (_testSettings.connectedDeviceAddress == null) {
      setState(() {
        _error = 'لا توجد طابعة متصلة. يرجى الذهاب لإعدادات الطابعة أولاً.';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
      _success = null;
    });

    try {
      await PrinterService.printReceiptUnified(
        settings: _testSettings,
        data: _testData,
        operationType: _testData['operationType'],
        asImage: true, // طباعة كصورة
      );

      setState(() {
        _success = 'تم طباعة الإيصال كصورة بنجاح! 🖼️';
      });
    } catch (e) {
      setState(() {
        _error = 'فشل في طباعة الإيصال كصورة: ${e.toString()}';
      });
      // محاولة إيقاف طوارئ في حالة الخطأ
      try {
        await PrinterService.emergencyStop();
      } catch (stopError) {
        // تجاهل أخطاء الإيقاف
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  void _resetData() {
    setState(() {
      _testData['companyInfo'] = 'شركة المنصة للخدمات الذكية';
      _testData['subscriberName'] = 'أحمد محمد علي';
      _testData['paymentAmount'] = '150';
      _testData['dateTime'] = DateTime.now().toString().substring(0, 16);
      _error = null;
      _success = null;
    });
  }

  /// إيقاف طوارئ للطابعة
  Future<void> _emergencyStop() async {
    try {
      await PrinterService.emergencyStop();
      setState(() {
        _success = 'تم إرسال أمر الإيقاف الطارئ للطابعة';
        _error = null;
        _isLoading = false; // إيقاف حالة التحميل
      });
    } catch (e) {
      setState(() {
        _error = 'فشل في إيقاف الطابعة: $e';
        _success = null;
      });
    }
  }

  void _showPrinterSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات الطابعة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [            CheckboxListTile(
              title: const Text('عرض معلومات الشركة'),
              value: _testSettings.showCompanyInfo,
              onChanged: (value) {
                setState(() {
                  _testSettings = _testSettings.copyWith(showCompanyInfo: value ?? true);
                });
                Navigator.pop(context);
                _showPrinterSettings();
              },
            ),
            CheckboxListTile(
              title: const Text('عرض نوع العملية'),
              value: _testSettings.showOperationType,
              onChanged: (value) {
                setState(() {
                  _testSettings = _testSettings.copyWith(showOperationType: value ?? true);
                });
                Navigator.pop(context);
                _showPrinterSettings();
              },
            ),
            CheckboxListTile(
              title: const Text('عرض اسم المشترك'),
              value: _testSettings.showSubscriberName,
              onChanged: (value) {
                setState(() {
                  _testSettings = _testSettings.copyWith(showSubscriberName: value ?? true);
                });
                Navigator.pop(context);
                _showPrinterSettings();
              },
            ),
            CheckboxListTile(
              title: const Text('عرض المبلغ'),
              value: _testSettings.showPaymentAmount,
              onChanged: (value) {
                setState(() {
                  _testSettings = _testSettings.copyWith(showPaymentAmount: value ?? true);
                });
                Navigator.pop(context);
                _showPrinterSettings();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
