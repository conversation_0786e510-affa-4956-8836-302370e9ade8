import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/foundation.dart'; // Import debugPrint

/// A [JsonConverter] that can handle both [Timestamp] and [String] values from Firestore.
/// It serializes [DateTime] objects to [Timestamp] for consistent storage.
class FlexibleTimestampConverter implements JsonConverter<DateTime, dynamic> {
  const FlexibleTimestampConverter();

    @override
  DateTime fromJson(dynamic json) {
    if (json is Timestamp) {
      return json.toDate();
    }
    if (json is String) {
      try {
        // Attempt to parse as ISO format first
        return DateTime.parse(json);
      } catch (e) {
        // If that fails, try custom formats
        try {
          // Try format: "yyyy-MM-dd HH:mm:ss.SSSSSS"
          if (json.contains(".")) {
            return DateTime.parse(json.replaceFirst(" ", "T"));
          }
          // Try format: "yyyy-MM-dd HH:mm:ss"
          return DateTime.parse(json.replaceFirst(" ", "T").replaceFirst(RegExp(r"\.\d+"), ""));
        } catch (e2) {
          try {
            // Handle format like "17 August 2025 at 21:03:09 UTC+3"
            final regex = RegExp(r"(\d{1,2})\s+(\w+)\s+(\d{4})\s+at\s+(\d{1,2}):(\d{2}):(\d{2})");
            final match = regex.firstMatch(json);
            if (match != null) {
              final day = int.parse(match.group(1)!);
              final monthName = match.group(2)!;
              final year = int.parse(match.group(3)!);
              final hour = int.parse(match.group(4)!);
              final minute = int.parse(match.group(5)!);
              final second = int.parse(match.group(6)!);
              
              // Convert month name to number
              final month = _getMonthNumber(monthName);
              
              return DateTime(year, month, day, hour, minute, second);
            }
          } catch (e3) {
            print("Error parsing date string: $json");
            // Default to current time if parsing fails
            return DateTime.now();
          }
        }
      }
    }
    // Default to current time if the field is null or of an unexpected type
    return DateTime.now();
  }
  
  /// Helper method to convert month name to number
  int _getMonthNumber(String monthName) {
    const months = {
      "January": 1, "Jan": 1, "يناير": 1,
      "February": 2, "Feb": 2, "فبراير": 2,
      "March": 3, "Mar": 3, "مارس": 3,
      "April": 4, "Apr": 4, "أبريل": 4,
      "May": 5, "مايو": 5,
      "June": 6, "يونيو": 6,
      "July": 7, "يوليو": 7,
      "August": 8, "Aug": 8, "أغسطس": 8,
      "September": 9, "Sep": 9, "سبتمبر": 9,
      "October": 10, "Oct": 10, "أكتوبر": 10,
      "November": 11, "Nov": 11, "نوفمبر": 11,
      "December": 12, "Dec": 12, "ديسمبر": 12,
    };
    
    return months[monthName] ?? DateTime.now().month;
  }

  @override
  Timestamp toJson(DateTime date) => Timestamp.fromDate(date);
}
