import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/subscriber_model.dart';
import '../models/support_message_model.dart';
import '../models/support_chat_model.dart';
import '../services/support_chat_service.dart';
import 'package:uuid/uuid.dart';

class SupportChatPage extends StatefulWidget {
  final SubscriberModel subscriber;
  const SupportChatPage({Key? key, required this.subscriber}) : super(key: key);

  @override
  State<SupportChatPage> createState() => _SupportChatPageState();
}

class _SupportChatPageState extends State<SupportChatPage> {
  final SupportChatService _chatService = SupportChatService();
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _sending = false;
  SupportChatModel? _chat;
  String? _adminId;

  @override
  void initState() {
    super.initState();
    _loadOrCreateChat();
  }

  Future<void> _loadOrCreateChat() async {
    // جلب المدير المرتبط بالمشترك عبر adminId
    final adminId = widget.subscriber.adminId;
    if (adminId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('لا يوجد مدير مرتبط بهذا المشترك.')));
      Navigator.of(context).pop();
      return;
    }
    final adminDoc = await FirebaseFirestore.instance.collection('users').doc(adminId).get();
    if (!adminDoc.exists) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('لا يوجد مدير متاح حالياً.')));
      Navigator.of(context).pop();
      return;
    }
    _adminId = adminId;
    final chat = await _chatService.createOrGetChat(
      subscriberId: widget.subscriber.id,
      adminId: _adminId!,
    );
    setState(() => _chat = chat);
  }

  void _scrollToEnd() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendMessage() async {
    if (_controller.text.trim().isEmpty || _chat == null || _sending) return;
    setState(() => _sending = true);
    final msg = SupportMessageModel(
      id: const Uuid().v4(),
      chatId: _chat!.id,
      senderId: widget.subscriber.id,
      senderType: 'subscriber',
      text: _controller.text.trim(),
      timestamp: DateTime.now(),
      isRead: false,
    );
    await _chatService.sendMessage(
      chatId: _chat!.id,
      message: msg,
      lastSender: widget.subscriber.id,
    );
    _controller.clear();
    setState(() => _sending = false);
    _scrollToEnd();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('الدعم الفني'),
        backgroundColor: theme.colorScheme.surface,
      ),
      body: _chat == null
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                if (!_chat!.isOpen)
                  Column(
                    children: [
                      Container(
                        width: double.infinity,
                        color: Colors.red.shade50,
                        padding: const EdgeInsets.all(12),
                        child: Row(
                          children: [
                            const Icon(Icons.lock, color: Colors.red),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text('تم إغلاق هذه المحادثة من قبل الإدارة. لا يمكنك إرسال رسائل جديدة.', style: TextStyle(color: Colors.red)),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 12),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.chat),
                        label: const Text('بدء محادثة جديدة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                        ),
                        onPressed: () async {
                          setState(() => _chat = null); // لإظهار اللودينج
                          await _loadOrCreateChat();
                        },
                      ),
                    ],
                  ),
                Expanded(
                  child: StreamBuilder<List<SupportMessageModel>>(
                    stream: _chatService.getMessagesStream(_chat!.id),
                    builder: (context, snapshot) {
                      final messages = snapshot.data ?? [];
                      WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToEnd());
                      return Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [theme.colorScheme.surface, theme.colorScheme.primary.withOpacity(0.04)],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                        child: ListView.builder(
                          controller: _scrollController,
                          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
                          itemCount: messages.length,
                          itemBuilder: (context, i) {
                            final msg = messages[i];
                            final isMe = msg.senderType == 'subscriber';
                            final showAvatar = i == 0 || messages[i - 1].senderId != msg.senderId;
                            return Column(
                              crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                              children: [
                                if (showAvatar)
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                                    child: Row(
                                      mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
                                      children: [
                                        if (!isMe)
                                          CircleAvatar(
                                            backgroundColor: Colors.blue.shade100,
                                            child: const Icon(Icons.support_agent, color: Colors.blue),
                                          ),
                                        if (isMe)
                                          CircleAvatar(
                                            backgroundColor: Colors.green.shade100,
                                            child: const Icon(Icons.person, color: Colors.green),
                                          ),
                                        const SizedBox(width: 8),
                                        Text(
                                          isMe ? 'أنت' : 'الدعم',
                                          style: theme.textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                  ),
                                Align(
                                  alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
                                  child: Container(
                                    margin: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 14),
                                    constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.75),
                                    decoration: BoxDecoration(
                                      color: isMe
                                          ? theme.colorScheme.primary.withOpacity(0.15)
                                          : theme.colorScheme.surfaceVariant,
                                      borderRadius: BorderRadius.only(
                                        topLeft: const Radius.circular(16),
                                        topRight: const Radius.circular(16),
                                        bottomLeft: Radius.circular(isMe ? 16 : 4),
                                        bottomRight: Radius.circular(isMe ? 4 : 16),
                                      ),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          msg.text,
                                          style: theme.textTheme.bodyMedium,
                                        ),
                                        const SizedBox(height: 4),
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(Icons.access_time, size: 12, color: Colors.grey.shade500),
                                            const SizedBox(width: 2),
                                            Text(
                                              _formatTime(msg.timestamp),
                                              style: theme.textTheme.labelSmall?.copyWith(color: Colors.grey.shade500),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      );
                    },
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    boxShadow: [
                      BoxShadow(
                        color: theme.colorScheme.primary.withOpacity(0.07),
                        blurRadius: 8,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: SafeArea(
                    top: false,
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _controller,
                            minLines: 1,
                            maxLines: 4,
                            decoration: InputDecoration(
                              hintText: 'اكتب رسالتك هنا...'
                            ),
                            onSubmitted: (_) => _chat!.isOpen ? _sendMessage() : null,
                            enabled: _chat!.isOpen,
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: _sending
                              ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2))
                              : const Icon(Icons.send, color: Colors.blue),
                          onPressed: _sending || !_chat!.isOpen ? null : _sendMessage,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final min = time.minute.toString().padLeft(2, '0');
    return '$hour:$min';
  }
} 