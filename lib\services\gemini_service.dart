import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:isp_manager/models/ai_assistant_model.dart';

class GeminiService {
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
  final String apiKey;

  GeminiService({required this.apiKey});

  Future<String> generateContent(String prompt, {List<Map<String, dynamic>>? chatHistory}) async {
    try {
      final url = '$_baseUrl/models/gemini-1.5-flash:generateContent?key=$apiKey';
      
      // تحضير الرسائل مع الأخذ في الاعتبار سياق الجهاز
      final messages = <Map<String, dynamic>>[];
      
      // إضافة رسالة النظام أولاً
      messages.add({
        'role': 'user',
        'parts': [{'text': _getSystemPrompt()}]
      });
      messages.add({
        'role': 'model',
        'parts': [{'text': 'حسناً، أنا جاهز لمساعدتك في إدارة أجهزة الشبكة.'}]
      });
      
      // إضافة سجل المحادثة السابقة إذا وجد
      if (chatHistory != null && chatHistory.isNotEmpty) {
        // نأخذ آخر 10 رسائل لتجنب تجاوز الحد الأقصى للسياق
        final recentHistory = chatHistory.length > 10 
            ? chatHistory.sublist(chatHistory.length - 10) 
            : chatHistory;
            
        for (var msg in recentHistory) {
          // تخطي رسائل النظام في السجل
          if (msg['content'] == _getSystemPrompt()) continue;
          
          // إضافة سياق الجهاز إذا كان متاحاً
          String content = msg['content'];
          if (msg['device'] != null) {
            final device = msg['device'];
            content = "[الجهاز: ${device['name']} - ${device['host']}]\n$content";
          }
          
          messages.add({
            'role': msg['role'] == 'user' ? 'user' : 'model',
            'parts': [{'text': content}]
          });
        }
      }
      
      // إضافة الرسالة الجديدة مع سياق الجهاز إذا كان متوفراً
      String currentPrompt = prompt;
      if (chatHistory != null && 
          chatHistory.isNotEmpty && 
          chatHistory.last['device'] != null) {
        final device = chatHistory.last['device'];
        currentPrompt = "[الجهاز: ${device['name']} - ${device['host']}]\n$prompt";
      }
      
      messages.add({
        'role': 'user',
        'parts': [{'text': currentPrompt}]
      });

      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'contents': messages,
          'generationConfig': {
            'temperature': 0.7,  // زيادة قليلاً للإبداع
            'topK': 50,
            'topP': 0.9,
            'maxOutputTokens': 2048,
          },
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['candidates']?[0]['content']['parts']?[0]['text'] ?? 'لم أتمكن من فهم الرد';
      } else {
        debugPrint('Gemini API Error: ${response.statusCode} - ${response.body}');
        return 'حدث خطأ أثناء الاتصال بخدمة الذكاء الاصطناعي. الرجاء المحاولة مرة أخرى.';
      }
    } catch (e) {
      debugPrint('Error in GeminiService: $e');
      return 'حدث خطأ غير متوقع: $e';
    }
  }

  String _getSystemPrompt() {
    return """
    أنت مساعد ذكي متخصص في إدارة أجهزة الشبكة، وخاصة أجهزة MikroTik و Ubiquiti.
    مهمتك هي مساعدة المستخدمين في إدارة أجهزتهم الشبكية، وحل المشكلات، وتقديم إجابات دقيقة.

    **능력 (Capabilities):**
    - **SSH Command Execution:** You can execute SSH commands on the devices. To do this, formulate your response as `run: <command>`. For example, to get the system identity, you would say `run: /system identity print`. The command will be executed on the device specified in the user's message.

    تعليمات مهمة:
    1. استخدم لغة عربية واضحة وسهلة الفهم.
    2. ركز على تقديم إجابات تقنية دقيقة.
    3. إذا لم تكن متأكداً من الإجابة، قل أنك لا تعرف بدلاً من تقديم معلومات خاطئة.
    4. ساعد في صياغة أوامر التكوين بشكل صحيح.
    5. قدم نصائح أمان مهمة عند الاقتضاء.
    
    معلومات عن الأجهزة المتاحة:
    - يمكنك رؤية معلومات الجهاز المستهدف في بداية الرسالة بين قوسين معقوفين [الجهاز: الاسم - العنوان]
    - استخدم هذه المعلومات لتقديم إجابات أكثر دقة وتخصيصاً
    
    ملاحظات أمان مهمة:
    - لا تقدم أوامر تنفيذية مباشرة على الأجهزة إلا بعد التأكيد من المستخدم
    - حذر المستخدم من المخاطر المحتملة لأي أمر تنفيذي
    - اقترح دائمًا نسخ احتياطي قبل إجراء تغييرات كبيرة
    
    عند الرد على استفسارات حول أجهزة محددة:
    1. تحقق من معلومات الجهاز المتوفرة
    2. قدم إجابات مخصصة بناءً على نوع الجهاز وإصدار البرنامج
    3. إذا لزم الأمر، اطلب معلومات إضافية من المستخدم
    
    تذكر أن تكون دقيقًا ومفيدًا في إجاباتك، مع الحفاظ على أمان الشبكة.
    - استكشاف الأخطاء وإصلاحها
    - تحسين أداء الشبكة
    - وغيرها من مهام إدارة الشبكات
    """;
  }
}
