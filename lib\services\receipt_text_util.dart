// Utility to build the receipt text for both printing and preview
import '../models/printer_settings_model.dart';

String buildReceiptText({
  required PrinterSettingsModel settings,
  required Map<String, dynamic> data,
  required String operationType,
}) {
  final buffer = StringBuffer();
  for (final element in settings.elementOrder) {
    switch (element) {
      case 'companyInfo':
        if (settings.showCompanyInfo) {
          buffer.writeln(settings.companyName ?? '');
          buffer.writeln(settings.companyAddress ?? '');
          buffer.writeln('----------------------');
        }
        break;
      case 'subscriberName':
        if (settings.showSubscriberName) {
          buffer.writeln('الاسم: ${data['subscriberName'] ?? ''}');
        }
        break;
      case 'subscriptionNumber':
        if (settings.showSubscriptionNumber) {
          buffer.writeln('رقم الاشتراك: ${data['subscriptionNumber'] ?? ''}');
        }
        break;
      case 'dateTime':
        if (settings.showDateTime) {
          buffer.writeln('التاريخ: ${data['dateTime'] ?? ''}');
        }
        break;
      case 'operationType':
        if (settings.showOperationType) {
          buffer.writeln('العملية: $operationType');
        }
        break;
      case 'paymentAmount':
        if (settings.showPaymentAmount) {
          buffer.writeln('المبلغ: ${data['paymentAmount'] ?? ''}');
        }
        break;
      case 'employeeName':
        if (settings.showEmployeeName) {
          buffer.writeln('الموظف: ${data['employeeName'] ?? ''}');
        }
        break;
    }  }
  buffer.writeln('----------------------');
  buffer.writeln('شكراً لاستخدامكم خدماتنا');
  return buffer.toString();
}
