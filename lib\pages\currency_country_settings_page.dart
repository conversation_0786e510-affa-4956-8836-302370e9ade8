// Currency and Country Settings Page
import 'package:flutter/material.dart';
import 'package:currency_picker/currency_picker.dart';
import 'package:country_picker/country_picker.dart';
import '../models/app_settings_model.dart';
import '../services/app_settings_service.dart';

class CurrencyCountrySettingsPage extends StatefulWidget {
  const CurrencyCountrySettingsPage({super.key});

  @override
  State<CurrencyCountrySettingsPage> createState() => _CurrencyCountrySettingsPageState();
}

class _CurrencyCountrySettingsPageState extends State<CurrencyCountrySettingsPage> {
  AppSettings? _currentSettings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await AppSettingsService.getSettings();
      setState(() {
        _currentSettings = settings;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _currentSettings = AppSettings.defaultSettings();
        _isLoading = false;
      });
    }
  }

  Future<void> _showCurrencyPicker() async {
    showCurrencyPicker(
      context: context,
      showFlag: true,
      showCurrencyName: true,
      showCurrencyCode: true,
      onSelect: (Currency currency) async {
        await AppSettingsService.updateCurrency(
          currencyCode: currency.code,
          currencySymbol: currency.symbol,
          currencyName: currency.name,
        );
        await _loadSettings();
        _showSuccessMessage('تم تحديث العملة بنجاح');
      },
      currencyFilter: <String>[
        'USD', 'EUR', 'IQD', 'SAR', 'AED', 'KWD', 'QAR', 'BHD', 'OMR',
        'JOD', 'LBP', 'SYP', 'EGP', 'TRY', 'IRR', 'GBP', 'JPY', 'CHF',
        'CAD', 'AUD', 'CNY', 'INR', 'PKR', 'AFN', 'BDT', 'BTN', 'LKR',
        'MVR', 'NPR', 'RUB', 'KZT', 'UZS', 'KGS', 'TJS', 'TMT', 'AZN',
        'GEL', 'AMD', 'MDL', 'UAH', 'BYN', 'RON', 'BGN', 'HRK', 'CZK',
        'HUF', 'PLN', 'SEK', 'NOK', 'DKK', 'ISK', 'ALL', 'MKD', 'RSD',
        'BAM', 'ME', 'EUR',
      ],
    );
  }

  Future<void> _showCountryPicker() async {
    showCountryPicker(
      context: context,
      showPhoneCode: true,
      countryListTheme: CountryListThemeData(
        flagSize: 25,
        backgroundColor: Colors.white,
        textStyle: const TextStyle(fontSize: 16, color: Colors.blueGrey),
        bottomSheetHeight: 500,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
        inputDecoration: InputDecoration(
          labelText: 'البحث',
          hintText: 'ابحث عن دولة',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              color: const Color(0xFF8C98A8).withOpacity(0.2),
            ),
          ),
        ),
      ),
      onSelect: (Country country) async {
        await AppSettingsService.updateCountry(
          countryCode: country.countryCode,
          countryName: country.displayNameNoCountryCode,
          countryFlag: country.flagEmoji,
          phoneCode: '+${country.phoneCode}',
        );
        await _loadSettings();
        _showSuccessMessage('تم تحديث إعدادات الدولة بنجاح');
      },
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  Future<void> _resetToDefault() async {
    final confirmed = await _showConfirmationDialog(
      'إعادة تعيين الإعدادات',
      'هل أنت متأكد من إعادة تعيين إعدادات العملة والدولة إلى القيم الافتراضية (العراق - الدينار العراقي)؟',
      'إعادة تعيين',
    );

    if (confirmed) {
      await AppSettingsService.resetToDefault();
      await _loadSettings();
      _showSuccessMessage('تم إعادة تعيين الإعدادات إلى القيم الافتراضية');
    }
  }

  Future<bool> _showConfirmationDialog(String title, String message, String confirmText) async {
    return await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            const SizedBox(width: 8),
            Expanded(child: Text(title)),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(dialogContext).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    ) ?? false;
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildSettingsTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color? color,
    Widget? trailing,
  }) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: trailing ?? const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }


  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('إعدادات العملة والدولة')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إعدادات العملة والدولة',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetToDefault,
            tooltip: 'إعادة تعيين للإعدادات الافتراضية',
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Current Settings Overview
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary.withOpacity(0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.settings,
                  size: 40,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
                const SizedBox(height: 12),
                Text(
                  'الإعدادات الحالية',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Column(
                      children: [
                        Text(
                          'العملة',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.8),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_currentSettings?.currencySymbol} ${_currentSettings?.currencyCode}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      height: 40,
                      width: 1,
                      color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.3),
                    ),
                    Column(
                      children: [
                        Text(
                          'الدولة',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.8),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_currentSettings?.countryFlag} ${_currentSettings?.phoneCode}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Currency Settings Section
          Text(
            'إعدادات العملة',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 12),

          _buildSettingsCard([
            _buildSettingsTile(
              icon: Icons.attach_money,
              title: 'اختيار العملة',
              subtitle: '${_currentSettings?.currencyName} (${_currentSettings?.currencyCode}) - ${_currentSettings?.currencySymbol}',
              onTap: _showCurrencyPicker,
              color: Colors.green,
            ),
          ]),
          const SizedBox(height: 20),

          // Country Settings Section
          Text(
            'إعدادات الدولة',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 12),

          _buildSettingsCard([
            _buildSettingsTile(
              icon: Icons.flag,
              title: 'اختيار الدولة',
              subtitle: '${_currentSettings?.countryName} (${_currentSettings?.phoneCode})',
              onTap: _showCountryPicker,
              color: Colors.blue,
            ),
          ]),
          const SizedBox(height: 20),

          // Information Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'معلومات مهمة',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  '• العملة المختارة ستظهر في جميع المعاملات المالية',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 4),
                Text(
                  '• مفتاح الدولة سيستخدم في إشعارات WhatsApp و SMS',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 4),
                Text(
                  '• يمكن تغيير هذه الإعدادات في أي وقت',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
