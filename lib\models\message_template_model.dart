class MessageTemplateModel {
  final String id;
  final String name;
  final String content;
  final MessageTemplateType type;
  final DateTime createdAt;
  final bool isDefault;
  String adminId;

  MessageTemplateModel({
    required this.id,
    required this.name,
    required this.content,
    required this.type,
    required this.createdAt,
    required this.adminId,
    this.isDefault = false,
  });

  // Firebase compatibility methods
  Map<String, dynamic> toMap() => {
    'id': id,
    'name': name,
    'content': content,
    'type': type.index,
    'createdAt': createdAt.toIso8601String(),
    'isDefault': isDefault,
    "adminId": adminId,
  };

  factory MessageTemplateModel.fromMap(Map<String, dynamic> map) =>
      MessageTemplateModel(
        id: map['id']??"",
        name: map['name']??"",
        content: map['content'],
        type: MessageTemplateType.values[map['type']??0],
        createdAt: DateTime.parse(map['createdAt']),
        isDefault: map['isDefault'] == 1 || map['isDefault'] == true,
        adminId: map['adminId'] ?? "",
      );

  // Legacy compatibility methods
  Map<String, dynamic> toJson() => toMap();

  factory MessageTemplateModel.fromJson(Map<String, dynamic> json) =>
      MessageTemplateModel.fromMap(json);

  MessageTemplateModel copyWith({
    String? id,
    String? name,
    String? content,
    MessageTemplateType? type,
    DateTime? createdAt,
    bool? isDefault,
    required String adminId,
  }) => MessageTemplateModel(
    id: id ?? this.id,
    name: name ?? this.name,
    content: content ?? this.content,
    type: type ?? this.type,
    createdAt: createdAt ?? this.createdAt,
    isDefault: isDefault ?? this.isDefault,
    adminId: adminId,
  );

  // متغيرات القالب المتاحة
  static const Map<String, String> availableVariables = {
    '{subscriber_name}': 'اسم المشترك',
    '{package_name}': 'اسم الباقة',
    '{package_price}': 'سعر الباقة (بالعملة المحددة في الإعدادات)',
    '{package_duration}': 'مدة الباقة',
    '{package_speed}': 'سرعة الباقة',
    '{package_devices}': 'عدد الأجهزة',
    '{start_date}': 'تاريخ بداية الاشتراك',
    '{end_date}': 'تاريخ انتهاء الاشتراك',
    '{days_remaining}': 'الأيام المتبقية',
    '{debt_amount}': 'المبلغ المستحق (بالعملة المحددة في الإعدادات)',
    '{payment_amount}': 'مبلغ الدفعة (بالعملة المحددة في الإعدادات)',
    '{payment_date}': 'تاريخ الدفع',
  };

  // الحصول على وصف المتغير
  static String getVariableDescription(String variable) {
    return availableVariables[variable] ?? 'غير معروف';
  }

  // التحقق من وجود المتغير
  static bool isValidVariable(String variable) {
    return availableVariables.containsKey(variable);
  }
}

// أنواع قوالب الرسائل
enum MessageTemplateType {
  renewal, // تجديد الاشتراك
  payment, // تسجيل دفعة
  reminder, // تذكير بالدفع
  welcome, // ترحيب بمشترك جديد
  expiry, // انتهاء الاشتراك
  nearExpiry, // قرب انتهاء الاشتراك (جديد)
  custom, // مخصص
}

// امتداد لتحويل نوع القالب إلى نص
extension MessageTemplateTypeExtension on MessageTemplateType {
  String get displayName {
    switch (this) {
      case MessageTemplateType.renewal:
        return 'تجديد الاشتراك';
      case MessageTemplateType.payment:
        return 'تسجيل دفعة';
      case MessageTemplateType.reminder:
        return 'تذكير بالدفع';
      case MessageTemplateType.welcome:
        return 'ترحيب بمشترك جديد';
      case MessageTemplateType.expiry:
        return 'انتهاء الاشتراك';
      case MessageTemplateType.nearExpiry:
        return 'تنبيه قرب انتهاء الاشتراك';
      case MessageTemplateType.custom:
        return 'مخصص';
    }
  }
}
