name: isp_manager
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.6+3

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter
  freezed_annotation: ^2.4.1
  cupertino_icons: ^1.0.8
  router_os_client: ^1.0.12
  dartssh2: ^2.12.0
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.3
  cloud_firestore: ^5.5.0
  firebase_storage: ^12.3.3
  firebase_messaging: ^15.1.3
  firebase_in_app_messaging: ^0.8.1+9
  bluetooth_print_plus: ^2.4.6
  fl_chart: ^0.68.0
  shared_preferences: ^2.3.2
  path_provider: ^2.1.5
  http: ^1.2.2
  uuid: ^4.5.1
  sqflite: ^2.3.2
  path: ^1.9.1
  file_selector: ^1.0.3
  share_plus: ^11.0.0
  url_launcher: ^6.3.1
  intl: ^0.20.2
  excel: ^4.0.6
  sqflite_common_ffi: ^2.3.0
  teledart: ^0.6.1
  fluttertoast: ^8.2.1
  blue_thermal_printer: ^1.2.3
  flutter_localizations:
    sdk: flutter
  bidi: ^2.0.13
  image: ^3.0.2
  google_fonts: ^6.2.1
  esc_pos_utils: any
  package_info_plus: ^8.0.0
  collection: ^1.18.0
  local_auth: ^2.2.0
  device_info_plus: ^11.5.0
  json_annotation: ^4.8.1
  crypto: ^3.0.2
  encrypt: ^5.0.1
  flutter_widget_from_html_core: ^0.14.11
  screenshot: ^3.0.0
  pdf: ^3.8.4
  cryptojs_aes_dart:
    path: ./cryptojs_aes_dart-main
  android_intent_plus: ^5.3.0
  currency_picker: ^2.0.21
  country_picker: ^2.0.25
  shimmer: ^3.0.0
  zerotier_sockets: ^1.1.0
  wireguard_flutter: ^0.1.3
  audioplayers: ^6.5.0
  qr_flutter: ^4.1.0
  csv: ^6.0.0
  flutter_map: ^7.0.2
  latlong2: ^0.9.0
  geolocator: ^12.0.0
  geocoding: ^3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  freezed: ^2.5.2
  flutter_launcher_icons: "^0.11.0"
  json_serializable: ^6.7.1
  build_runner: ^2.4.7
  flutter_lints: ^5.0.0

flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/images/logo.png"
    background_color: "#0175C2"
    theme_color: "#0175C2"
  windows:
    generate: true
    image_path: "assets/images/logo.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/images/logo.png"

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/images/
    - assets/error.mp3