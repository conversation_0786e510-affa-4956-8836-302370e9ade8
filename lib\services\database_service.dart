import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'dart:math';
import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:isp_manager/services/firebase_auth_service.dart';
import 'package:isp_manager/services/firebase_service.dart';
import 'package:path/path.dart' as path_lib;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:share_plus/share_plus.dart'; // Import share_plus
import 'package:path_provider/path_provider.dart'; // For temporary directory
import '../models/user_model.dart';
import '../models/package_model.dart';
import '../models/subscriber_model.dart';
import '../models/message_template_model.dart';
import '../models/expense_category_model.dart'; // New import
import '../models/expense_model.dart'; // New import
import '../models/activity_log_model.dart'; // Add ActivityLogModel import
import '../models/payment_record_model.dart';
import 'sqlite_service.dart';
import 'telegram_service.dart'; // Import TelegramService
import 'sas_api_service.dart'; // Import SasApiService
import '../models/sas_user_model.dart'; // Import SasUser
import 'earthlink_service.dart'; // Import EarthlinkService
import '../models/earthlink_user_model.dart'; // Import EarthlinkUser
import '../models/earthlink_account_model.dart'; // Import EarthlinkAccount
import 'app_settings_service.dart'; // Add AppSettingsService
import '../models/sas_server_model.dart';

class DatabaseService {
  static const String _usersKey = 'users';
  static const String _packagesKey = 'packages';
  static const String _subscribersKey = 'subscribers';
  static const String _activityLogsKey = 'activity_logs';
  static const String _paymentRecordsKey = 'payment_records';
  static const String _messageTemplatesKey = 'message_templates';
  static const String _expenseCategoriesKey =
      'expense_categories'; // New constant
  static const String _expensesKey = 'expenses'; // New constant
  static const String _currentUserKey = 'current_user';
  static const String _isInitializedKey = 'is_initialized';

  static final DatabaseService _instance = DatabaseService._internal();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final authService = FirebaseAuthService();
  final firebaseUser = "";
  factory DatabaseService() => _instance;
  DatabaseService._internal();
  final user = FirebaseAuth.instance.currentUser;
  late String adminId = "";
  final Uuid _uuid = const Uuid();
  final SQLiteService _sqliteService = SQLiteService();
  final SasApiService _sasApiService =
      SasApiService(); // Add SasApiService instance

  // Flag to determine if we should use SQLite or SharedPreferences
  bool _useSQLite = true;

  // Initialize database with sample data
  Future<void> initializeDatabase() async {
    if (_useSQLite) {
      // Check if database is already initialized by checking if users table has data
      final users = await _sqliteService.getUsers();
      if (users.isEmpty) {
        await _createSampleData();
      }
      // Ensure default expense categories are added if they don't exist
      final categories = await _sqliteService.getExpenseCategories();
      if (categories.isEmpty) {
        await _addDefaultExpenseCategories();
      }
    } else {
      // Legacy SharedPreferences initialization
      final prefs = await SharedPreferences.getInstance();
      final isInitialized = prefs.getBool(_isInitializedKey) ?? false;

      if (!isInitialized) {
        await _createSampleData();
        await prefs.setBool(_isInitializedKey, true);
      }
      // For SharedPreferences, we'll add default categories during sample data creation
    }
  }

  Future<void> loadAdmin() async {
    final doc = await FirebaseFirestore.instance
        .collection('users')
        .doc(FirebaseAuth.instance.currentUser!.uid)
        .get();
    adminId = doc["adminId"];
    print('DEBUG: loadAdmin - Fetched adminId from Firestore: ${doc["adminId"]}');
    print('DEBUG: loadAdmin - Set adminId in DatabaseService: $adminId');
  }

  Future<void> _createSampleData() async {
    // Create only admin user
    final users = [
      UserModel(
        adminId: _uuid.v4(),
        id: _uuid.v4(),
        username: 'admin',
        password: '123456',
        phoneNumber: '0791234567',
        role: UserRole.admin,
        fullName: 'المدير',
        createdAt: DateTime.now(),
        permissions: Permission.allPermissions,
      ),
    ];

    // Empty packages list
    final packages = <PackageModel>[];

    // Empty subscribers list
    final subscribers = <SubscriberModel>[];

    if (_useSQLite) {
      // Save to SQLite
      for (var user in users) {
        await _sqliteService.insertUser(user.toJson());
      }
      await _addDefaultExpenseCategories(); // Add default categories for SQLite
    } else {
      // Save to SharedPreferences
      await saveUsers(users);
      await savePackages(packages);
      await saveSubscribers(subscribers);
      await _addDefaultExpenseCategories(); // Add default categories for SharedPreferences
    }
  }

  Future<void> _addDefaultExpenseCategories() async {
    final defaultCategories = [
      'إيجار',
      'رواتب',
      'كهرباء',
      'ماء',
      'صيانة',
      'إنترنت',
      'مستلزمات مكتبية',
      'وقود',
      'ضرائب',
      'أخرى',
    ];

    for (var categoryName in defaultCategories) {
      // Check if category already exists to prevent duplicates on app restart
      final existingCategories = await getExpenseCategories();
      if (!existingCategories.any((cat) => cat.name == categoryName)) {
        final category = ExpenseCategoryModel(
          adminId: adminId,
          id: _uuid.v4(),
          name: categoryName,
          createdAt: DateTime.now(),
        );
        if (_useSQLite) {
          await _sqliteService.insertExpenseCategory(category.toJson());
        } else {
          final categories = await getExpenseCategories();
          categories.add(category);
          await saveExpenseCategories(categories);
        }
      }
    }
  }

  // User operations
  Future<List<UserModel>> getUsers() async {
    if (_useSQLite) {
      final userMaps = await _sqliteService.getUsers();
      return userMaps.map((map) => UserModel.fromJson(map)).toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString(_usersKey);
      if (usersJson == null) return [];

      final List<dynamic> usersList = jsonDecode(usersJson);
      return usersList.map((json) => UserModel.fromJson(json)).toList();
    }
  }

  Future<void> saveUsers(List<UserModel> users) async {
    if (_useSQLite) {
      // Clear existing users and insert new ones
      await _sqliteService
          .clearAllTables(); // This is a bit extreme, consider more targeted approach
      for (var user in users) {
        await _sqliteService.insertUser(user.toJson());
      }
    } else {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = jsonEncode(users.map((user) => user.toJson()).toList());
      await prefs.setString(_usersKey, usersJson);
    }
  }

  Future<UserModel?> authenticate(String username, String password) async {
    if (_useSQLite) {
      // For SQLite, we need to query the database directly for better performance
      final db = await _sqliteService.database;
      final results = await db.query(
        'users',
        where:
            '(username = ? OR phoneNumber = ?) AND password = ? AND (isActive = 1 OR isActive = "true")',
        whereArgs: [username, username, password],
      );

      if (results.isNotEmpty) {
        return UserModel.fromJson(results.first);
      }
      return null;
    } else {
      // Legacy SharedPreferences approach
      final users = await getUsers();
      try {
        return users.firstWhere(
          (user) =>
              (user.username == username || user.phoneNumber == username) &&
              user.password == password &&
              user.isActive,
        );
      } catch (e) {
        return null;
      }
    }
  }

  Future<SubscriberModel?> getSubscriberById(String id) async {
    if (_useSQLite) {
      final subscriberMap = await _sqliteService.getSubscriberById(id);
      return subscriberMap != null
          ? SubscriberModel.fromJson(subscriberMap)
          : null;
    } else {
      final subscribers = await getSubscribers();
      try {
        return subscribers.firstWhere((s) => s.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  Future<void> setCurrentUser(UserModel? user) async {
    // We'll keep using SharedPreferences for current user session regardless of database choice
    final prefs = await SharedPreferences.getInstance();
    if (user != null) {
      await prefs.setString(_currentUserKey, jsonEncode(user.toJson()));
    } else {
      await prefs.remove(_currentUserKey);
    }
  }

  Future<UserModel?> getCurrentUser() async {
    // We'll keep using SharedPreferences for current user session regardless of database choice
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_currentUserKey);
    if (userJson == null) return null;
    return UserModel.fromJson(jsonDecode(userJson));
  }

  Future<void> updateUser(UserModel user) async {
    if (_useSQLite) {
      await _sqliteService.updateUser(user.toJson());
    } else {
      final users = await getUsers();
      final index = users.indexWhere((u) => u.id == user.id);
      if (index != -1) {
        users[index] = user;
        await saveUsers(users);
      }
    }
  }

  // Package operations
  Future<List<PackageModel>> getPackages() async {
    if (_useSQLite) {
      final packageMaps = await _sqliteService.getPackages();

      return packageMaps.map((map) => PackageModel.fromJson(map)).toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final packagesJson = prefs.getString(_packagesKey);
      if (packagesJson == null) return [];

      final List<dynamic> packagesList = jsonDecode(packagesJson);
      return packagesList.map((json) => PackageModel.fromJson(json)).toList();
    }
  }

  Future<List<PackageModel>> getPackagesFire() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('packages')
          .where('adminId', isEqualTo: adminId)
          .get();

      return snapshot.docs.map((doc) {
        return PackageModel.fromMap(doc.data());
      }).toList();
    } catch (e) {
      print('Error fetching packages from Firestore: $e');
      return [];
    }
  }

  Future<void> syncPackagesToFirebase() async {
    final localPackages = await getPackages(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('packages')
        .where('adminId', isEqualTo: adminId)
        .get();

    // IDs الموجودة بالفعل في Firestore
    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final package in localPackages) {
      if (!firebaseIds.contains(package.id)) {
        PackageModel newPackage = package.copyWith(adminId: adminId);
        await _firestore
            .collection('packages')
            .doc(newPackage.id)
            .set(newPackage.toMap());
      }
    }
  }

  Future<void> syncTemplatesToFirebase() async {
    final localTemplates = await getMessageTemplates(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('message_templates')
        .get();

    // IDs الموجودة بالفعل في Firestore
    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final template in localTemplates) {
      if (!firebaseIds.contains(template.id)) {
        MessageTemplateModel newTemplate = template.copyWith(adminId: adminId);
        await _firestore
            .collection('message_templates')
            .doc(newTemplate.id)
            .set(newTemplate.toMap());
      }
    }
  }

  Future<void> syncExpensesToFirebase() async {
    final localExpenses = await getExpenses(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('expenses')
        .where('adminId', isEqualTo: adminId)
        .get();

    // IDs الموجودة بالفعل في Firestore
    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final expense in localExpenses) {
      if (!firebaseIds.contains(expense.id)) {
        ExpenseModel newEx = expense.copyWith(adminId: adminId);
        await _firestore
            .collection('expenses')
            .doc(newEx.id)
            .set(newEx.toMap());
      }
    }
  }

  Future<void> syncPaymentsToFirebase() async {
    final localPayments = await getPaymentRecords(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('paymentRecord')
        .where('adminId', isEqualTo: adminId)
        .get();

    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final payment in localPayments) {
      if (!firebaseIds.contains(payment.id)) {
        PaymentRecordModel newPayment = payment.copyWith(adminId: adminId);
        await _firestore
            .collection('paymentRecord')
            .doc(newPayment.id)
            .set(newPayment.toMap());
      }
    }
  }

  Future<void> syncActivityToFirebase() async {
    final localActivities = await getActivityLogs(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('activityLog')
        .where('adminId', isEqualTo: adminId)
        .get();

    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final activity in localActivities) {
      if (!firebaseIds.contains(activity.id)) {
        ActivityLogModel newActivity = activity.copyWith(adminId: adminId);
        await _firestore
            .collection('activityLog')
            .doc(newActivity.id)
            .set(newActivity.toMap());
      }
    }
  }

  Future<void> syncExpenseCategoryToFirebase() async {
    final localCategories = await getExpenseCategories(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('expenseCategory')
        .get();

    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final category in localCategories) {
      if (!firebaseIds.contains(category.id)) {
        ExpenseCategoryModel newCat = category.copyWith(adminId: adminId);
        await _firestore
            .collection('expenseCategory')
            .doc(newCat.id)
            .set(newCat.toMap());
      }
    }
  }

  Future<void> syncSubscribersToFirebase() async {
    final localSubscribers = await getSubscribers(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('subscribers')
        .where('adminId', isEqualTo: adminId)
        .get();

    // IDs الموجودة بالفعل في Firestore
    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final sub in localSubscribers) {
      if (!firebaseIds.contains(sub.id)) {
        SubscriberModel newSub = sub.copyWith(adminId: adminId);
        await _firestore
            .collection('subscribers')
            .doc(newSub.id)
            .set(newSub.toMap());
      }
    }
  }

  Future<void> savePackages(List<PackageModel> packages) async {
    if (_useSQLite) {
      // Delete all existing packages and insert new ones
      final db = await _sqliteService.database;
      await db.delete('packages');

      for (var package in packages) {
        await _sqliteService.insertPackage(package.toJson());
      }
    } else {
      final prefs = await SharedPreferences.getInstance();
      final packagesJson = jsonEncode(
        packages.map((pkg) => pkg.toJson()).toList(),
      );
      await prefs.setString(_packagesKey, packagesJson);
    }
  }

  Future<void> addPackage(PackageModel package) async {
    // if (_useSQLite) {
    //   await _sqliteService.insertPackage(package.toJson());
    // } else {
    //   final packages = await getPackages();
    //   packages.add(package);
    //   await savePackages(packages);
    // }
    PackageModel newPack = package.copyWith(adminId: adminId);
    await _firestore
        .collection('packages')
        .doc(newPack.id)
        .set(newPack.toMap());
    TelegramService()
        .sendPackageNotification(
          action: 'إضافة',
          packageName: package.name,
          price: package.price,
          speed: package.speed,
        )
        .catchError((error) {
          // Silently handle telegram errors to prevent app freeze
          print('Telegram notification error: $error');
        });
  }

  Future<void> updatePackage(PackageModel package) async {
    try {
      await _firestore
          .collection('packages')
          .doc(package.id)
          .update(package.toMap());
      
      TelegramService()
          .sendPackageNotification(
            action: 'تعديل',
            packageName: package.name,
            price: package.price,
            speed: package.speed,
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } catch (e) {
      print('Error updating package: $e');
      rethrow;
    }
  }

  Future<void> deletePackage(String packageId) async {
    try {
      await FirebaseFirestore.instance
          .collection('packages')
          .doc(packageId)
          .delete();
    } catch (e) {
      print('Error deleting package: $e');
    }
  }

    Future<PackageModel?> getPackageById(String id) async {
    final packages = await getPackagesFire();
    try {
      return packages.firstWhere(
        (p) => p.id.trim().toLowerCase() == id.trim().toLowerCase(),
      );
    } catch (e) {
      print('تحذير: لم يتم العثور على باقة حقيقية للمعرف: "${id.trim()}" ');
      print('الباقات المتوفرة: ${packages.map((p) => p.id).toList()}');
      return null;
    }
  }

  Future<SasServerModel?> getServerById(String serverId) async {
    try {
      final doc = await _firestore.collection('sas_servers').doc(serverId).get();
      if (doc.exists) {
        return SasServerModel.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error fetching server by ID: $e');
      return null;
    }
  }

  Future<SasServerModel?> getConnectedServer() async {
    print('DEBUG: getConnectedServer - Current adminId: $adminId');
    try {
      final querySnapshot = await _firestore
          .collection('sas_servers')
          .where('adminId', isEqualTo: adminId)
          .where('isConnected', isEqualTo: 1)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        print('DEBUG: getConnectedServer - Found connected server for adminId: $adminId');
        return SasServerModel.fromMap(querySnapshot.docs.first.data());
      }
      print('DEBUG: getConnectedServer - No connected server found for adminId: $adminId');
      return null;
    } catch (e) {
      print('DEBUG: getConnectedServer - Error fetching connected server: $e');
      return null;
    }
  }

  // Subscriber operations
  Future<List<SubscriberModel>> getSubscribers() async {
    if (_useSQLite) {
      final subscriberMaps = await _sqliteService.getSubscribers();
      return subscriberMaps
          .map((map) => SubscriberModel.fromJson(map))
          .toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final subscribersJson = prefs.getString(_subscribersKey);
      if (subscribersJson == null) return [];

      final List<dynamic> subscribersList = jsonDecode(subscribersJson);
      return subscribersList
          .map((json) => SubscriberModel.fromJson(json))
          .toList();
    }
  }

  Future<List<SubscriberModel>> getSubscribersFire() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('subscribers')
          .where("adminId", isEqualTo: adminId)
          .get();
      print("admin $adminId");
      return snapshot.docs.map((doc) {
        return SubscriberModel.fromMap(doc.data());
      }).toList();
    } catch (e) {
      print('Error fetching subscribers from Firestore: $e');
      return [];
    }
  }

  Future<void> saveSubscribers(List<SubscriberModel> subscribers) async {
    if (_useSQLite) {
      // Delete all existing subscribers and insert new ones
      final db = await _sqliteService.database;
      await db.delete('subscribers');

      for (var subscriber in subscribers) {
        await _sqliteService.insertSubscriber(subscriber.toJson());
      }
    } else {
      final prefs = await SharedPreferences.getInstance();
      final subscribersJson = jsonEncode(
        subscribers.map((sub) => sub.toJson()).toList(),
      );
      await prefs.setString(_subscribersKey, subscribersJson);
    }
  }

  Future<void> addSubscriber(SubscriberModel subscriber, {bool isSyncUpdate = false}) async {
    final SubscriberModel newSub = subscriber.copyWith(adminId: adminId);
    await _firestore
        .collection('subscribers')
        .doc(newSub.id)
        .set(newSub.toMap());

    // Logic for adding user to external servers (SAS, MikroTik, etc.)
    // has been moved to the UI layer (e.g., _saveSubscriber in subscribers_page.dart)
    // to ensure the correct service is called for the correct server type.
    print('Subscriber ${subscriber.fullName} saved to database.');
  }
  Future<void> updateSubscriber(SubscriberModel subscriber, {bool isSyncUpdate = false}) async {
    SubscriberModel? oldSubscriber;
    final oldSubscriberMap = await _sqliteService.getSubscriberById(
      subscriber.id,
    );
    oldSubscriber = oldSubscriberMap != null
        ? SubscriberModel.fromJson(oldSubscriberMap)
        : null;

    String specificAction = 'تعديل'; // Default to generic modification
    try {
      await _firestore
          .collection('subscribers')
          .doc(subscriber.id)
          .update(subscriber.toMap());
      
      // Only update SAS if this is not a sync update AND the subscriber has a server
      // Check if this is a local subscriber (no server connection needed)
      bool isLocalSubscriber = subscriber.username.isEmpty || 
                              (subscriber.earthlinkUserIndex == null || subscriber.earthlinkUserIndex!.isEmpty) && 
                              subscriber.sasServerId == null;
      
      if (!isSyncUpdate && !isLocalSubscriber) {
        // Check if this is an Earthlink user - if so, skip SAS API calls
        if (subscriber.earthlinkUserIndex != null && subscriber.earthlinkUserIndex!.isNotEmpty) {
          print('📱 تخطي تحديث SAS لمستخدم Earthlink: ${subscriber.fullName}');
        } else {
          // This is a SAS user, proceed with SAS API update
          try {
            await _sasApiService.login();
            if (oldSubscriber != null) {
              await _sasApiService.updateUser(oldSubscriber.username, subscriber.username);
            }
          } catch (e) {
            print('SAS غير متوفر أو فشل التحديث: $e');
            // تجاهل الخطأ ولا توقف العملية
          }
        }
      } else if (isLocalSubscriber) {
        print('🏠 المستخدم محلي (بدون خادم) - تخطي اتصال SAS');
      }
    } catch (e) {
      print(subscriber.id);
      print('Error updating subscriber: $e');
      rethrow;
    }

    // Only send notifications if this is not a sync update
    if (!isSyncUpdate) {
      if (oldSubscriber != null) {
        if (oldSubscriber.packageId != subscriber.packageId) {
          specificAction = 'تغيير باقة';
        } else if (oldSubscriber.isActive != subscriber.isActive) {
          specificAction = subscriber.isActive ? 'تفعيل' : 'إلغاء تفعيل';
        } else if (oldSubscriber.phoneNumber != subscriber.phoneNumber) {
          specificAction = 'تغيير رقم هاتف';
        } else if (oldSubscriber.fullName != subscriber.fullName) {
          specificAction = 'تغيير اسم المشترك';
        } else if (oldSubscriber.address != subscriber.address) {
          specificAction = 'تغيير عنوان المشترك';
        }
      }

      PackageModel? package;
      if (subscriber.packageId.isNotEmpty) {
        package = await getPackageById(subscriber.packageId);
      }

      if (package != null) {
        TelegramService()
            .sendPackageNotification(
              action: specificAction,
              packageName: package.name,
              price: package.price,
              speed: package.speed,
            )
            .catchError((error) {
              // Silently handle telegram errors to prevent app freeze
              print('Telegram notification error: $error');
            });
      }
    }
  }

  Future<void> deleteSubscriber(String subscriberId) async {
    try {
      await FirebaseFirestore.instance
          .collection('subscribers')
          .doc(subscriberId)
          .delete();
    } catch (e) {
      print('Error deleting package: $e');
    }
  }

  // Activity log operations
  Future<List<ActivityLogModel>> getActivityLogs() async {
    if (_useSQLite) {
      final logMaps = await _sqliteService.getActivityLogs();
      return logMaps.map((map) => ActivityLogModel.fromJson(map)).toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final logsJson = prefs.getString(_activityLogsKey);
      if (logsJson == null) return [];

      final List<dynamic> logsList = jsonDecode(logsJson);
      return logsList.map((json) => ActivityLogModel.fromJson(json)).toList();
    }
  }

  Future<List<ActivityLogModel>> getActivityLogsFire() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('activityLog')
          .where('adminId', isEqualTo: adminId)
          .get();

      return snapshot.docs.map((doc) {
        return ActivityLogModel.fromMap(doc.data());
      }).toList();
    } catch (e) {
      print('Error fetching packages from Firestore: $e');
      return [];
    }
  }

  Future<void> addActivityLog(ActivityLogModel log) async {
    await _firestore.collection('activityLog').doc(log.id).set(log.toMap());
  }

  // Payment record operations
  Future<List<PaymentRecordModel>> getPaymentRecords() async {
    if (_useSQLite) {
      final recordMaps = await _sqliteService.getPaymentRecords();
      return recordMaps.map((map) => PaymentRecordModel.fromJson(map)).toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getString(_paymentRecordsKey);
      if (recordsJson == null) return [];

      final List<dynamic> recordsList = jsonDecode(recordsJson);
      return recordsList
          .map((json) => PaymentRecordModel.fromJson(json))
          .toList();
    }
  }

  Future<List<PaymentRecordModel>> getPaymentRecordsFire() async {
    final snapshot = await FirebaseFirestore.instance
        .collection('paymentRecord')
        .where("adminId", isEqualTo: adminId)
        .get();

    return snapshot.docs.map((doc) {
      return PaymentRecordModel.fromMap(doc.data());
    }).toList();
  }

  Future<void> addPaymentRecord(PaymentRecordModel record) async {
    PaymentRecordModel newRecord = record.copyWith(adminId: adminId);
    await _firestore
        .collection('paymentRecord')
        .doc(newRecord.id)
        .set(newRecord.toMap());
    // if (_useSQLite) {
    //   await _sqliteService.insertPaymentRecord(record.toJson());
    // } else {
    //   final records = await getPaymentRecordsFire();
    //   records.add(record);
    //   final prefs = await SharedPreferences.getInstance();
    //   final recordsJson = jsonEncode(records.map((r) => r.toJson()).toList());
    //   await prefs.setString(_paymentRecordsKey, recordsJson);
    // }
    final subscriber = await getSubscriberById(record.subscriberId);
    if (subscriber != null) {
      // Check if this payment record is part of a renewal or debt adjustment
      // If so, we assume sendSubscriberNotification is handled by the caller
      // and we should not send a duplicate sendPaymentNotification.
      final isRenewalOrDebtAdjustment = 
          ((record.notes?.contains('تجديد') ?? false) ||
          (record.notes?.contains('دين سابق') ?? false) ||
          (record.notes?.contains('سداد دين') ?? false));
      if (!isRenewalOrDebtAdjustment) {
        TelegramService()
            .sendPaymentNotification(
              subscriberName: subscriber.fullName,
              subscriberPhone: subscriber.phoneNumber,
              amount: record.amount,
              paymentMethod: record.paymentMethod,
              notes: record.notes,
            )
            .catchError((error) {
              // Silently handle telegram errors to prevent app freeze
              print('Telegram notification error: $error');
            });
      }
    }
  }

  // Backup and restore
  // Create a JSON backup
  Future<Map<String, dynamic>> createJsonBackup() async {
    final users = await getUsers();
    final packages = await getPackages();
    final subscribers = await getSubscribers();
    final activityLogs = await getActivityLogs();
    final paymentRecords = await getPaymentRecords();
    final expenseCategories = await getExpenseCategories(); // New
    final expenses = await getExpenses(); // New

    return {
      'users': users.map((u) => u.toJson()).toList(),
      'packages': packages.map((p) => p.toJson()).toList(),
      'subscribers': subscribers.map((s) => s.toJson()).toList(),
      'activityLog': activityLogs.map((l) => l.toJson()).toList(),
      'paymentRecords': paymentRecords.map((r) => r.toJson()).toList(),
      'expenseCategories': expenseCategories
          .map((c) => c.toJson())
          .toList(), // New
      'expenses': expenses.map((e) => e.toJson()).toList(), // New
      'backupDate': DateTime.now().toIso8601String(),
    };
  }

  // Save JSON backup to a file
  Future<String> saveJsonBackupToFile(String destinationPath) async {
    final backupData = await createJsonBackup();
    final backupJson = jsonEncode(backupData);

    final backupFileName =
        'isp_manager_backup_${DateTime.now().millisecondsSinceEpoch}.json';
    final backupFilePath = path_lib.join(destinationPath, backupFileName);

    final file = File(backupFilePath);
    await file.writeAsString(backupJson, flush: true);

    return backupFilePath;
  }

  // Share JSON backup
  Future<void> shareDatabaseAsJson() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final backupFilePath = await saveJsonBackupToFile(tempDir.path);

      await Share.shareXFiles([
        XFile(backupFilePath),
      ], text: 'نسخة احتياطية من بيانات تطبيق إدارة المشتركين');
    } catch (e) {
      print('Error sharing database as JSON: $e');
      // Optionally show a user-friendly message
    }
  }

  // Create a SQLite database backup
  Future<String> createSqliteBackup(String destinationPath) async {
    if (_useSQLite) {
      return await _sqliteService.createDatabaseBackup(destinationPath);
    } else {
      // If not using SQLite, migrate to SQLite first, then create backup
      await migrateToSQLite();
      return await _sqliteService.createDatabaseBackup(destinationPath);
    }
  }

  // Restore from JSON backup data
  Future<void> restoreFromJsonBackup(Map<String, dynamic> backup) async {
    try {
      final users = (backup['users'] as List)
          .map((json) => UserModel.fromJson(json))
          .toList();
      final packages = (backup['packages'] as List)
          .map((json) => PackageModel.fromJson(json))
          .toList();
      final subscribers = (backup['subscribers'] as List)
          .map((json) => SubscriberModel.fromJson(json))
          .toList();

      // Clear existing data
      if (_useSQLite) {
        await _sqliteService.clearAllTables();
      }

      await saveUsers(users);
      await savePackages(packages);
      await saveSubscribers(subscribers);

      if (backup.containsKey('activityLog')) {
        final logs = (backup['activityLog'] as List)
            .map((json) => ActivityLogModel.fromJson(json))
            .toList();

        if (_useSQLite) {
          for (var log in logs) {
            await _sqliteService.insertActivityLog(log.toJson());
          }
        } else {
          final prefs = await SharedPreferences.getInstance();
          final logsJson = jsonEncode(logs.map((l) => l.toJson()).toList());
          await prefs.setString(_activityLogsKey, logsJson);
        }
      }

      if (backup.containsKey('paymentRecords')) {
        final records = (backup['paymentRecords'] as List)
            .map((json) => PaymentRecordModel.fromJson(json))
            .toList();

        if (_useSQLite) {
          for (var record in records) {
            await _sqliteService.insertPaymentRecord(record.toJson());
          }
        } else {
          final prefs = await SharedPreferences.getInstance();
          final recordsJson = jsonEncode(
            records.map((r) => r.toJson()).toList(),
          );
          await prefs.setString(_paymentRecordsKey, recordsJson);
        }
      }

      // Restore expense categories
      if (backup.containsKey('expenseCategories')) {
        final categories = (backup['expenseCategories'] as List)
            .map((json) => ExpenseCategoryModel.fromJson(json))
            .toList();
        await saveExpenseCategories(categories);
      }

      // Restore expenses
      if (backup.containsKey('expenses')) {
        final expenses = (backup['expenses'] as List)
            .map((json) => ExpenseModel.fromJson(json))
            .toList();
        await saveExpenses(expenses);
      }
    } catch (e) {
      throw Exception('فشل في استعادة البيانات: ${e.toString()}');
    }
  }

  // Restore from JSON backup file
  Future<void> restoreFromJsonFile(String filePath) async {
    try {
      final file = File(filePath);

      if (!await file.exists()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      final fileSize = await file.length();
      if (fileSize == 0) {
        throw Exception('ملف النسخة الاحتياطية فارغ');
      }

      final jsonString = await file.readAsString();

      if (jsonString.trim().isEmpty) {
        throw Exception('ملف النسخة الاحتياطية فارغ أو لا يحتوي على بيانات');
      }

      final backupData = jsonDecode(jsonString) as Map<String, dynamic>;

      // Validate that the backup contains the expected structure
      if (!backupData.containsKey('users') ||
          !backupData.containsKey('packages') ||
          !backupData.containsKey('subscribers')) {
        throw Exception(
          'ملف النسخة الاحتياطية لا يحتوي على البنية المطلوبة (users, packages, subscribers)',
        );
      }
      // Also check for new expense tables
      if (!backupData.containsKey('expenseCategories') ||
          !backupData.containsKey('expenses')) {
        // This is not a critical error, as older backups might not have these.
        // We can log a warning or handle it gracefully. For now, just proceed.
      }

      await restoreFromJsonBackup(backupData);
    } catch (e) {
      if (e.toString().contains('FormatException')) {
        throw Exception('ملف JSON غير صحيح أو تالف');
      }
      throw Exception('فشل في قراءة ملف النسخة الاحتياطية: ${e.toString()}');
    }
  }

  // Restore from SQLite database file
  Future<void> restoreFromSqliteFile(String filePath) async {
    try {
      // Ensure we're using SQLite
      _useSQLite = true;

      // Restore the database
      await _sqliteService.restoreFromDatabaseBackup(filePath);
    } catch (e) {
      throw Exception('فشل في استعادة قاعدة البيانات: ${e.toString()}');
    }
  }

  Future<void> resetDatabase() async {
    await _sqliteService.clearAllTables();

    // await initializeDatabase();
    TelegramService()
        .sendSystemNotification(
          title: 'إعادة ضبط التطبيق',
          description: 'تمت إعادة ضبط جميع البيانات في التطبيق بنجاح.',
        )
        .catchError((error) {
          // Silently handle telegram errors to prevent app freeze
          print('Telegram notification error: $error');
        });
  }

  Future<void> deleteDocsByAdminIdAcrossCollections() async {
    final firestore = FirebaseFirestore.instance;
    final collections = [
      'activityLog',
      'expenseCategory',
      'expenses',
      "message_templates",
      "mikrotik_devices",
      "network_devices",
      "packages",
      "paymentRecord",
      "sas_servers",
      "subscribers",
    ];

    for (final collectionName in collections) {
      while (true) {
        final snapshot = await firestore
            .collection(collectionName)
            .where('adminId', isEqualTo: adminId)
            .get();

        if (snapshot.docs.isEmpty) break;

        final batch = firestore.batch();
        for (final doc in snapshot.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();

        print('Deleted ${snapshot.docs.length} docs from $collectionName');
      }
    }
  }

  // Method to migrate from SharedPreferences to SQLite
  Future<void> migrateToSQLite() async {
    if (!_useSQLite) {
      _useSQLite = true;

      // Get all data from SharedPreferences
      final prefs = await SharedPreferences.getInstance();

      // Check if there's data in SharedPreferences
      if (prefs.getString(_usersKey) != null) {
        // Get all data
        final users = await getUsers();
        final packages = await getPackagesFire();
        final subscribers = await getSubscribersFire();
        final activityLogs = await getActivityLogsFire();
        final paymentRecords = await getPaymentRecordsFire();
        final expenseCategories = await getExpenseCategories(); // New
        final expenses = await getExpensesFire(); // New

        // Clear SQLite tables
        await _sqliteService.clearAllTables();

        // Insert data into SQLite
        for (var user in users) {
          await _sqliteService.insertUser(user.toJson());
        }

        for (var package in packages) {
          await _sqliteService.insertPackage(package.toJson());
        }

        for (var subscriber in subscribers) {
          await _sqliteService.insertSubscriber(subscriber.toJson());
        }

        for (var log in activityLogs) {
          await _sqliteService.insertActivityLog(log.toJson());
        }

        for (var record in paymentRecords) {
          await _sqliteService.insertPaymentRecord(record.toJson());
        }

        for (var category in expenseCategories) {
          // New
          await _sqliteService.insertExpenseCategory(category.toJson());
        }

        for (var expense in expenses) {
          // New
          await _sqliteService.insertExpense(expense.toJson());
        }
      }
    }
  }

  // Message template operations
  Future<List<MessageTemplateModel>> getMessageTemplates() async {
    if (_useSQLite) {
      final templateMaps = await _sqliteService.getMessageTemplates();
      return templateMaps
          .map((map) => MessageTemplateModel.fromJson(map))
          .toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final templatesJson = prefs.getString(_messageTemplatesKey);
      if (templatesJson == null) return [];

      final List<dynamic> templatesList = jsonDecode(templatesJson);
      return templatesList
          .map((json) => MessageTemplateModel.fromJson(json))
          .toList();
    }
  }

  Future<List<MessageTemplateModel>> getMessageTemplatesFire() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('message_templates')
          .where('adminId', isEqualTo: adminId)
          .get();

      return snapshot.docs.map((doc) {
        return MessageTemplateModel.fromMap(doc.data());
      }).toList();
    } catch (e) {
      print('Error fetching Messages from Firestore: $e');
      return [];
    }
  }

  Future<void> saveMessageTemplates(
    List<MessageTemplateModel> templates,
  ) async {
    if (_useSQLite) {
      // Delete all existing templates and insert new ones
      final db = await _sqliteService.database;
      await db.delete('message_templates');

      for (var template in templates) {
        await _sqliteService.insertMessageTemplate(template.toJson());
      }
    } else {
      final prefs = await SharedPreferences.getInstance();
      final templatesJson = jsonEncode(
        templates.map((t) => t.toJson()).toList(),
      );
      await prefs.setString(_messageTemplatesKey, templatesJson);
    }
  }

  Future<void> addMessageTemplate(MessageTemplateModel template) async {
    MessageTemplateModel newMess = template.copyWith(adminId: adminId);
    await _firestore
        .collection('message_templates')
        .doc(newMess.id)
        .set(newMess.toMap());
  }

  Future<void> updateMessageTemplate(MessageTemplateModel template) async {
    try {
      await FirebaseFirestore.instance
          .collection('message_templates')
          .doc(template.id) // لازم الـ id يكون مضبوط
          .update(template.toMap());
    } catch (e) {
      print('Error updating package: $e');
    }
  }

  Future<void> deleteMessageTemplate(String templateId) async {
    if (_useSQLite) {
      await FirebaseFirestore.instance
          .collection('message_templates')
          .doc(templateId)
          .delete();
    } else {
      final templates = await getMessageTemplates();
      templates.removeWhere((t) => t.id == templateId);
      await saveMessageTemplates(templates);
    }
  }

  Future<List<MessageTemplateModel>> getMessageTemplatesByType(
    MessageTemplateType type,
  ) async {
    final templates = await getMessageTemplatesFire();
    return templates.where((t) => t.type == type).toList();
  }

  Future<MessageTemplateModel?> getDefaultMessageTemplate(
    MessageTemplateType type,
  ) async {
    final templates = await getMessageTemplatesByType(type);
    try {
      return templates.firstWhere((t) => t.isDefault);
    } catch (e) {
      return templates.isNotEmpty ? templates.first : null;
    }
  }

  // Expense Category operations
  Future<List<ExpenseCategoryModel>> getExpenseCategories() async {
    if (_useSQLite) {
      final categoryMaps = await _sqliteService.getExpenseCategories();
      return categoryMaps
          .map((map) => ExpenseCategoryModel.fromJson(map))
          .toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getString(_expenseCategoriesKey);
      if (categoriesJson == null) return [];

      final List<dynamic> categoriesList = jsonDecode(categoriesJson);
      return categoriesList
          .map((json) => ExpenseCategoryModel.fromJson(json))
          .toList();
    }
  }

  Future<List<ExpenseCategoryModel>> getExpenseCategoriesFire() async {
    final snapshot = await FirebaseFirestore.instance
        .collection('expenseCategory')
        .get();

    return snapshot.docs.map((doc) {
      return ExpenseCategoryModel.fromMap(doc.data());
    }).toList();
  }

  Future<void> saveExpenseCategories(
    List<ExpenseCategoryModel> categories,
  ) async {
    if (_useSQLite) {
      final db = await _sqliteService.database;
      await db.delete('expense_categories');
      for (var category in categories) {
        await _sqliteService.insertExpenseCategory(category.toJson());
      }
    } else {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = jsonEncode(
        categories.map((cat) => cat.toJson()).toList(),
      );
      await prefs.setString(_expenseCategoriesKey, categoriesJson);
    }
  }

  Future<void> addExpenseCategory(ExpenseCategoryModel category) async {
    try {
      // if (_useSQLite) {
      //   await _sqliteService.insertExpenseCategory(category.toJson());
      // } else {
      //   final categories = await getExpenseCategories();
      //   categories.add(category);
      //   await saveExpenseCategories(categories);
      // }
      await _firestore
          .collection('expenseCategory')
          .doc(category.id)
          .set(category.toMap());
      // Send Telegram notification
      TelegramService()
          .sendExpenseNotification(
            action: 'إضافة فئة',
            description: 'فئة مصروف جديدة: ${category.name}',
            categoryName: category.name,
            notes: category
                .name, // Changed from category.description to category.name
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } catch (e) {
      // Re-throw database errors
      rethrow;
    }
  }

  Future<void> updateExpenseCategory(ExpenseCategoryModel category) async {
    try {
      await FirebaseFirestore.instance
          .collection('expenseCategory')
          .doc(category.id) // لازم الـ id يكون مضبوط
          .update(category.toMap());
      // if (_useSQLite) {
      //   await _sqliteService.updateExpenseCategory(category.toJson());
      // } else {
      //   final categories = await getExpenseCategories();
      //   final index = categories.indexWhere((c) => c.id == category.id);
      //   if (index != -1) {
      //     categories[index] = category;
      //     await saveExpenseCategories(categories);
      //   }
      // }

      // Send Telegram notification
      TelegramService()
          .sendExpenseNotification(
            action: 'تعديل فئة',
            description: 'تم تعديل فئة المصروف: ${category.name}',
            categoryName: category.name,
            notes: category
                .name, // Changed from category.description to category.name
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } catch (e) {
      // Re-throw database errors
      rethrow;
    }
  }

  Future<void> deleteExpenseCategory(String categoryId) async {
    try {
      // Get category details before deletion for notification
      final categories = await getExpenseCategoriesFire();
      final categoryToDelete = categories.firstWhere(
        (c) => c.id == categoryId,
        orElse: () => ExpenseCategoryModel(
          adminId: adminId,
          id: '',
          name: 'فئة محذوفة',
          createdAt: DateTime.now(),
        ),
      ); // Added createdAt

      await FirebaseFirestore.instance
          .collection('expenseCategory')
          .doc(categoryId)
          .delete();

      // Send Telegram notification
      TelegramService()
          .sendExpenseNotification(
            action: 'حذف فئة',
            description: 'تم حذف فئة المصروف: ${categoryToDelete.name}',
            categoryName: categoryToDelete.name,
            notes: categoryToDelete
                .name, // Changed from categoryToDelete.description to categoryToDelete.name
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } catch (e) {
      // Re-throw database errors
      rethrow;
    }
  }

  Future<ExpenseCategoryModel?> getExpenseCategoryById(String id) async {
    if (_useSQLite) {
      final categoryMap = await _sqliteService.getExpenseCategoryById(id);
      return categoryMap != null
          ? ExpenseCategoryModel.fromJson(categoryMap)
          : null;
    } else {
      final categories = await getExpenseCategories();
      try {
        return categories.firstWhere((c) => c.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  // Expense operations
  Future<List<ExpenseModel>> getExpenses() async {
    if (_useSQLite) {
      final expenseMaps = await _sqliteService.getExpenses();
      return expenseMaps.map((map) => ExpenseModel.fromJson(map)).toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final expensesJson = prefs.getString(_expensesKey);
      if (expensesJson == null) return [];

      final List<dynamic> expensesList = jsonDecode(expensesJson);
      return expensesList.map((json) => ExpenseModel.fromJson(json)).toList();
    }
  }

  Future<List<ExpenseModel>> getExpensesFire() async {
    final snapshot = await FirebaseFirestore.instance
        .collection('expenses')
        .where("adminId", isEqualTo: adminId)
        .get();

    return snapshot.docs.map((doc) {
      return ExpenseModel.fromMap(doc.data());
    }).toList();
  }

  Future<void> saveExpenses(List<ExpenseModel> expenses) async {
    if (_useSQLite) {
      final db = await _sqliteService.database;
      await db.delete('expenses');
      for (var expense in expenses) {
        await _sqliteService.insertExpense(expense.toJson());
      }
    } else {
      final prefs = await SharedPreferences.getInstance();
      final expensesJson = jsonEncode(
        expenses.map((exp) => exp.toJson()).toList(),
      );
      await prefs.setString(_expensesKey, expensesJson);
    }
  }

  Future<void> addExpense(ExpenseModel expense) async {
    try {
      await _firestore
          .collection('expenses')
          .doc(expense.id)
          .set(expense.toMap());
      // if (_useSQLite) {
      //   await _sqliteService.insertExpense(expense.toJson());
      // } else {
      //   final expenses = await getExpenses();
      //   expenses.add(expense);
      //   await saveExpenses(expenses);
      // }
      // Send Telegram notification
      final categories = await getExpenseCategories();
      final category = categories.firstWhere(
        (cat) => cat.id == expense.categoryId,
        orElse: () => ExpenseCategoryModel(
          adminId: adminId,
          id: '',
          name: 'غير محدد',
          createdAt: DateTime.now(),
        ),
      );

      TelegramService()
          .sendExpenseNotification(
            action: 'إضافة مصروف',
            description: expense
                .notes, // Changed from expense.description to expense.notes
            categoryName: category.name,
            amount: expense.amount,
            notes: expense.notes,
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } catch (e) {
      // Re-throw database errors
      rethrow;
    }
  }

  Future<double> getTotalExpensesAmount() async {
    final expenses = await getExpensesFire();
    return expenses.fold<double>(
      0.0,
      (double sum, ExpenseModel expense) => sum + expense.amount,
    );
  }

  Future<double> getTotalCollectedAmount() async {
    // if (_useSQLite) {
    //     final db = await _sqliteService.database;
    //     final result = await db.rawQuery('''
    //       SELECT SUM(amount) as total
    //       FROM payment_records
    //     ''');
    //     print(result.first['total']);
    //     return (result.first['total'] as double?) ?? 0.0;
    //   } else {}
    final paymentRecords = await getPaymentRecordsFire();

    return paymentRecords.fold<double>(0.0, (
      double sum,
      PaymentRecordModel record,
    ) {
      return sum + record.amount;
    });
  }

  Future<double> getCurrentBalance() async {
    final totalCollected = await getTotalCollectedAmount();
    final totalExpenses = await getTotalExpensesAmount();
    return totalCollected - totalExpenses;
  }

  String generateId() => _uuid.v4();

  // =========================================================================
  // EFFICIENT EARTHLINK SYNC (NEW IMPLEMENTATION)
  // =========================================================================

  /// Synchronizes packages and subscribers from Earthlink API to local database.
  /// This is the main entry point for the sync process.
  Future<bool> syncFromEarthlink({
    EarthlinkService? earthlinkService,
    String? username,
    String? password,
  }) async {
    print('==== [SYNC] syncFromEarthlink CALLED ====');
    try {
      EarthlinkService service;
      
      if (earthlinkService != null) {
        service = earthlinkService;
        print('Using provided authenticated EarthlinkService instance');
      } else {
        service = EarthlinkService();
        if (username != null && password != null) {
          print('Attempting to login to Earthlink with provided credentials...');
          final loginResult = await service.login(
            username: username,
            password: password,
          );
          if (!loginResult['success']) {
            print('Failed to login to Earthlink: ${loginResult['error']}');
            return false;
          }
          print('Earthlink login successful');
        } else {
          print('No authenticated service or credentials provided. Skipping Earthlink sync.');
          return true;
        }
      }
      
      if (!service.isLoggedIn) {
        print('Earthlink API not logged in. Skipping Earthlink sync.');
        return true;
      }
      print('Earthlink API Login successful. Starting efficient synchronization...');

      // Call the new efficient sync method
      return await _syncFromEarthlinkEfficiently(service);

    } catch (e) {
      print('==== [SYNC] Error in syncFromEarthlink: $e ====');
      return false;
    }
  }

  /// Private method containing the new efficient synchronization logic.
  Future<bool> _syncFromEarthlinkEfficiently(EarthlinkService earthlinkService) async {
    print('==== [SYNC EFFICIENT] Starting efficient synchronization ====');

    try {
      // Step 1: Sync packages from Earthlink to local/Firebase DB.
      await _syncPackages(earthlinkService);
      final localPackages = await getPackagesFire();
      print('==== [SYNC EFFICIENT] Found ${localPackages.length} local packages after sync ====');

      // Step 2: Fetch all subscribers from Earthlink using the efficient paginated endpoint.
      final usersResult = await earthlinkService.fetchAllUsersWithPagination(
        userStatusID: 0, // All users
        batchSize: 100,
      );

      if (!usersResult['success'] || !(usersResult['data'] is List)) {
        print('==== [SYNC EFFICIENT] Failed to fetch users from Earthlink: ${usersResult['error']} ====');
        return false;
      }
      
      final List<EarthlinkUser> allEarthlinkUsers = usersResult['data'] as List<EarthlinkUser>;
      print('==== [SYNC EFFICIENT] Found ${allEarthlinkUsers.length} users from Earthlink ====');

      // Step 3: Get all current subscribers from Firestore to compare.
      final currentLocalSubscribers = await getSubscribersFire();
      final Map<String, SubscriberModel> localSubscribersMap = {
        for (var sub in currentLocalSubscribers) 
          if (sub.earthlinkUserIndex != null && sub.earthlinkUserIndex!.isNotEmpty) 
            sub.earthlinkUserIndex!: sub
      };
      print('==== [SYNC EFFICIENT] Found ${currentLocalSubscribers.length} existing subscribers in Firestore ====');

      List<SubscriberModel> subscribersToSave = [];
      int newSubscribersCount = 0;
      int updatedSubscribersCount = 0;

      // Step 4: Process all fetched Earthlink users without any extra API calls.
      for (var earthlinkUser in allEarthlinkUsers) {
        final userIndex = earthlinkUser.userIndex?.toString();
        if (userIndex == null || userIndex.isEmpty) {
          print('==== [SYNC EFFICIENT] Skipping user with empty userIndex. UserID: ${earthlinkUser.userID} ====');
          continue;
        }

        // Directly extract data from the fetched EarthlinkUser object.
        String userId = earthlinkUser.userID ?? '';
        String? displayName = earthlinkUser.displayName;
        String? mobileNumber = earthlinkUser.mobileNumber;
        String accountName = earthlinkUser.accountName ?? 'باقة غير معروفة';
        DateTime? expirationDate = earthlinkUser.subscriptionEnd; // FIX: Use the correct field from the model

        String fullName = (displayName != null && displayName.isNotEmpty) ? displayName : _createNameFromId(userId);
        String phoneNumber = (mobileNumber != null && mobileNumber.isNotEmpty) ? mobileNumber : 'غير محدد';

        PackageModel? matchedPackage;
        try {
          matchedPackage = localPackages.firstWhere((p) => p.name == accountName);
        } catch (e) {
          matchedPackage = null;
        }

        final existingSubscriber = localSubscribersMap[userIndex];

        if (existingSubscriber != null) {
          // Update existing subscriber
          updatedSubscribersCount++;
          final updatedSub = existingSubscriber.copyWith(
            adminId: adminId, // FIX: Added missing adminId
            fullName: fullName,
            phoneNumber: phoneNumber,
            username: userId,
            packageId: matchedPackage?.id ?? existingSubscriber.packageId,
            packageName: accountName,
            subscriptionEnd: expirationDate,
            isActive: earthlinkUser.isActive ?? existingSubscriber.isActive,
          );
          subscribersToSave.add(updatedSub);
        } else {
          // Add new subscriber
          newSubscribersCount++;
          final newSub = SubscriberModel(
            id: _uuid.v4(),
            adminId: adminId,
            earthlinkUserIndex: userIndex,
            fullName: fullName,
            phoneNumber: phoneNumber,
            username: userId,
            packageId: matchedPackage?.id ?? 'unknown_package',
            packageName: accountName,
            subscriptionStart: DateTime.now(), // FIX: Added required subscriptionStart
            subscriptionEnd: expirationDate,
            address: 'العراق - غير محدد',
            debtAmount: 0,
            createdAt: DateTime.now(),
            isActive: earthlinkUser.isActive ?? true,
            paymentStatus: PaymentStatus.pending,
            subscriptionType: SubscriptionType.broadband,
          );
          subscribersToSave.add(newSub);
        }
      }

      print('==== [SYNC EFFICIENT] Processing complete. New: $newSubscribersCount, Updated: $updatedSubscribersCount ====');

      // Step 5: Save all new and updated subscribers to the database.
      await _saveSubscribersToDatabase(subscribersToSave);
      
      print('==== [SYNC EFFICIENT] Synchronization finished successfully. ====');
      return true;

    } catch (e) {
      print('==== [SYNC EFFICIENT] An error occurred during efficient sync: $e ====');
      return false;
    }
  }

  /// Helper method to synchronize packages from Earthlink.
  Future<void> _syncPackages(EarthlinkService service) async {
    print('==== [SYNC PACKAGES] Fetching accounts from Earthlink ====');
    final accountsResult = await service.getAllAccounts();
    if (accountsResult['success'] && accountsResult['data'] is List) {
      final accounts = accountsResult['data'] as List<EarthlinkAccount>;
      print('==== [SYNC PACKAGES] Found ${accounts.length} accounts from Earthlink ====');
      
      final currentLocalPackages = await getPackagesFire();
      
      for (var account in accounts) {
        PackageModel? existingPackage;
        try {
          existingPackage = currentLocalPackages.firstWhere(
            (p) =>
                p.earthlinkAccountId == account.accountIndex?.toString() ||
                p.name == account.accountName,
          );
        } catch (e) {
          existingPackage = null;
        }

        if (existingPackage != null) {
          final updatedPackage = existingPackage.copyWith(
              adminId: adminId, // FIX: Added missing adminId
              name: account.accountName,
              price: account.accountCost,
              durationInDays: account.durationDays ?? 30,
              speed: account.speed ?? '',
              deviceCount: account.deviceLimit ?? 1,
              notes: account.description ?? '',
              isActive: account.isActive ?? true,
              earthlinkAccountId: account.accountIndex?.toString(),
            );
          await updatePackage(updatedPackage);
        } else {
          final newPackage = PackageModel(
              adminId: adminId, // FIX: Added missing adminId
              id: _firestore.collection('packages').doc().id,
              serverId: account.accountIndex?.toString() ?? '',
              name: account.accountName,
              price: account.accountCost,
              durationInDays: account.durationDays ?? 30,
              speed: account.speed ?? '',
              deviceCount: account.deviceLimit ?? 1,
              notes: account.description ?? '',
              createdAt: DateTime.now(),
              isActive: account.isActive ?? true,
              earthlinkAccountId: account.accountIndex?.toString(),
            );
          await addPackage(newPackage);
        }
      }
      print('==== [SYNC PACKAGES] Packages synchronized successfully. ====');
    } else {
      print('==== [SYNC PACKAGES] No accounts found on Earthlink API or failed to fetch. ====');
    }
  }

  // --- Helper functions for efficient sync ---

  /// Helper function to create a default name from a username.
  String _createNameFromId(String userId) {
    if (userId.contains('@')) {
      return userId.split('@').first;
    }
    return userId;
  }

  /// Synchronize data from SAS Radius server
  Future<bool> syncFromSas() async {
    try {
      print('==== [SYNC] Starting SAS synchronization ====');

      // Continue with user synchronization...
      return await _syncSasUsers();
    } catch (e) {
      print('==== [SYNC] Error in syncFromSas: $e ====');
      return false;
    }
  }

  /// Helper method to synchronize SAS users
  Future<bool> _syncSasUsers() async {
    // 3. Fetch and synchronize subscribers (users) from SAS API with improved pagination
    print('==== [SYNC] Fetching subscribers from SAS ====');
    List<SasUser> sasUsers = [];
    int currentPage = 1;
    int perPage = 500; // Increased back to 500 as requested
    int? totalUsers;
    int maxPages = 50; // Add safety limit to prevent infinite loops
    int consecutiveEmptyPages = 0; // Track empty pages
    int consecutiveErrors = 0; // Track consecutive errors
    bool shouldContinue = true; // Flag to control the loop

    while (shouldContinue) {
      print('==== [SYNC] Fetching SAS users - Page $currentPage (per page: $perPage) ====');
      
      try {
        final sasUsersResponse = await _sasApiService.getUsers(
          page: currentPage,
          perPage: perPage,
        );
        
        if (sasUsersResponse != null && sasUsersResponse.containsKey('data')) {
          final List<dynamic> usersData = sasUsersResponse['data'];
          final List<SasUser> currentBatch = usersData
              .map((json) => SasUser.fromJson(json))
              .toList();
          
          print('==== [SYNC] Fetched ${currentBatch.length} users from page $currentPage ====');
          
          // Reset error counter on successful request
          consecutiveErrors = 0;
          
          if (currentBatch.isNotEmpty) {
            sasUsers.addAll(currentBatch);
            consecutiveEmptyPages = 0; // Reset empty page counter
          } else {
            consecutiveEmptyPages++;
            print('==== [SYNC] Empty page detected. Consecutive empty pages: $consecutiveEmptyPages ====');
          }

          // Attempt to get total users from the response, if available
          if (sasUsersResponse.containsKey('total') &&
              sasUsersResponse['total'] is int) {
            totalUsers = sasUsersResponse['total'];
            print('==== [SYNC] Total users reported by API: $totalUsers ====');
          } else if (sasUsersResponse.containsKey('meta') &&
              sasUsersResponse['meta']['total'] is int) {
            totalUsers = sasUsersResponse['meta']['total'];
            print('==== [SYNC] Total users from meta: $totalUsers ====');
          }

          // Stop conditions
          if (currentBatch.isEmpty && currentPage > 1) {
            print('==== [SYNC] Empty page detected, stopping pagination ====');
            shouldContinue = false;
          } else if (totalUsers != null && sasUsers.length >= totalUsers) {
            print('==== [SYNC] Reached reported total users: $totalUsers ====');
            shouldContinue = false;
          } else if (consecutiveEmptyPages >= 3) {
            print('==== [SYNC] Stopping due to consecutive empty pages ====');
            shouldContinue = false;
          } else if (currentPage >= maxPages) {
            print('==== [SYNC] Reached maximum page limit ($maxPages), stopping ====');
            shouldContinue = false;
          } else {
            currentPage++;
          }
        } else {
          print('==== [SYNC] Invalid response format or no data key, stopping ====');
          shouldContinue = false;
        }
      } catch (e) {
        consecutiveErrors++;
        print('==== [SYNC] Error fetching page $currentPage: $e ====');
        print('==== [SYNC] Consecutive errors: $consecutiveErrors ====');
        
        // Stop if we've had too many consecutive errors
        if (consecutiveErrors >= 3) {
          print('==== [SYNC] Too many consecutive errors, stopping pagination ====');
          shouldContinue = false;
        } else if (currentPage > 10) {
          print('==== [SYNC] Too many errors after page 10, stopping pagination ====');
          shouldContinue = false;
        } else {
          currentPage++;
        }
      }
      
      // Add small delay between requests to prevent overwhelming the server
      if (shouldContinue) {
        await Future.delayed(Duration(milliseconds: 100));
      }
    }

    print('==== [SYNC] Total SAS users fetched: ${sasUsers.length} ====');
    
    if (sasUsers.isNotEmpty) {
      final currentLocalSubscribers = await getSubscribersFire();
      print(
        '==== [SYNC DEBUG] Current local subscribers count: ${currentLocalSubscribers.length} ====',
      );

      // Continue with existing SAS user sync logic...
      return await _processSasUsersSync(sasUsers, currentLocalSubscribers);
    } else {
      print('No SAS users found or failed to fetch.');
      return true;
    }
  }

  /// Helper method to process SAS users synchronization
  Future<bool> _processSasUsersSync(List<SasUser> sasUsers, List<SubscriberModel> currentLocalSubscribers) async {
    try {
      // Create a map for efficient lookup of existing subscribers
      final Map<String, SubscriberModel> localSubscribersMap = {};
      for (var sub in currentLocalSubscribers) {
        // Add multiple keys for each subscriber for better matching
        if (sub.sasServerId != null) {
          localSubscribersMap['sas_${sub.sasServerId}'] = sub;
        }
        if (sub.username.isNotEmpty) {
          localSubscribersMap['username_${sub.username}'] = sub;
        }
        if (sub.phoneNumber.isNotEmpty) {
          localSubscribersMap['phone_${sub.phoneNumber}'] = sub;
        }
      }

      final List<SubscriberModel> subscribersToSave = [];
      int processedCount = 0;
      
      for (var sasUser in sasUsers) {
        processedCount++;
        if (processedCount % 50 == 0) {
          print('==== [SYNC] Processed $processedCount/${sasUsers.length} users ====');
        }
        
        // Find existing subscriber and update/create as needed
        final updatedSubscriber = await _processIndividualSasUser(sasUser, localSubscribersMap);
        if (updatedSubscriber != null) {
          subscribersToSave.add(updatedSubscriber);
        }
      }
      
      // Save all subscribers
      return await _saveSubscribersToDatabase(subscribersToSave);
    } catch (e) {
      print('==== [SYNC] Error processing SAS users: $e ====');
      return false;
    }
  }

  /// Helper method to process individual SAS user during sync
  Future<SubscriberModel?> _processIndividualSasUser(
    SasUser sasUser, 
    Map<String, SubscriberModel> localSubscribersMap
  ) async {
    try {
      SubscriberModel? existingSubscriber;

      // Prioritize lookup by sasServerId first
      if (sasUser.id != null &&
          localSubscribersMap.containsKey('sas_${sasUser.id}')) {
        existingSubscriber = localSubscribersMap['sas_${sasUser.id}'];
      } else if (sasUser.username.isNotEmpty &&
          localSubscribersMap.containsKey('username_${sasUser.username}')) {
        existingSubscriber = localSubscribersMap['username_${sasUser.username}'];
      } else if (sasUser.phone != null &&
          sasUser.phone!.isNotEmpty &&
          localSubscribersMap.containsKey('phone_${sasUser.phone}')) {
        existingSubscriber = localSubscribersMap['phone_${sasUser.phone}'];
      }

      // Find the corresponding local package ID using sasProfileId
      final packages = await getPackagesFire();
      final matchingPackage = packages.firstWhere(
        (p) => p.sasProfileId == sasUser.profileId?.toString(),
        orElse: () => PackageModel(
          adminId: adminId,
          id: 'unknown_package',
          serverId: "",
          name: 'غير معروف',
          price: 0,
          durationInDays: 30,
          speed: '',
          deviceCount: 0,
          createdAt: DateTime.now(),
        ),
      );

      if (existingSubscriber != null) {
        // Update existing subscriber while preserving debt amount and other financial data
        return existingSubscriber.copyWith(
          adminId: adminId,
          fullName: sasUser.fullName,
          phoneNumber: sasUser.phone ?? existingSubscriber.phoneNumber,
          username: sasUser.username,
          password: existingSubscriber.password,
          packageId: matchingPackage.id,
          packageName: matchingPackage.name,
          subscriptionEnd: sasUser.expiration,
          isActive: sasUser.isActive,
          sasServerId: sasUser.id?.toString(),
          // Preserve existing financial data
          debtAmount: existingSubscriber.debtAmount,
          paymentStatus: existingSubscriber.paymentStatus,
          address: existingSubscriber.address,
          macAddress: existingSubscriber.macAddress,
          routerName: existingSubscriber.routerName,
          technicalNotes: existingSubscriber.technicalNotes,
          subscriptionStart: existingSubscriber.subscriptionStart,
          subscriptionType: existingSubscriber.subscriptionType,
        );
      } else {
        // Add new subscriber
        return SubscriberModel(
          adminId: adminId,
          id: _uuid.v4(),
          fullName: sasUser.fullName,
          phoneNumber: sasUser.phone ?? '',
          packageId: matchingPackage.id,
          packageName: matchingPackage.name,
          address: sasUser.address ?? 'غير محدد',
          paymentStatus: PaymentStatus.pending,
          subscriptionStart: DateTime.now(),
          subscriptionEnd: sasUser.expiration,
          macAddress: '',
          routerName: '',
          technicalNotes: 'تمت المزامنة من SAS Radius',
          debtAmount: 0.0,
          createdAt: DateTime.now(),
          isActive: sasUser.isActive,
          subscriptionType: SubscriptionType.broadband,
          username: sasUser.username,
          password: '',
          sasServerId: sasUser.id?.toString(),
        );
      }
    } catch (e) {
      print('Error processing individual SAS user: $e');
      return null;
    }
  }

  /// Parse date string in various formats
  DateTime? _parseDateString(String dateStr) {
    try {
      // Handle different date formats
      if (dateStr.contains('T')) {
        // ISO format like "2024-11-29T17:21:00"
        return DateTime.parse(dateStr);
      } else if (dateStr.contains('/')) {
        // Handle format like "29/11/2024 05:21 PM" or "11/29/2024"
        final parts = dateStr.split(' ');
        if (parts.length >= 1) {
          final datePart = parts[0];
          final dateParts = datePart.split('/');
          if (dateParts.length == 3) {
            // Try to determine if it's DD/MM/YYYY or MM/DD/YYYY
            // We'll assume DD/MM/YYYY format based on common Middle Eastern usage
            final day = int.parse(dateParts[0]);
            final month = int.parse(dateParts[1]);
            final year = int.parse(dateParts[2]);
            
            // Validate the date parts
            if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year > 2000) {
              return DateTime(year, month, day);
            }
          }
        }
      } else if (dateStr.contains('-')) {
        // Handle format like "2024-11-29"
        final dateParts = dateStr.split('-');
        if (dateParts.length == 3) {
          final year = int.parse(dateParts[0]);
          final month = int.parse(dateParts[1]);
          final day = int.parse(dateParts[2]);
          return DateTime(year, month, day);
        }
      } else {
        // Try parsing as YYYY-MM-DD format
        return DateTime.parse(dateStr.split(' ')[0]);
      }
    } catch (e) {
      print('Error parsing date string "$dateStr": $e');
    }
    return null;
  }

  /// Helper method to save subscribers to database
  Future<bool> _saveSubscribersToDatabase(List<SubscriberModel> subscribersToSave) async {
    try {
      print('Saving ${subscribersToSave.length} subscribers to database');
      
      int savedCount = 0;
      for (var subscriber in subscribersToSave) {
        savedCount++;
        if (savedCount % 50 == 0) {
          print('Saved $savedCount/${subscribersToSave.length} subscribers');
        }
        
        final existingInDb = await getSubscriberById(subscriber.id);
        if (existingInDb != null) {
          await updateSubscriber(subscriber, isSyncUpdate: true);
        } else {
          await addSubscriber(subscriber, isSyncUpdate: true);
        }
      }
      
      print('Subscribers synchronized successfully.');
      return true;
    } catch (e) {
      print('Error saving subscribers to database: $e');
      return false;
    }
  }

  // Financial reconciliation method
  Future<void> reconcileFinancialData(
    DateTime startDate,
    DateTime endDate, 
    String adminId, {
    double? newTotalCollected,
    double? newTotalExpenses,
    String? notes,
  }) async {
    try {
      String reconciliationDetails = '';
      // مطابقة المقبوضات
      if (newTotalCollected != null) {
        // final db = await _sqliteService.database;
        // await db.delete('payment_records');
        final querySnapshot = await _firestore
            .collection("paymentRecord")
            .where("adminId", isEqualTo: adminId)
            .get();
        for (final doc in querySnapshot.docs) {
          await doc.reference.delete();
        }
        // إضافة سجل واحد جديد
        final reconciliationRecord = PaymentRecordModel(
          adminId: adminId,
          id: generateId(),
          subscriberId: 'reconciliation',
          amount: newTotalCollected,
          paymentMethod: 'مطابقة حسابات',
          paymentDate: DateTime.now(),
          recordedBy: 'النظام',
          notes:
              'مطابقة شاملة للمقبوضات${notes != null && notes.isNotEmpty ? ' - $notes' : ''}',
        );
        final reconciliationRecordExpenses = ExpenseModel(
          adminId: adminId,
          id: generateId(),
          categoryId: 'reconciliation',
          amount: newTotalExpenses ?? 0.0,

          timestamp: DateTime.now(),

          notes:
              'مطابقة شاملة للمقبوضات${notes != null && notes.isNotEmpty ? ' - $notes' : ''}',
        );
        await addPaymentRecord(reconciliationRecord);
        await addExpense(reconciliationRecordExpenses);
        final formattedAmount = await AppSettingsService.formatCurrency(
          newTotalCollected,
        );
        reconciliationDetails +=
            'تم تعيين إجمالي المقبوضات إلى $formattedAmount\n';
      }
      // مطابقة المصاريف
      if (newTotalExpenses != null) {
        final prefs = await SharedPreferences.getInstance();
        List<ExpenseModel> allExpenses = await getExpensesFire();
        allExpenses.clear();
        final reconciliationExpense = ExpenseModel(
          adminId: adminId,
          id: generateId(),
          categoryId: '',
          amount: newTotalExpenses,
          notes:
              'مطابقة شاملة للمصاريف${notes != null && notes.isNotEmpty ? ' - $notes' : ''}',
          timestamp: DateTime.now(),
        );
        allExpenses.add(reconciliationExpense);
        await prefs.setString(
          _expensesKey,
          jsonEncode(allExpenses.map((e) => e.toJson()).toList()),
        );

        final formattedAmount = await AppSettingsService.formatCurrency(
          newTotalExpenses,
        );
        reconciliationDetails +=
            'تم تعيين إجمالي المصاريف إلى $formattedAmount\n';
      }
      // إرسال إشعار تلغرام
      if (reconciliationDetails.isNotEmpty) {
        Future.microtask(
          () => TelegramService()
              .sendExpenseNotification(
                action: 'مطابقة حسابات',
                description: 'تمت مطابقة الحسابات المالية',
                notes: reconciliationDetails,
              )
              .catchError((error) {
                print('Telegram notification error: $error');
              }),
        );
      }
    } catch (e) {
      print('Error in reconcileFinancialData: $e');
      rethrow;
    }
  }

  /// Update subscriber with renewal information
  Future<void> updateSubscriberWithRenewal(
    String subscriberId,
    DateTime newEndDate,
    String packageId,
    double renewalAmount,
  ) async {
    try {
      final subscriber = await getSubscriberById(subscriberId);
      if (subscriber != null) {
        final updatedSubscriber = subscriber.copyWith(
          adminId: adminId,
          subscriptionEnd: newEndDate,
          packageId: packageId,
          paymentStatus: PaymentStatus.paid,
          debtAmount: max(0, subscriber.debtAmount - renewalAmount),
        );
        await updateSubscriber(updatedSubscriber);
        
        // Add payment record
        final payment = PaymentRecordModel(
          adminId: adminId,
          id: _uuid.v4(),
          subscriberId: subscriberId,
          amount: renewalAmount,
          paymentMethod: 'نقدي',
          paymentDate: DateTime.now(),
          recordedBy: 'النظام',
          notes: 'تجديد الاشتراك',
        );
        await addPaymentRecord(payment);
      }
    } catch (e) {
      print('Error updating subscriber with renewal: $e');
      throw e;
    }
  }

  /// Update subscriber with payment information
  Future<void> updateSubscriberWithPayment(
    SubscriberModel updatedSubscriber,
    double paymentAmount,
    String paymentMethod,
    String notes,
  ) async {
    try {
      // Update the subscriber in database
      await _firestore
          .collection('subscribers')
          .doc(updatedSubscriber.id)
          .update(updatedSubscriber.toMap());
      
      // Add payment record
      final payment = PaymentRecordModel(
        adminId: adminId,
        id: _uuid.v4(),
        subscriberId: updatedSubscriber.id,
        amount: paymentAmount,
        paymentMethod: paymentMethod,
        paymentDate: DateTime.now(),
        recordedBy: 'النظام',
        notes: notes,
      );
      await addPaymentRecord(payment);
      
      // Send specific Telegram notification for payment (non-blocking)
      TelegramService()
          .sendSubscriberNotification(
            action: 'تسجيل دفعة',
            subscriberName: updatedSubscriber.fullName,
            subscriberPhone: updatedSubscriber.phoneNumber,
            amount: paymentAmount,
            additionalInfo:
                'طريقة الدفع: $paymentMethod${notes.isNotEmpty ? ' - $notes' : ''}',
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } catch (e) {
      print('Error updating subscriber with payment: $e');
      throw e;
    }
  }

  /// Update subscriber data
  Future<void> updateSubscriberData(
    String subscriberId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final subscriber = await getSubscriberById(subscriberId);
      if (subscriber != null) {
        final updatedSubscriber = SubscriberModel(
          adminId: subscriber.adminId,
          id: subscriber.id,
          fullName: updates['fullName'] ?? subscriber.fullName,
          phoneNumber: updates['phoneNumber'] ?? subscriber.phoneNumber,
          packageId: updates['packageId'] ?? subscriber.packageId,
          packageName: updates['packageName'] ?? subscriber.packageName,
          address: updates['address'] ?? subscriber.address,
          paymentStatus: updates['paymentStatus'] ?? subscriber.paymentStatus,
          subscriptionStart: updates['subscriptionStart'] ?? subscriber.subscriptionStart,
          subscriptionEnd: updates['subscriptionEnd'] ?? subscriber.subscriptionEnd,
          macAddress: updates['macAddress'] ?? subscriber.macAddress,
          routerName: updates['routerName'] ?? subscriber.routerName,
          technicalNotes: updates['technicalNotes'] ?? subscriber.technicalNotes,
          debtAmount: updates['debtAmount'] ?? subscriber.debtAmount,
          createdAt: subscriber.createdAt,
          isActive: updates['isActive'] ?? subscriber.isActive,
          subscriptionType: updates['subscriptionType'] ?? subscriber.subscriptionType,
          username: updates['username'] ?? subscriber.username,
          password: updates['password'] ?? subscriber.password,
          sasServerId: updates['sasServerId'] ?? subscriber.sasServerId,
          earthlinkUserIndex: updates['earthlinkUserIndex'] ?? subscriber.earthlinkUserIndex,
        );
        await updateSubscriber(updatedSubscriber);
      }
    } catch (e) {
      print('Error updating subscriber data: $e');
      throw e;
    }
  }

  /// Authenticate subscriber (for subscriber login)
  Future<SubscriberModel?> authenticateSubscriber(
    String username,
    String password,
  ) async {
    try {
      final subscribers = await getSubscribersFire();
      for (var subscriber in subscribers) {
        if ((subscriber.username == username || subscriber.phoneNumber == username) &&
            subscriber.password == password &&
            subscriber.isActive) {
          return subscriber;
        }
      }
      return null;
    } catch (e) {
      print('Error authenticating subscriber: $e');
      return null;
    }
  }

  // Missing methods that are required by the application
  
  /// Get debtors - subscribers who have outstanding debt
  Future<List<SubscriberModel>> getDebtors() async {
    try {
      final subscribers = await getSubscribersFire();
      return subscribers.where((sub) => sub.debtAmount > 0).toList();
    } catch (e) {
      print('Error getting debtors: $e');
      return [];
    }
  }

  /// Delete an expense record
  Future<void> deleteExpense(String expenseId) async {
    try {
      if (_useSQLite) {
        await _sqliteService.deleteExpense(expenseId);
      } else {
        final expenses = await getExpenses();
        expenses.removeWhere((expense) => expense.id == expenseId);
        await saveExpenses(expenses);
      }

      // Also delete from Firestore if connected
      await _firestore.collection('expenses').doc(expenseId).delete();
    } catch (e) {
      print('Error deleting expense: $e');
      throw e;
    }
  }

  /// Update subscriber with debt information (method signature overload for different use cases)
  Future<void> updateSubscriberWithDebt(
    String subscriberId,
    double debtAmount,
    String reason,
  ) async {
    try {
      final subscriber = await getSubscriberById(subscriberId);
      if (subscriber != null) {
        final updatedSubscriber = subscriber.copyWith(
          adminId: adminId,
          debtAmount: subscriber.debtAmount + debtAmount,
          paymentStatus: PaymentStatus.pending,
        );
        await updateSubscriber(updatedSubscriber);
        
        // Add expense record
        final expense = ExpenseModel(
          adminId: adminId,
          id: generateId(),
          categoryId: 'debt_category',
          amount: debtAmount,
          notes: reason,
          timestamp: DateTime.now(),
        );
        await addExpense(expense);
      }
    } catch (e) {
      print('Error updating subscriber with debt: $e');
      throw e;
    }
  }
}