import 'package:flutter/material.dart';
import 'package:isp_manager/services/telegram_service.dart';
import 'package:fluttertoast/fluttertoast.dart' show Fluttertoast, Toast, ToastGravity;

class TelegramSettingsPage extends StatefulWidget {
  const TelegramSettingsPage({super.key});

  @override
  State<TelegramSettingsPage> createState() => _TelegramSettingsPageState();
}

class _TelegramSettingsPageState extends State<TelegramSettingsPage> {
  final _botTokenController = TextEditingController();
  final _chatIdController = TextEditingController();
  bool _isEnabled = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });
    final settings = await TelegramService().getSettings();
    _botTokenController.text = settings.botToken;
    _chatIdController.text = settings.chatId;
    _isEnabled = settings.isEnabled;
    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });
    final settings = TelegramSettings(
      botToken: _botTokenController.text.trim(),
      chatId: _chatIdController.text.trim(),
      isEnabled: _isEnabled,
    );
    await TelegramService().saveSettings(settings);
    setState(() {
      _isLoading = false;
    });
    Fluttertoast.showToast(
      msg: "تم حفظ إعدادات تلكرام بنجاح",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.green,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
    });
    final botToken = _botTokenController.text.trim();
    final chatId = _chatIdController.text.trim();

    if (botToken.isEmpty || chatId.isEmpty) {
      Fluttertoast.showToast(
        msg: "الرجاء إدخال التوكن ومعرف المحادثة أولاً",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 16.0,
      );
      setState(() {
        _isLoading = false;
      });
      return;
    }

    final result = await TelegramService().testConnection(botToken, chatId);
    setState(() {
      _isLoading = false;
    });

    if (result['success']) {
      Fluttertoast.showToast(
        msg: result['message'],
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.green,
        textColor: Colors.white,
        fontSize: 16.0,
      );
    } else {
      Fluttertoast.showToast(
        msg: result['message'],
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 16.0,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات إشعارات تلكرام'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'توكن بوت تلكرام:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _botTokenController,
                    decoration: const InputDecoration(
                      hintText: 'أدخل توكن البوت الخاص بك',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.vpn_key),
                    ),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'معرف المحادثة (Chat ID):',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _chatIdController,
                    decoration: const InputDecoration(
                      hintText: 'أدخل معرف المحادثة (مثال: -1234567890)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.chat),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'تفعيل إشعارات تلكرام:',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      Switch(
                        value: _isEnabled,
                        onChanged: (value) {
                          setState(() {
                            _isEnabled = value;
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 30),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _testConnection,
                      icon: const Icon(Icons.wifi_tethering),
                      label: const Text('اختبار الاتصال', style: TextStyle(fontSize: 18)),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        backgroundColor: Colors.blueAccent,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 15),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _saveSettings,
                      icon: const Icon(Icons.save),
                      label: const Text('حفظ الإعدادات', style: TextStyle(fontSize: 18)),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 30),
                  const Text(
                    'ملاحظات هامة:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.orange),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '1. للحصول على توكن البوت، تحدث مع @BotFather في تلكرام وأنشئ بوت جديد.',
                    style: TextStyle(fontSize: 14),
                  ),
                  const Text(
                    '2. للحصول على معرف المحادثة (Chat ID):',
                    style: TextStyle(fontSize: 14),
                  ),
                  const Padding(
                    padding: EdgeInsets.only(left: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('   أ. أضف البوت الخاص بك إلى المجموعة التي تريد إرسال الإشعارات إليها.', style: TextStyle(fontSize: 14)),
                        Text('   ب. أرسل أي رسالة إلى البوت في تلك المجموعة.', style: TextStyle(fontSize: 14)),
                        Text('   ج. اذهب إلى المتصفح وافتح الرابط التالي (استبدل YOUR_BOT_TOKEN بالتوكن الخاص بك):', style: TextStyle(fontSize: 14)),
                        SelectableText(
                          'https://api.telegram.org/botYOUR_BOT_TOKEN/getUpdates',
                          style: TextStyle(fontSize: 14, color: Colors.blue, decoration: TextDecoration.underline),
                        ),
                        Text('   د. ابحث عن "chat" ثم "id" في استجابة JSON. سيكون الرقم بجانب "id" هو معرف المحادثة.', style: TextStyle(fontSize: 14)),
                        Text('   هـ. إذا كان معرف المحادثة يبدأ بـ "100-"، فتأكد من تضمين "-100" في البداية (مثال: -1001234567890).', style: TextStyle(fontSize: 14)),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
