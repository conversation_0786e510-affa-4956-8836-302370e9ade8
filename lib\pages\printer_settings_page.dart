import 'package:flutter/material.dart';
import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';
import '../models/printer_settings_model.dart';
import '../services/printer_service.dart';
import '../services/receipt_image_service.dart'; // Import for buildReceiptPreview

class PrinterSettingsPage extends StatefulWidget {
  const PrinterSettingsPage({super.key});

  @override
  State<PrinterSettingsPage> createState() => _PrinterSettingsPageState();
}

class _PrinterSettingsPageState extends State<PrinterSettingsPage> {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  List<BluetoothDevice> _devices = [];
  BluetoothDevice? _selectedDevice;
  bool _isConnected = false;
  PrinterSettingsModel _settings = PrinterSettingsModel(
    showCompanyInfo: true,
    showOperationType: true,
    showSubscriberName: true,
    showSubscriptionNumber: true,
    showPaymentAmount: true,
    showDateTime: true,
    showEmployeeName: true,
    companyName: 'شركة المنصة للخدمات الذكية',
    connectedDeviceAddress: null,
    connectedDeviceName: null,
  );
  bool _loading = true;

  // Test data for receipt preview
  final Map<String, dynamic> _testData = {
    'companyInfo': 'شركة المنصة للخدمات الذكية',
    'operationType': 'تجديد اشتراك',
    'subscriberName': 'أحمد محمد علي',
    'subscriptionNumber': '12345678',
    'paymentAmount': '150',
    'dateTime': DateTime.now().toString().substring(0, 16),
    'employeeName': 'محمد أحمد',
  };

  bool _isLoadingPrint = false;
  String? _errorPrint;
  String? _successPrint;
  
  // إضافة StreamSubscription للتحكم في الاستماع لحالة البلوتوث
  StreamSubscription? _bluetoothStateSubscription;

  @override
  void initState() {
    super.initState();
    _initBluetooth();
    _loadSettings();
  }

  @override
  void dispose() {
    // إلغاء الاستماع لحالة البلوتوث لمنع memory leaks
    _bluetoothStateSubscription?.cancel();
    super.dispose();
  }
  Future<void> _initBluetooth() async {
    List<BluetoothDevice> devices = await bluetooth.getBondedDevices();
    if (mounted) {
      setState(() {
        _devices = devices;
      });
    }
    
    // إضافة استماع آمن لحالة البلوتوث
    _bluetoothStateSubscription = bluetooth.onStateChanged().listen((state) {
      if (mounted) {
        setState(() {
          _isConnected = state == BlueThermalPrinter.CONNECTED;
        });
      }
    });
  }
  Future<void> _loadSettings() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? json = prefs.getString('printer_settings');
    if (json != null) {
      _settings = PrinterSettingsModel.fromJson(jsonDecode(json));
      // Update _testData company name from loaded settings
      _testData['companyInfo'] = _settings.companyName ?? 'شركة المنصة للخدمات الذكية';
    }
    if (mounted) {
      setState(() { _loading = false; });
    }
  }

  Future<void> _saveSettings() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('printer_settings', jsonEncode(_settings.toJson()));
  }

  void _connectDevice(BluetoothDevice device) async {
    await bluetooth.connect(device);
    setState(() {
      _selectedDevice = device;
      _settings = _settings.copyWith(
        connectedDeviceAddress: device.address,
        connectedDeviceName: device.name,
      );
    });
    _saveSettings();
  }

  void _disconnectDevice() async {
    await bluetooth.disconnect();
    setState(() {
      _selectedDevice = null;
      _settings = _settings.copyWith(
        connectedDeviceAddress: null,
        connectedDeviceName: null,
      );
    });
    _saveSettings();
  }

  void _printAsText() async {
    if (_settings.connectedDeviceAddress == null) {
      setState(() {
        _errorPrint = 'لا توجد طابعة متصلة. يرجى الذهاب لإعدادات الطابعة أولاً.';
        _successPrint = null;
      });
      return;
    }

    setState(() {
      _isLoadingPrint = true;
      _errorPrint = null;
      _successPrint = null;
    });

    try {
      await PrinterService.printReceiptUnified(
        settings: _settings,
        data: _testData,
        operationType: _testData['operationType'],
        asImage: false, // طباعة كنص
      );

      setState(() {
        _successPrint = 'تم طباعة الإيصال كنص بنجاح! 📝';
      });
    } catch (e) {
      setState(() {
        _errorPrint = 'فشل في طباعة الإيصال كنص: ${e.toString()}';
      });
      // محاولة إيقاف طوارئ في حالة الخطأ
      try {
        await PrinterService.emergencyStop();
      } catch (stopError) {
        // تجاهل أخطاء الإيقاف
      }
    } finally {
      setState(() {
        _isLoadingPrint = false;
      });
    }
  }

  void _printAsImage() async {
    if (_settings.connectedDeviceAddress == null) {
      setState(() {
        _errorPrint = 'لا توجد طابعة متصلة. يرجى الذهاب لإعدادات الطابعة أولاً.';
        _successPrint = null;
      });
      return;
    }

    setState(() {
      _isLoadingPrint = true;
      _errorPrint = null;
      _successPrint = null;
    });

    try {
      await PrinterService.printReceiptUnified(
        settings: _settings,
        data: _testData,
        operationType: _testData['operationType'],
        asImage: true, // طباعة كصورة
      );

      setState(() {
        _successPrint = 'تم طباعة الإيصال كصورة بنجاح! 🖼️';
      });
    } catch (e) {
      setState(() {
        _errorPrint = 'فشل في طباعة الإيصال كصورة: ${e.toString()}';
      });
      // محاولة إيقاف طوارئ في حالة الخطأ
      try {
        await PrinterService.emergencyStop();
      } catch (stopError) {
        // تجاهل أخطاء الإيقاف
      }
    } finally {
      setState(() {
        _isLoadingPrint = false;
      });
    }
  }

  void _resetTestData() {
    setState(() {
      _testData['companyInfo'] = 'شركة المنصة للخدمات الذكية';
      _testData['subscriberName'] = 'أحمد محمد علي';
      _testData['paymentAmount'] = '150';
      _testData['dateTime'] = DateTime.now().toString().substring(0, 16);
      _errorPrint = null;
      _successPrint = null;
      // Also update settings company name if it was changed
      _settings = _settings.copyWith(companyName: _testData['companyInfo']);
      _saveSettings();
    });
  }
  
  Future<void> _emergencyStop() async {
    try {
      await PrinterService.emergencyStop();
      setState(() {
        _successPrint = 'تم إرسال أمر الإيقاف الطارئ للطابعة';
        _errorPrint = null;
        _isLoadingPrint = false; // إيقاف حالة التحميل
      });
    } catch (e) {
      setState(() {
        _errorPrint = 'فشل في إيقاف الطابعة: $e';
        _successPrint = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) return const Center(child: CircularProgressIndicator());
    return Scaffold(
      appBar: AppBar(title: const Text('إعدادات الطابعة')),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رسائل الحالة
                if (_errorPrint != null) ...[
                  Card(
                    color: Colors.red[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Icon(Icons.error, color: Colors.red[700]),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _errorPrint!,
                              style: TextStyle(color: Colors.red[700]),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                if (_successPrint != null) ...[
                  Card(
                    color: Colors.green[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Icon(Icons.check_circle, color: Colors.green[700]),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _successPrint!,
                              style: TextStyle(color: Colors.green[700]),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // قسم اتصال الطابعة
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'اتصال الطابعة',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: DropdownButton<BluetoothDevice>(
                                hint: const Text('اختر الطابعة'),
                                value: _selectedDevice,
                                items: _devices.map((d) => DropdownMenuItem(
                                  value: d,
                                  child: Text((d).name ?? (d).address!),
                                )).toList(),
                                onChanged: (d) {
                                  if (d != null) _connectDevice(d);
                                },
                              ),
                            ),
                            if (_isConnected)
                              IconButton(
                                icon: const Icon(Icons.link_off),
                                onPressed: _disconnectDevice,
                                tooltip: 'قطع الاتصال',
                              ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        DropdownButton<String>(
                          value: _settings.paperSize,
                          items: const [
                            DropdownMenuItem(value: '58mm', child: Text('58mm')),
                            DropdownMenuItem(value: '80mm', child: Text('80mm')),
                            DropdownMenuItem(value: 'custom', child: Text('مخصص')),
                          ],
                          onChanged: (v) {
                            setState(() { _settings = _settings.copyWith(paperSize: v); });
                            _saveSettings();
                          },
                        ),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: _settings.connectedDeviceAddress != null 
                                ? Colors.green[100] 
                                : Colors.orange[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                _settings.connectedDeviceAddress != null 
                                    ? Icons.bluetooth_connected 
                                    : Icons.bluetooth_disabled,
                                color: _settings.connectedDeviceAddress != null 
                                    ? Colors.green[700] 
                                    : Colors.orange[700],
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _settings.connectedDeviceAddress != null 
                                      ? 'الطابعة متصلة: ${_settings.connectedDeviceName ?? "غير معروف"}'
                                      : 'لا توجد طابعة متصلة - يرجى الاتصال بطابعة',
                                  style: TextStyle(
                                    color: _settings.connectedDeviceAddress != null 
                                        ? Colors.green[700] 
                                        : Colors.orange[700],
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // إعدادات محتوى الإيصال
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'إعدادات محتوى الإيصال',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        SwitchListTile(
                          title: const Text('اسم المشترك'),
                          value: _settings.showSubscriberName,
                          onChanged: (v) {
                            setState(() { _settings = _settings.copyWith(showSubscriberName: v); });
                            _saveSettings();
                          },
                        ),
                        SwitchListTile(
                          title: const Text('رقم الاشتراك'),
                          value: _settings.showSubscriptionNumber,
                          onChanged: (v) {
                            setState(() { _settings = _settings.copyWith(showSubscriptionNumber: v); });
                            _saveSettings();
                          },
                        ),
                        SwitchListTile(
                          title: const Text('التاريخ والوقت'),
                          value: _settings.showDateTime,
                          onChanged: (v) {
                            setState(() { _settings = _settings.copyWith(showDateTime: v); });
                            _saveSettings();
                          },
                        ),
                        SwitchListTile(
                          title: const Text('قيمة الدفع'),
                          value: _settings.showPaymentAmount,
                          onChanged: (v) {
                            setState(() { _settings = _settings.copyWith(showPaymentAmount: v); });
                            _saveSettings();
                          },
                        ),
                        SwitchListTile(
                          title: const Text('نوع العملية'),
                          value: _settings.showOperationType,
                          onChanged: (v) {
                            setState(() { _settings = _settings.copyWith(showOperationType: v); });
                            _saveSettings();
                          },
                        ),
                        SwitchListTile(
                          title: const Text('اسم الموظف'),
                          value: _settings.showEmployeeName,
                          onChanged: (v) {
                            setState(() { _settings = _settings.copyWith(showEmployeeName: v); });
                            _saveSettings();
                          },
                        ),
                        SwitchListTile(
                          title: const Text('اسم الشركة/العنوان'),
                          value: _settings.showCompanyInfo,
                          onChanged: (v) {
                            setState(() { _settings = _settings.copyWith(showCompanyInfo: v); });
                            _saveSettings();
                          },
                        ),
                        TextFormField(
                          initialValue: _settings.companyName,
                          decoration: const InputDecoration(
                            labelText: 'اسم الشركة',
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (value) {
                            setState(() {
                              _settings = _settings.copyWith(companyName: value);
                              _testData['companyInfo'] = value; // Update test data as well
                            });
                            _saveSettings();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // إعدادات الطباعة التلقائية
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'إعدادات الطباعة التلقائية',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        SwitchListTile(
                          title: const Text('طباعة تلقائية بعد التجديد'),
                          value: _settings.autoRenewalPrint,
                          onChanged: (v) {
                            setState(() { _settings = _settings.copyWith(autoRenewalPrint: v); });
                            _saveSettings();
                          },
                        ),
                        SwitchListTile(
                          title: const Text('طباعة تلقائية بعد تسديد الديون'),
                          value: _settings.autoPaymentPrint,
                          onChanged: (v) {
                            setState(() { _settings = _settings.copyWith(autoPaymentPrint: v); });
                            _saveSettings();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // معاينة الإيصال
                Text(
                  'معاينة الإيصال',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),

                // عرض معاينة الإيصال مع أزرار الطباعة
                Center(
                  child: SizedBox(
                    width: 300, // Constrain the preview width for consistent display
                    child: ReceiptImageService.buildReceiptPreview(
                      settings: _settings,
                      data: _testData,
                      operationType: _testData['operationType'],
                      onPrintAsText: _printAsText,
                      onPrintAsImage: _printAsImage,
                    ),
                  ),
                ),

                const SizedBox(height: 16),
                // معلومات إضافية
                Card(
                  color: Colors.blue[50],
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue[700]),
                            const SizedBox(width: 8),
                            Text(
                              'معلومات مفيدة',
                              style: TextStyle(
                                color: Colors.blue[700],
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• الطباعة كصورة توفر أفضل جودة للنصوص العربية\n'
                          '• الطباعة كنص أسرع ولكن قد تواجه مشاكل مع بعض الأحرف العربية\n'
                          '• تأكد من اتصال الطابعة قبل بدء الطباعة\n'
                          '• يمكنك تعديل البيانات أعلاه لاختبار حالات مختلفة\n'
                          '• في حالة توقف الطابعة عن السحب، استخدم زر الإيقاف الطارئ',
                          style: TextStyle(color: Colors.blue[700]),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // أزرار إضافية
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isLoadingPrint ? null : _resetTestData,
                        icon: const Icon(Icons.refresh),
                        label: const Text('إعادة ضبط البيانات التجريبية'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isLoadingPrint ? null : _emergencyStop,
                        icon: const Icon(Icons.stop),
                        label: const Text('إيقاف طوارئ للطابعة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
              ],
            ),
          ),
          
          // مؤشر التحميل
          if (_isLoadingPrint)
            Container(
              color: Colors.black54,
              child: Center(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const CircularProgressIndicator(),
                        const SizedBox(height: 16),
                        Text(
                          'جارٍ الطباعة...\nيرجى عدم إغلاق التطبيق',
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
