import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:isp_manager/models/ai_assistant_model.dart';

class CommandHistoryPage extends StatelessWidget {
  final List<AIChatMessage> messages;
  final Function(String) onCommandSelected;

  const CommandHistoryPage({
    Key? key,
    required this.messages,
    required this.onCommandSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Filter messages to only include those from the user, which represent commands
    final commandMessages = messages.where((m) => m.role == 'user').toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('سجل الأوامر'),
      ),
      body: commandMessages.isEmpty
          ? const Center(
              child: Text('لم يتم تنفيذ أي أوامر بعد.'),
            )
          : ListView.builder(
              itemCount: commandMessages.length,
              itemBuilder: (context, index) {
                final message = commandMessages[index];
                return ListTile(
                  leading: const Icon(Icons.terminal),
                  title: Text(message.content),
                  subtitle: Text('الجهاز: ${message.device?.name ?? "غير محدد"}'),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.play_arrow_outlined),
                        tooltip: 'إعادة تنفيذ الأمر',
                        onPressed: () {
                          onCommandSelected(message.content);
                          Navigator.pop(context);
                        },
                      ),
                      IconButton(
                        icon: const Icon(Icons.copy_outlined),
                        tooltip: 'نسخ الأمر',
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: message.content));
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('تم نسخ الأمر!')),
                          );
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
    );
  }
}
