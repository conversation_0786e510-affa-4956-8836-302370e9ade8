import 'package:flutter/material.dart';
import '../services/app_settings_service.dart';

class SubscriberModel {
  final String id;
  final String fullName;
  final String phoneNumber;
  final String packageId;
  final String packageName; // New field for package name
  final String address;
  final PaymentStatus paymentStatus;
  final DateTime? subscriptionStart;
  final DateTime? subscriptionEnd;
  final String macAddress;
  final String routerName;
  final String technicalNotes;
  final double debtAmount;
  final DateTime createdAt;
  final bool isActive;
  // إضافة المعلومات الجديدة
  final SubscriptionType subscriptionType;
  final String username;
  final String password;
  final String? sasServerId; // New field for SAS server ID
  final String? earthlinkUserIndex; // New field for Earthlink user index
  final String adminId;
  SubscriberModel({
    required this.id,
    required this.fullName,
    required this.phoneNumber,
    required this.packageId,
    this.packageName = 'غير محدد', // Default value
    required this.address,
    required this.paymentStatus,
    required this.subscriptionStart,
    required this.subscriptionEnd,
    this.macAddress = '',
    this.routerName = '',
    this.technicalNotes = '',
    this.debtAmount = 0.0,
    required this.createdAt,
    required this.adminId,
    this.isActive = true,
    this.subscriptionType = SubscriptionType.broadband,
    this.username = '',
    this.password = '',
    this.sasServerId, // Initialize new field
    this.earthlinkUserIndex, // Initialize new field
  });

  // Firebase compatibility methods
  Map<String, dynamic> toMap() => {
    'id': id,
    'fullName': fullName,
    'phoneNumber': phoneNumber,
    'packageId': packageId,
    'packageName': packageName, // Add to JSON
    'address': address,
    'paymentStatus': paymentStatus.toString(),
    if (subscriptionStart != null)
      'subscriptionStart': subscriptionStart!.toIso8601String(),
    if (subscriptionEnd != null)
      'subscriptionEnd': subscriptionEnd!.toIso8601String(),
    'macAddress': macAddress,
    'routerName': routerName,
    'technicalNotes': technicalNotes,
    'debtAmount': debtAmount,
    'createdAt': createdAt.toIso8601String(),
    'isActive': isActive,
    'subscriptionType': subscriptionType.toString(),
    'username': username,
    'password': password,
    'sasServerId': sasServerId,
    'earthlinkUserIndex': earthlinkUserIndex,
    "adminId": adminId,
  };

  factory SubscriberModel.fromMap(Map<String, dynamic> map) {
    return SubscriberModel(
      id: map['id'],
      fullName: map['fullName'],
      phoneNumber: map['phoneNumber'],
      packageId: map['packageId'],
      packageName: map['packageName'] ?? 'غير محدد', // Read from JSON
      address: map['address'],
      paymentStatus: PaymentStatus.values.firstWhere(
        (e) => e.toString() == map['paymentStatus'],
        orElse: () => PaymentStatus.pending,
      ),
      subscriptionStart: map['subscriptionStart'] != null
          ? DateTime.parse(map['subscriptionStart'])
          : null,
      subscriptionEnd: map['subscriptionEnd'] != null
          ? DateTime.parse(map['subscriptionEnd'])
          : null,
      macAddress: map['macAddress'] ?? '',
      routerName: map['routerName'] ?? '',
      technicalNotes: map['technicalNotes'] ?? '',
      debtAmount: map['debtAmount']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(map['createdAt']),
      isActive:
          map['isActive'] == 1 ||
          map['isActive'] == true ||
          map['isActive'] == null,
      subscriptionType: SubscriptionType.values.firstWhere(
        (e) => e.toString() == map['subscriptionType'],
        orElse: () => SubscriptionType.broadband,
      ),
      username: map['username'] ?? '',
      password: map['password'] ?? '',
      sasServerId: map['sasServerId'].toString(),
      earthlinkUserIndex: map['earthlinkUserIndex']?.toString(),
      adminId: map['adminId'] ?? "",
    );
  }

  // Legacy compatibility methods
  Map<String, dynamic> toJson() => toMap();

  factory SubscriberModel.fromJson(Map<String, dynamic> json) =>
      SubscriberModel.fromMap(json);

  SubscriberModel copyWith({
    String? id,
    String? fullName,
    String? phoneNumber,
    String? packageId,
    String? packageName, // Add to copyWith
    String? address,
    PaymentStatus? paymentStatus,
    DateTime? subscriptionStart,
    DateTime? subscriptionEnd,
    String? macAddress,
    String? routerName,
    String? technicalNotes,
    double? debtAmount,
    DateTime? createdAt,
    bool? isActive,
    SubscriptionType? subscriptionType,
    String? username,
    String? password,
    String? sasServerId,
    String? earthlinkUserIndex,
    required String adminId, // Add to copyWith
  }) => SubscriberModel(
    id: id ?? this.id,
    fullName: fullName ?? this.fullName,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    packageId: packageId ?? this.packageId,
    packageName: packageName ?? this.packageName, // Add to copyWith
    address: address ?? this.address,
    paymentStatus: paymentStatus ?? this.paymentStatus,
    subscriptionStart: subscriptionStart ?? this.subscriptionStart,
    subscriptionEnd: subscriptionEnd ?? this.subscriptionEnd,
    macAddress: macAddress ?? this.macAddress,
    routerName: routerName ?? this.routerName,
    technicalNotes: technicalNotes ?? this.technicalNotes,
    debtAmount: debtAmount ?? this.debtAmount,
    createdAt: createdAt ?? this.createdAt,
    isActive: isActive ?? this.isActive,
    subscriptionType: subscriptionType ?? this.subscriptionType,
    username: username ?? this.username,
    password: password ?? this.password,
    sasServerId: sasServerId ?? this.sasServerId,
    earthlinkUserIndex: earthlinkUserIndex ?? this.earthlinkUserIndex,
    adminId: adminId,
    // Add to copyWith
  );

  bool get isExpired =>
      subscriptionEnd != null && subscriptionEnd!.isBefore(DateTime.now());

  bool get isExpiringSoon {
    if (subscriptionEnd == null) return false;
    final now = DateTime.now();
    final tomorrow = now.add(const Duration(days: 1));
    return subscriptionEnd!.isBefore(tomorrow) && subscriptionEnd!.isAfter(now);
  }

  int? get daysRemaining {
    if (subscriptionEnd == null) {
      return null; // Or a very large number, depending on desired behavior for "never expires"
    }
    final now = DateTime.now();
    if (subscriptionEnd!.isBefore(now)) return 0;
    return subscriptionEnd!.difference(now).inDays;
  }

  String get paymentStatusDisplayText {
    switch (paymentStatus) {
      case PaymentStatus.paid:
        return 'مدفوع';
      case PaymentStatus.pending:
        return 'في انتظار الدفع';
      case PaymentStatus.overdue:
        return 'متأخر';
    }
  }

  String get subscriptionStatusText {
    if (isExpired) return 'منتهي الصلاحية';
    if (isExpiringSoon) return 'ينتهي قريباً';
    return 'نشط';
  }

  String get subscriptionTypeDisplayText {
    switch (subscriptionType) {
      case SubscriptionType.broadband:
        return 'برودباند';
      case SubscriptionType.hotspot:
        return 'هوت سبوت';
    }
  }

  /// حالة الدين - ديون مستحقة (موجب)
  bool get hasOutstandingDebt => debtAmount > 0;

  /// حالة الرصيد - دفع مقدم (سالب)
  bool get hasAdvancePayment => debtAmount < 0;

  /// حالة الرصيد - لا توجد ديون (صفر)
  bool get hasNoDebt => debtAmount == 0;

  /// مبلغ الدفع المقدم (القيمة المطلقة للرصيد السالب)
  double get advancePaymentAmount => hasAdvancePayment ? debtAmount.abs() : 0.0;

  /// نص وصفي لحالة الدين
  String get debtStatusText {
    if (hasOutstandingDebt) {
      return 'دين مستحق: ${debtAmount.toStringAsFixed(0)}'; // This should not be used anymore - use getDebtStatusText() instead
    } else if (hasAdvancePayment) {
      return 'دفع مقدم: ${advancePaymentAmount.toStringAsFixed(0)}'; // This should not be used anymore - use getDebtStatusText() instead
    } else {
      return 'لا توجد ديون';
    }
  }

  /// Get debt status text with current currency format
  Future<String> getDebtStatusText() async {
    if (hasOutstandingDebt) {
      return 'دين مستحق: ${await AppSettingsService.formatCurrency(debtAmount)}';
    } else if (hasAdvancePayment) {
      return 'دفع مقدم: ${await AppSettingsService.formatCurrency(advancePaymentAmount)}';
    } else {
      return 'لا توجد ديون';
    }
  }

  /// لون بطاقة الدين حسب الحالة
  Color get debtStatusColor {
    if (hasOutstandingDebt) {
      return const Color(0xFFE53E3E); // أحمر للديون المستحقة
    } else if (hasAdvancePayment) {
      return const Color(0xFFD69E2E); // ذهبي للدفع المقدم
    } else {
      return const Color(0xFF3182CE); // أزرق لعدم وجود ديون
    }
  }

  /// أيقونة حالة الدين
  IconData get debtStatusIcon {
    if (hasOutstandingDebt) {
      return Icons.warning; // تحذير للديون المستحقة
    } else if (hasAdvancePayment) {
      return Icons.account_balance_wallet; // محفظة للدفع المقدم
    } else {
      return Icons.check_circle; // علامة صح لعدم وجود ديون
    }
  }
}

enum PaymentStatus { paid, pending, overdue }

enum SubscriptionType { broadband, hotspot }
