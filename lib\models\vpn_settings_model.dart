// VPN Settings Model for ZeroTier configuration
// VPN Type Enum
enum VpnType { zerotier, wireguard }

class VpnSettings {
  final VpnType vpnType;
  final bool isEnabled;
  final String? networkId;
  final String? networkName;
  final String? nodeIdentity;
  final String? assignedIp;
  final bool autoConnect;
  final List<String> allowedDevices;
  final DateTime? lastConnected;
  final String connectionStatus; // 'connected', 'connecting', 'disconnected', 'error'
  final String userId;
  // WireGuard specific fields
  final String? wgConfig;
  final String? wgPrivateKey;
  final String? wgPublicKey;
  final String? wgEndpoint;
  final String? wgAllowedIps;
  final int? wgListenPort;
  final String? wgDns;

  VpnSettings({
    this.vpnType = VpnType.zerotier,
    this.isEnabled = false,
    this.networkId,
    this.networkName,
    this.nodeIdentity,
    this.assignedIp,
    this.autoConnect = false,
    List<String>? allowedDevices,
    this.lastConnected,
    required this.userId,
    this.connectionStatus = 'disconnected',
    // WireGuard fields
    this.wgConfig,
    this.wgPrivateKey,
    this.wgPublicKey,
    this.wgEndpoint,
    this.wgAllowedIps,
    this.wgListenPort,
    this.wgDns,
  }) : allowedDevices = allowedDevices ?? [];

  // Default settings
  factory VpnSettings.defaultSettings() {
    return VpnSettings(
      vpnType: VpnType.zerotier,
      userId: "",
      isEnabled: false,
      autoConnect: false,
      connectionStatus: 'disconnected',
    );
  }

  // Convert to Map for SharedPreferences
  Map<String, dynamic> toMap() {
    return {
      'vpnType': vpnType.name,
      'isEnabled': isEnabled,
      'networkId': networkId,
      'networkName': networkName,
      'nodeIdentity': nodeIdentity,
      'assignedIp': assignedIp,
      'autoConnect': autoConnect,
      'allowedDevices': allowedDevices,
      'lastConnected': lastConnected?.toIso8601String(),
      'connectionStatus': connectionStatus,
      "userId": userId,
      // WireGuard fields
      'wgConfig': wgConfig,
      'wgPrivateKey': wgPrivateKey,
      'wgPublicKey': wgPublicKey,
      'wgEndpoint': wgEndpoint,
      'wgAllowedIps': wgAllowedIps,
      'wgListenPort': wgListenPort,
      'wgDns': wgDns,
    };
  }

  // Create from Map
  factory VpnSettings.fromMap(Map<String, dynamic> map) {
    return VpnSettings(
      vpnType: VpnType.values.firstWhere(
        (e) => e.name == (map['vpnType'] ?? 'zerotier'),
        orElse: () => VpnType.zerotier,
      ),
      isEnabled: map['isEnabled'] ?? false,
      networkId: map['networkId'],
      networkName: map['networkName'],
      nodeIdentity: map['nodeIdentity'],
      assignedIp: map['assignedIp'],
      autoConnect: map['autoConnect'] ?? false,
      allowedDevices: (map['allowedDevices'] as List?)?.cast<String>() ?? [],
      lastConnected: map['lastConnected'] != null
          ? DateTime.tryParse(map['lastConnected'])
          : null,
      connectionStatus: map['connectionStatus'] ?? 'disconnected',
      userId: map["userId"] ?? "",
      // WireGuard fields
      wgConfig: map['wgConfig'],
      wgPrivateKey: map['wgPrivateKey'],
      wgPublicKey: map['wgPublicKey'],
      wgEndpoint: map['wgEndpoint'],
      wgAllowedIps: map['wgAllowedIps'],
      wgListenPort: map['wgListenPort'],
      wgDns: map['wgDns'],
    );
  }

  // Copy with method for updates
  VpnSettings copyWith({
    VpnType? vpnType,
    required String userId,
    bool? isEnabled,
    String? networkId,
    String? networkName,
    String? nodeIdentity,
    String? assignedIp,
    bool? autoConnect,
    List<String>? allowedDevices,
    DateTime? lastConnected,
    String? connectionStatus,
    // WireGuard fields
    String? wgConfig,
    String? wgPrivateKey,
    String? wgPublicKey,
    String? wgEndpoint,
    String? wgAllowedIps,
    int? wgListenPort,
    String? wgDns,
  }) {
    return VpnSettings(
      vpnType: vpnType ?? this.vpnType,
      isEnabled: isEnabled ?? this.isEnabled,
      networkId: networkId ?? this.networkId,
      networkName: networkName ?? this.networkName,
      nodeIdentity: nodeIdentity ?? this.nodeIdentity,
      assignedIp: assignedIp ?? this.assignedIp,
      autoConnect: autoConnect ?? this.autoConnect,
      allowedDevices: allowedDevices ?? this.allowedDevices,
      lastConnected: lastConnected ?? this.lastConnected,
      connectionStatus: connectionStatus ?? this.connectionStatus,
      userId: userId,
      // WireGuard fields
      wgConfig: wgConfig ?? this.wgConfig,
      wgPrivateKey: wgPrivateKey ?? this.wgPrivateKey,
      wgPublicKey: wgPublicKey ?? this.wgPublicKey,
      wgEndpoint: wgEndpoint ?? this.wgEndpoint,
      wgAllowedIps: wgAllowedIps ?? this.wgAllowedIps,
      wgListenPort: wgListenPort ?? this.wgListenPort,
      wgDns: wgDns ?? this.wgDns,
    );
  }

  // Helper methods
  bool get isConnected => connectionStatus == 'connected';
  bool get isConnecting => connectionStatus == 'connecting';
  bool get hasNetwork => networkId != null && networkId!.isNotEmpty;

  String get statusText {
    switch (connectionStatus) {
      case 'connected':
        return 'متصل';
      case 'connecting':
        return 'جاري الاتصال...';
      case 'error':
        return 'خطأ في الاتصال';
      default:
        return 'غير متصل';
    }
  }

  String get statusIcon {
    switch (connectionStatus) {
      case 'connected':
        return '🔗';
      case 'connecting':
        return '⏳';
      case 'error':
        return '❌';
      default:
        return '🔌';
    }
  }
}
