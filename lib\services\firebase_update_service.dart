import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:android_intent_plus/android_intent.dart';

class FirebaseUpdateService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<Map<String, dynamic>?> fetchLatestVersion() async {
    try {
      print('[DEBUG] fetchLatestVersion called...');
      print('تحقق من إعدادات التحديث من قاعدة البيانات...');
      
      // التحقق أولاً من وجود السجلات
      final countQuery = await _firestore
          .collection('app_versions')
          .count()
          .get();
      
      print('عدد السجلات في الجدول: ${countQuery.count}');
      
      if (countQuery.count == 0) {
        print('تحذير: الجدول فارغ - لا توجد إعدادات للتحديث');
        return null;
      }

      // جلب جميع السجلات وترتيبها
      final allRecordsQuery = await _firestore
          .collection('app_versions')
          .orderBy('created_at', descending: true)
          .get();

      final allRecords = allRecordsQuery.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();

      print('جميع السجلات: $allRecords');

      // تصفية السجلات ذات التنسيق الصحيح فقط
      List<Map<String, dynamic>> validRecords = [];
      
      for (var record in allRecords) {
        final String version = record['version'];
        if (_isValidVersionFormat(version)) {
          validRecords.add(record);
        } else {
          print('تخطي سجل بتنسيق إصدار غير صحيح: $version');
        }
      }

      if (validRecords.isEmpty) {
        print('لم يتم العثور على أي سجل بتنسيق إصدار صحيح');
        return null;
      }

      // ترتيب السجلات الصحيحة حسب الإصدار (الأحدث أولاً)
      validRecords.sort((a, b) {
        try {
          final versionA = a['version'] as String?;
          final versionB = b['version'] as String?;
          
          if (versionA == null || versionB == null) {
            return 0;
          }
          
          return _compareVersions(versionA, versionB) ? -1 : 1;
        } catch (e) {
          return 0;
        }
      });

      // إرجاع أحدث سجل
      final latestRecord = validRecords.first;
      print('أحدث سجل صحيح: $latestRecord');
      return latestRecord;
    } catch (e) {
      print('خطأ في جلب إعدادات التحديث: $e');
      
      // التحقق من نوع الخطأ وتقديم حلول مقترحة
      if (e.toString().contains('permission')) {
        print('❌ حل مقترح: مشكلة في الصلاحيات - يرجى تفعيل Firestore Security Rules');
      } else if (e.toString().contains('not-found')) {
        print('❌ حل مقترح: المجموعة غير موجودة - يرجى إنشاء مجموعة app_versions');
      }
      
      return null;
    }
  }

  Future<bool> isUpdateRequired() async {
    try {
      print('[DEBUG] isUpdateRequired called...');
      final latestVersionData = await fetchLatestVersion();
      if (latestVersionData == null) {
        print('لم يتم العثور على بيانات التحديث في قاعدة البيانات');
        return false;
      }

      final String latestVersion = latestVersionData['version'];
      final PackageInfo packageInfo = await PackageInfo.fromPlatform();
      final String currentVersion = packageInfo.version;
      print('[DEBUG] Current app version from PackageInfo: $currentVersion');
      final bool forceUpdate = latestVersionData['force_update'] ?? false;

      print('الإصدار الحالي: $currentVersion');
      print('أحدث إصدار: $latestVersion');
      print('التحديث الإجباري: $forceUpdate');

      // التحقق من صحة تنسيق الإصدار
      if (!_isValidVersionFormat(latestVersion)) {
        print('تحذير: تنسيق الإصدار في قاعدة البيانات غير صحيح: $latestVersion');
        return false;
      }

      if (!_isValidVersionFormat(currentVersion)) {
        print('تحذير: تنسيق الإصدار الحالي غير صحيح: $currentVersion');
        return false;
      }

      // إذا كان التحديث الإجباري مفعل، أظهر نافذة التحديث دائمًا
      if (forceUpdate) {
        print('التحديث الإجباري مفعل - عرض نافذة التحديث');
        return true;
      }

      // مقارنة الإصدارات
      final bool versionNeedsUpdate = _compareVersions(currentVersion, latestVersion);
      print('نتيجة مقارنة الإصدارات: $versionNeedsUpdate');

      if (versionNeedsUpdate) {
        print('الإصدار الحالي أقل من المطلوب - يتوفر تحديث جديد');
        return true;
      }

      print('لا يوجد تحديث مطلوب');
      return false;
    } catch (e) {
      print('خطأ في فحص التحديث: $e');
      return false;
    }
  }

  // دالة للتحقق من صحة تنسيق الإصدار
  bool _isValidVersionFormat(String version) {
    final RegExp versionRegex = RegExp(r'^\d+\.\d+\.\d+$');
    return versionRegex.hasMatch(version);
  }

  // دالة محسنة لمقارنة الإصدارات
  bool _compareVersions(String currentVersion, String latestVersion) {
    try {
      List<int> currentParts = currentVersion.split('.').map((s) => int.tryParse(s) ?? 0).toList();
      List<int> latestParts = latestVersion.split('.').map((s) => int.tryParse(s) ?? 0).toList();

      // التأكد من أن كلا الإصدارين لهما نفس عدد الأجزاء
      while (currentParts.length < latestParts.length) {
        currentParts.add(0);
      }
      while (latestParts.length < currentParts.length) {
        latestParts.add(0);
      }

      for (int i = 0; i < latestParts.length; i++) {
        if (latestParts[i] > currentParts[i]) {
          return true; // الإصدار الجديد أحدث
        }
        if (latestParts[i] < currentParts[i]) {
          return false; // الإصدار الحالي أحدث
        }
      }

      return false; // الإصدارات متساوية
    } catch (e) {
      print('خطأ في مقارنة الإصدارات: $e');
      return false;
    }
  }

  Future<void> showForcedUpdateDialog(BuildContext context) async {
    final latestVersionData = await fetchLatestVersion();
    final telegramLink = latestVersionData?['telegram_link'] ?? 'https://t.me/your_telegram_channel';
    
    return showDialog(
      context: context,
      barrierDismissible: false, // منع الإغلاق بالنقر خارج النافذة
      builder: (BuildContext context) {
        return PopScope(
          canPop: false, // منع الإغلاق بزر الرجوع
          child: AlertDialog(
            title: Row(
              children: [
                Icon(Icons.system_update, color: Colors.red, size: 28),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'تحديث إجباري',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.warning_amber_rounded,
                  size: 64,
                  color: Colors.orange,
                ),
                SizedBox(height: 16),
                Text(
                  'يتوفر تحديث مهم للتطبيق',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 12),
                Text(
                  'يجب تحديث التطبيق لضمان استمرار العمل بشكل صحيح وللحصول على أحدث الميزات والإصلاحات الأمنية.',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16),
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.red, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'لا يمكن تخطي هذا التحديث',
                          style: TextStyle(
                            color: Colors.red,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: <Widget>[
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: Icon(Icons.download, color: Colors.white),
                  label: Text(
                    'تحميل التحديث الآن',
                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    padding: EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: () async {
                    try {
                      final intent = AndroidIntent(
                        action: 'action_view',
                        data: telegramLink,
                        package: 'org.telegram.messenger',
                      );
                      await intent.launch();
                    } catch (e) {
                      // إذا فشل فتح التليجرام، افتح المتصفح
                      final intent = AndroidIntent(
                        action: 'action_view',
                        data: telegramLink,
                      );
                      await intent.launch();
                    }
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> showUpdateDialog(BuildContext context) async {
    final latestVersionData = await fetchLatestVersion();
    final telegramLink = latestVersionData?['telegram_link'] ?? 'https://t.me/your_telegram_channel';
    
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.system_update, color: Colors.blue, size: 28),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'تحديث متوفر',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.new_releases,
                size: 64,
                color: Colors.blue,
              ),
              SizedBox(height: 16),
              Text(
                'يتوفر تحديث جديد للتطبيق',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 12),
              Text(
                'يحتوي التحديث الجديد على تحسينات وإصلاحات مهمة. نوصي بتحديث التطبيق للحصول على أفضل تجربة.',
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'لاحقاً',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton.icon(
              icon: Icon(Icons.download, color: Colors.white),
              label: Text(
                'تحديث الآن',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () async {
                Navigator.of(context).pop();
                try {
                  final intent = AndroidIntent(
                    action: 'action_view',
                    data: telegramLink,
                    package: 'org.telegram.messenger',
                  );
                  await intent.launch();
                } catch (e) {
                  // إذا فشل فتح التليجرام، افتح المتصفح
                  final intent = AndroidIntent(
                    action: 'action_view',
                    data: telegramLink,
                  );
                  await intent.launch();
                }
              },
            ),
          ],
        );
      },
    );
  }

  // دالة لإنشاء سجل تحديث جديد (للمطورين)
  Future<void> createUpdateRecord({
    required String version,
    required String telegramLink,
    bool forceUpdate = false,
  }) async {
    try {
      await _firestore.collection('app_versions').add({
        'version': version,
        'telegram_link': telegramLink,
        'force_update': forceUpdate,
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });
      print('تم إنشاء سجل التحديث بنجاح');
    } catch (e) {
      print('خطأ في إنشاء سجل التحديث: $e');
      rethrow;
    }
  }

  // دالة لإنشاء بيانات التحديث الافتراضية تلقائياً
  Future<void> createDefaultUpdateData() async {
    try {
      print('🔧 إنشاء بيانات التحديث الافتراضية...');
      
      // التحقق من وجود سجلات
      final countQuery = await _firestore
          .collection('app_versions')
          .count()
          .get();
      
      if ((countQuery.count ?? 0) > 0) {
        print('✅ بيانات التحديث موجودة بالفعل');
        return;
      }
      
      // إنشاء سجل تحديث افتراضي
      await createUpdateRecord(
        version: '1.0.5',
        telegramLink: 'https://t.me/your_channel',
        forceUpdate: false,
      );
      
      print('✅ تم إنشاء بيانات التحديث الافتراضية بنجاح');
      
      // إنشاء سجل تحديث إجباري للاختبار (اختياري)
      await createUpdateRecord(
        version: '1.0.6',
        telegramLink: 'https://t.me/your_channel',
        forceUpdate: true,
      );
      
      print('✅ تم إنشاء سجل التحديث الإجباري للاختبار');
      
    } catch (e) {
      print('❌ خطأ في إنشاء بيانات التحديث الافتراضية: $e');
      rethrow;
    }
  }

  // دالة لحذف سجلات التحديث القديمة
  Future<void> cleanupOldUpdateRecords() async {
    try {
      final oldRecords = await _firestore
          .collection('app_versions')
          .orderBy('created_at', descending: true)
          .limit(100)
          .get();

      // احتفظ بأحدث 10 سجلات فقط
      if ((oldRecords.docs.length) > 10) {
        final toDelete = oldRecords.docs.skip(10);
        for (var doc in toDelete) {
          await doc.reference.delete();
        }
        print('تم حذف ${toDelete.length} سجل تحديث قديم');
      }
    } catch (e) {
      print('خطأ في تنظيف سجلات التحديث: $e');
    }
  }


} 