import 'package:flutter/foundation.dart';

enum PaymentRequestStatus { pending, approved, rejected }

class PaymentRequestModel {
  final String id;
  final String adminId;
  final String subscriberId;
  final String subscriberName;
  final String paymentMethodId;
  final String paymentMethodName;
  final double amount;
  final String transactionNumber;
  final PaymentRequestStatus status;
  final DateTime createdAt;

  PaymentRequestModel({
    required this.id,
    required this.adminId,
    required this.subscriberId,
    required this.subscriberName,
    required this.paymentMethodId,
    required this.paymentMethodName,
    required this.amount,
    required this.transactionNumber,
    required this.status,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() => {
        'id': id,
        'adminId': adminId,
        'subscriberId': subscriberId,
        'subscriberName': subscriberName,
        'paymentMethodId': paymentMethodId,
        'paymentMethodName': paymentMethodName,
        'amount': amount,
        'transactionNumber': transactionNumber,
        'status': describeEnum(status),
        'createdAt': createdAt.toIso8601String(),
      };

  factory PaymentRequestModel.fromMap(Map<String, dynamic> map) => PaymentRequestModel(
        id: map['id'],
        adminId: map['adminId'],
        subscriberId: map['subscriberId'],
        subscriberName: map['subscriberName'],
        paymentMethodId: map['paymentMethodId'],
        paymentMethodName: map['paymentMethodName'],
        amount: (map['amount'] is int) ? (map['amount'] as int).toDouble() : map['amount'],
        transactionNumber: map['transactionNumber'],
        status: PaymentRequestStatus.values.firstWhere(
          (e) => describeEnum(e) == map['status'],
          orElse: () => PaymentRequestStatus.pending,
        ),
        createdAt: map['createdAt'] is DateTime
            ? map['createdAt']
            : DateTime.parse(map['createdAt']),
      );
} 