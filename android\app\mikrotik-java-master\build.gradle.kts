plugins {
    id("com.android.library")
    id("kotlin-android")
}

android {
    namespace = "com.github.GideonLeGrange.mikrotik_java"
    compileSdk = 34

    defaultConfig {
        minSdk = 21
        targetSdk = 34
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    sourceSets {
        getByName("main") {
            java.srcDirs("src/main/java")
        }
    }
}

dependencies {
    // Add any dependencies required by mikrotik-java itself, if any.
    // For example, if it uses Kotlin coroutines or other AndroidX libraries.
    // For now, I'll assume it's a pure Java library with no Android-specific dependencies.
}
