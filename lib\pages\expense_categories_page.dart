import 'package:flutter/material.dart';
import '../models/expense_category_model.dart';
import '../services/database_service.dart';
import 'package:uuid/uuid.dart';

class ExpenseCategoriesPage extends StatefulWidget {
  const ExpenseCategoriesPage({super.key});

  @override
  State<ExpenseCategoriesPage> createState() => _ExpenseCategoriesPageState();
}

class _ExpenseCategoriesPageState extends State<ExpenseCategoriesPage> {
  List<ExpenseCategoryModel> _categories = [];
  bool _isLoading = true;
  final TextEditingController _categoryNameController = TextEditingController();
  final Uuid _uuid = const Uuid();

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
    });
    try {
      _categories = await DatabaseService().getExpenseCategoriesFire();
      if (_categories.isEmpty) {
        _categories = await DatabaseService().getExpenseCategories();
      }
      await DatabaseService().syncExpenseCategoryToFirebase();
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل فئات المصاريف: $e')),
        );
      });
    }
  }

  void _addCategory() async {
    final name = _categoryNameController.text.trim();
    if (name.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى إدخال اسم الفئة.')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final newCategory = ExpenseCategoryModel(
        adminId: DatabaseService().adminId,
        id: _uuid.v4(),
        name: name,
        createdAt: DateTime.now(),
      );
      await DatabaseService().addExpenseCategory(newCategory);
      _categoryNameController.clear();
      await _loadCategories();
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('تم إضافة الفئة "$name" بنجاح.')));
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('خطأ في إضافة الفئة: $e')));
    }
  }

  void _editCategory(ExpenseCategoryModel category) {
    _categoryNameController.text = category.name;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل الفئة'),
        content: TextField(
          controller: _categoryNameController,
          decoration: const InputDecoration(labelText: 'اسم الفئة'),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _categoryNameController.clear();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final newName = _categoryNameController.text.trim();
              if (newName.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('يرجى إدخال اسم الفئة.')),
                );
                return;
              }
              setState(() {
                _isLoading = true;
              });
              try {
                final updatedCategory = category.copyWith(
                  name: newName,
                  adminId: category.adminId,
                );
                await DatabaseService().updateExpenseCategory(updatedCategory);
                _categoryNameController.clear();
                await _loadCategories();
                if (mounted) Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم تعديل الفئة إلى "$newName" بنجاح.'),
                  ),
                );
              } catch (e) {
                setState(() {
                  _isLoading = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('خطأ في تعديل الفئة: $e')),
                );
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _deleteCategory(String categoryId, String categoryName) async {
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الفئة'),
        content: Text('هل أنت متأكد أنك تريد حذف الفئة "$categoryName"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      setState(() {
        _isLoading = true;
      });
      try {
        await DatabaseService().deleteExpenseCategory(categoryId);
        await _loadCategories();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم حذف الفئة "$categoryName" بنجاح.')),
        );
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حذف الفئة: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إدارة فئات المصاريف')),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _categoryNameController,
                          decoration: const InputDecoration(
                            labelText: 'اسم الفئة الجديدة',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton.icon(
                        onPressed: _addCategory,
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 15,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: _categories.isEmpty
                      ? const Center(child: Text('لا توجد فئات مصاريف حالياً.'))
                      : ListView.builder(
                          itemCount: _categories.length,
                          itemBuilder: (context, index) {
                            final category = _categories[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              child: ListTile(
                                title: Text(category.name),
                                subtitle: Text(
                                  'تاريخ الإنشاء: ${category.createdAt.toLocal().toString().split(' ')[0]}',
                                ),
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      icon: const Icon(
                                        Icons.edit,
                                        color: Colors.blue,
                                      ),
                                      onPressed: () => _editCategory(category),
                                    ),
                                    IconButton(
                                      icon: const Icon(
                                        Icons.delete,
                                        color: Colors.red,
                                      ),
                                      onPressed: () => _deleteCategory(
                                        category.id,
                                        category.name,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
    );
  }
}
