<?php

return [
    "amount_required" => "يجب عليك تحديد المبلغ.",
    "amount_numeric" => "يجب أن يكون المبلغ رقمًا.",
    'amount_min' => 'يجب أن يكون المبلغ على الأقل :min دينار عراقي.',
    "msisdn_regex" => "رقم الهاتف (Msisdn) غير صالح. يجب أن يكون مكونًا من 13 رقمًا. مثال: 9647813881805.",
    "serviceType_required" => "يجب عليك تحديد نوع الخدمة (على سبيل المثال: كتاب ، سفر ، ألعاب ، إلخ).",
    "serviceType_max" => "يجب ألا يتجاوز نوع الخدمة 254 حرفًا.",
    "orderId_required" => "يجب عليك تحديد معرف الطلب الذي يعمل كمعرف للوصف (على سبيل المثال: 1515616313).",
    "orderId_max" => "يجب ألا يتجاوز معرف الطلب 512 حرفًا.",
    "id_required" => "يجب عليك تحديد معرف العملية.",
    "id_hexadecimal" => "يجب أن يكون :attribute سلسلة هكساديسمال صالحة.",
    "phonenumber_required" => "يجب عليك تحديد رقم الهاتف.",
    "phonenumber_regex" => "رقم الهاتف غير صالح. يجب أن يكون مكونًا من 13 رقمًا. مثال: 9647813881805.",
    "pin_required" => "يجب عليك تحديد رمز التحقق.",
    "pin_max" => "رمز التحقق يجب ألا يتجاوز 254 حرفًا.",
    "opt_required" => "يجب عليك تحديد رمز التحقق الثنائي.",
    "opt_max" => "رمز التحقق الثنائي يجب ألا يتجاوز 10 أحرف.",
];
