class ExpenseCategoryModel {
  String adminId;
  final String id;
  final String name;
  final DateTime createdAt;

  ExpenseCategoryModel({
    required this.id,
    required this.name,
    required this.createdAt,
    required this.adminId,
  });

  // Firebase compatibility methods
  Map<String, dynamic> toMap() => {
    'id': id,
    'name': name,
    'createdAt': createdAt.toIso8601String(),
    "adminId": adminId,
  };

  factory ExpenseCategoryModel.fromMap(Map<String, dynamic> map) =>
      ExpenseCategoryModel(
        adminId: map['adminId'],
        id: map['id'],
        name: map['name'],
        createdAt: DateTime.parse(map['createdAt']),
      );

  // Legacy compatibility methods
  Map<String, dynamic> toJson() => toMap();

  factory ExpenseCategoryModel.fromJson(Map<String, dynamic> json) =>
      ExpenseCategoryModel.fromMap(json);

  ExpenseCategoryModel copyWith({
    String? id,
    String? name,
    DateTime? createdAt,
    required String adminId,
  }) {
    return ExpenseCategoryModel(adminId: adminId,
      id: id ?? this.id,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
