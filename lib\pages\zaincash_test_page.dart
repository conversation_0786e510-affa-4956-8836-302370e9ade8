import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/zaincash_service.dart';
import '../config/zaincash_config.dart';
import 'zaincash_otp_payment_page.dart';

class ZainCashTestPage extends StatefulWidget {
  const ZainCashTestPage({super.key});

  @override
  State<ZainCashTestPage> createState() => _ZainCashTestPageState();
}

class _ZainCashTestPageState extends State<ZainCashTestPage> {
  final ZainCashService _zainCashService = ZainCashService();
  String _testResult = '';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار ZainCash'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              color: ZainCashConfig.isProduction ? Colors.red[50] : Colors.green[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          ZainCashConfig.isProduction ? Icons.warning : Icons.info,
                          color: ZainCashConfig.isProduction ? Colors.red : Colors.green,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'معلومات البيئة الحالية',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'البيئة: ${ZainCashConfig.isProduction ? 'الإنتاج 🚀' : 'الاختبار 🧪'}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: ZainCashConfig.isProduction ? Colors.red[700] : Colors.green[700],
                      ),
                    ),
                    if (ZainCashConfig.isProduction) ...[
                      const SizedBox(height: 4),
                      Text(
                        '⚠️ تحذير: أنت في بيئة الإنتاج! المعاملات حقيقية.',
                        style: TextStyle(
                          color: Colors.red[700],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                    const SizedBox(height: 8),
                    Text('Merchant ID: ${ZainCashConfig.merchantId}'),
                    Text('MSISDN: ${ZainCashConfig.msisdn}'),
                    Text('Base URL: ${ZainCashConfig.baseUrl}'),
                    Text('الحد الأدنى: ${ZainCashConfig.minAmount} د.ع'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _testJwtToken,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('اختبار JWT Token'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _testPaymentRequest,
              child: const Text('اختبار طلب دفع (قديم)'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _testOTPPayment,
              style: ElevatedButton.styleFrom(
                backgroundColor: ZainCashConfig.isProduction ? Colors.red : Colors.green,
                foregroundColor: Colors.white,
              ),
              child: Text(
                ZainCashConfig.isProduction
                  ? 'اختبار دفع مع OTP (إنتاج - حقيقي!)'
                  : 'اختبار دفع مع OTP (اختبار)',
              ),
            ),
            const SizedBox(height: 16),
            if (_testResult.isNotEmpty) ...[
              Text(
                'نتيجة الاختبار:',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: SelectableText(
                  _testResult,
                  style: const TextStyle(fontFamily: 'monospace'),
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: _testResult));
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم نسخ النتيجة')),
                  );
                },
                icon: const Icon(Icons.copy),
                label: const Text('نسخ النتيجة'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _testJwtToken() {
    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      final token = _zainCashService.testJwtToken();
      setState(() {
        _testResult = 'JWT Token تم إنشاؤه بنجاح:\n\n$token';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _testResult = 'خطأ في إنشاء JWT Token:\n\n$e';
        _isLoading = false;
      });
    }
  }

  void _testPaymentRequest() async {
    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      final result = await _zainCashService.createPaymentRequest(
        packageId: 'test_package',
        accountNumber: 'test_account',
        amount: 1000.0, // 1000 دينار للاختبار
        packageName: 'باقة اختبار',
        durationDays: 30,
      );

      setState(() {
        _testResult = 'نتيجة طلب الدفع:\n\n${_formatResult(result)}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _testResult = 'خطأ في طلب الدفع:\n\n$e';
        _isLoading = false;
      });
    }
  }

  String _formatResult(Map<String, dynamic> result) {
    final buffer = StringBuffer();
    result.forEach((key, value) {
      buffer.writeln('$key: $value');
    });
    return buffer.toString();
  }

  void _testOTPPayment() async {
    // تحذير إضافي في بيئة الإنتاج
    if (ZainCashConfig.isProduction) {
      final confirm = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.red),
              SizedBox(width: 8),
              Text('تحذير - بيئة الإنتاج'),
            ],
          ),
          content: const Text(
            'أنت في بيئة الإنتاج!\n\n'
            '⚠️ هذا الاختبار سيستخدم معاملات حقيقية\n'
            '💰 سيتم خصم المبلغ من المحفظة الحقيقية\n'
            '📱 ستحتاج لإدخال بيانات محفظة حقيقية\n\n'
            'هل أنت متأكد من المتابعة؟',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('متابعة (حقيقي)', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      );

      if (confirm != true) return;
    }

    // فتح صفحة الدفع مع OTP المحدثة
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => ZainCashOTPPaymentPage(
          packageId: 'test_package',
          accountNumber: 'test_account_123',
          amount: ZainCashConfig.isProduction ? 1000.0 : 1000.0, // نفس المبلغ
          packageName: ZainCashConfig.isProduction ? 'باقة إنتاج حقيقية' : 'باقة اختبار OTP',
          durationDays: 30,
        ),
      ),
    );

    if (result == true) {
      setState(() {
        _testResult = ZainCashConfig.isProduction
          ? 'تم الدفع الحقيقي بنجاح! ✅\n\n'
            'البيئة: الإنتاج 🚀\n'
            'المعاملة: حقيقية ومؤكدة\n'
            'المبلغ: تم خصمه من المحفظة\n\n'
            '🎉 النظام يعمل في الإنتاج بنجاح!'
          : 'تم اختبار الدفع مع OTP بنجاح! ✅\n\n'
            'تم إكمال جميع خطوات الدفع:\n'
            '1. إنشاء المعاملة\n'
            '2. إدخال بيانات المحفظة (رقم الهاتف + PIN)\n'
            '3. إدخال رمز OTP\n'
            '4. إكمال الدفع والتفعيل\n\n'
            '🎉 النظام يدعم الآن OTP بشكل كامل!';
      });
    } else if (result == false) {
      setState(() {
        _testResult = ZainCashConfig.isProduction
          ? 'تم إلغاء المعاملة الحقيقية'
          : 'تم إلغاء اختبار الدفع مع OTP';
      });
    }
  }
}
