<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">  
  <parent> 
    <groupId>org.sonatype.oss</groupId>  
    <artifactId>oss-parent</artifactId>  
    <version>9</version> 
  </parent>  
  <modelVersion>4.0.0</modelVersion>  
  <groupId>me.legrange</groupId>  
  <artifactId>mikrotik</artifactId>  
  <version>3.0.8</version>  
  <packaging>jar</packaging>  
  <name>Mikrotik API Java Client Library</name>  
  <url>https://github.com/GideonLeGrange/mikrotik-java</url>  
  <licenses> 
    <license> 
      <name>The Apache Software License, Version 2.0</name>  
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>  
      <distribution>repo</distribution> 
    </license> 
  </licenses>  
  <scm> 
    <url>**************:GideonLeGrange/mikrotik-java.git</url>  
    <connection>scm:git:**************:GideonLeGrange/mikrotik-java.git</connection>  
    <developerConnection>scm:git:**************:GideonLeGrange/mikrotik-java.git</developerConnection> 
  </scm>  
  <build> 
    <plugins> 
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-compiler-plugin</artifactId>  
        <version>2.3.2</version>  
        <configuration> 
          <showDeprecation>true</showDeprecation>  
          <source>1.7</source>  
          <target>1.7</target> 
        </configuration> 
      </plugin>  
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-jar-plugin</artifactId>  
        <version>2.3.2</version>  
        <executions> 
          <execution> 
            <id>default-jar</id>  
            <phase>package</phase>  
            <goals> 
              <goal>jar</goal> 
            </goals>  
            <configuration> 
              <excludes> 
                <exclude>**/examples</exclude>  
                <exclude>**/examples/*</exclude> 
              </excludes> 
            </configuration> 
          </execution> 
        </executions> 
      </plugin>  
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-source-plugin</artifactId>  
        <version>2.2.1</version>  
        <executions> 
          <execution> 
            <id>attach-sources</id>  
            <goals> 
              <goal>jar</goal> 
            </goals> 
          </execution> 
        </executions> 
      </plugin>  
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-javadoc-plugin</artifactId>  
        <version>2.9.1</version>  
        <configuration> 
          <excludePackageNames>example;*.impl</excludePackageNames> 
        </configuration>  
        <executions> 
          <execution> 
            <id>attach-javadocs</id>  
            <goals> 
              <goal>jar</goal> 
            </goals> 
          </execution> 
        </executions> 
      </plugin>  
   </plugins> 
  </build>  
  <profiles> 
    <profile> 
      <id>release</id>  
      <build> 
        <plugins> 
          <plugin> 
            <groupId>org.apache.maven.plugins</groupId>  
            <artifactId>maven-source-plugin</artifactId>  
            <version>2.4</version>  
            <executions> 
              <execution> 
                <id>attach-sources</id>  
                <goals> 
                  <goal>jar</goal> 
                </goals> 
              </execution> 
            </executions> 
          </plugin>  
          <plugin> 
            <groupId>org.apache.maven.plugins</groupId>  
            <artifactId>maven-javadoc-plugin</artifactId>  
            <version>2.10.1</version>  
            <executions> 
              <execution> 
                <id>attach-javadocs</id>  
                <goals> 
                  <goal>jar</goal> 
                </goals> 
              </execution> 
            </executions> 
          </plugin>  
          <plugin> 
            <groupId>org.apache.maven.plugins</groupId>  
            <artifactId>maven-gpg-plugin</artifactId>  
            <version>1.6</version>  
            <executions> 
              <execution> 
                <id>sign-artifacts</id>  
                <phase>verify</phase>  
                <goals> 
                  <goal>sign</goal> 
                </goals> 
              </execution> 
            </executions> 
          </plugin>  
          <plugin> 
            <groupId>org.sonatype.plugins</groupId>  
            <artifactId>nexus-staging-maven-plugin</artifactId>  
            <version>1.6.5</version>  
            <extensions>true</extensions>  
            <configuration> 
              <serverId>ossrh</serverId>  
              <nexusUrl>https://oss.sonatype.org/</nexusUrl>  
              <autoReleaseAfterClose>true</autoReleaseAfterClose> 
            </configuration> 
          </plugin> 
        </plugins> 
      </build>  
      <distributionManagement> 
        <snapshotRepository> 
          <id>ossrh</id>  
          <url>https://oss.sonatype.org/content/repositories/snapshots</url> 
        </snapshotRepository> 
      </distributionManagement> 
    </profile> 
  </profiles>  
  <properties> 
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding> 
  </properties>  
  <dependencies> 
    <dependency> 
      <groupId>junit</groupId>  
      <artifactId>junit</artifactId>  
      <version>4.13.1</version>  
      <scope>test</scope> 
    </dependency>  
  </dependencies> 
</project>
