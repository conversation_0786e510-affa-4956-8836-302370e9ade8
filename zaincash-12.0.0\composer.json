{"name": "waad/zaincash", "description": "ZainCash payment Integration Gateway package API for Laravel", "type": "library", "license": "MIT", "keywords": ["<PERSON><PERSON><PERSON>", "ZainCash", "<PERSON><PERSON>", "payment Integration", "payment Gateway", "Iraqi <PERSON>", "<PERSON><PERSON>", "<PERSON>ain<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ZainCash API", "API ZainCash"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://waad.netlify.app", "role": "Developer"}], "require": {"php": "^8.2", "laravel/framework": "^12.0"}, "autoload": {"psr-4": {"Waad\\ZainCash\\": "src/"}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"laravel": {"providers": ["Waad\\ZainCash\\ZainCashServiceProvider"], "aliases": {"ZainCash": "Waad\\ZainCash\\Facades\\ZainCash"}}}}