import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:screenshot/screenshot.dart';
import 'dart:ui' as ui;
import '../models/printer_settings_model.dart';
import 'printer_service.dart';
import 'package:image/image.dart' as img; // Import image package

class ReceiptImageService {
  static final ScreenshotController _screenshotController = ScreenshotController();
  static Widget buildReceiptWidget({
    required PrinterSettingsModel settings,
    required Map<String, dynamic> data,
    required String operationType,
  }) {
    return Container(
      width: 384,
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // رأس الإيصال مع اسم الشركة
          if (settings.showCompanyInfo) ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black, width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Text(
                    data['companyInfo'] ?? settings.companyName ?? 'شركة المنصة للخدمات الذكية',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      fontFamily: 'Arial',
                    ),
                    textAlign: TextAlign.center,
                    textDirection: TextDirection.rtl,
                  ),
                  const SizedBox(height: 4),
                  Container(
                    height: 2,
                    width: 100,
                    color: Colors.black,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
          ],

          // نوع العملية في صندوق مميز
          if (settings.showOperationType) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                border: Border.all(color: Colors.black, width: 1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                '${data['operationType'] ?? operationType}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
                textDirection: TextDirection.rtl,
              ),
            ),
            const SizedBox(height: 8),
          ],

          // خط فاصل مزخرف
          Row(
            children: [
              Expanded(child: Container(height: 1, color: Colors.black)),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: Text('◆', style: TextStyle(fontSize: 12)),
              ),
              Expanded(child: Container(height: 1, color: Colors.black)),
            ],
          ),
          const SizedBox(height: 6),          // معلومات المشترك في تصميم أنيق
          if (settings.showSubscriberName && data['subscriberName'] != null) ...[
            _buildReceiptRow('المشترك', data['subscriberName'], Icons.person),
          ],

          if (settings.showSubscriptionNumber && data['subscriptionNumber'] != null) ...[
            _buildReceiptRow('رقم الاشتراك', data['subscriptionNumber'], Icons.numbers),
          ],          if (settings.showPaymentAmount && data['paymentAmount'] != null) ...[
            _buildReceiptRow('المبلغ', '${data['paymentAmount']}', Icons.attach_money),
          ],

          if (settings.showDateTime) ...[
            _buildReceiptRow('التاريخ', data['dateTime'] ?? DateTime.now().toString().substring(0, 16), Icons.calendar_today),
          ],

          if (settings.showEmployeeName && data['employeeName'] != null) ...[
            _buildReceiptRow('الموظف', data['employeeName'], Icons.badge),
          ],

          // خط فاصل مزخرف نهائي
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(child: Container(height: 1, color: Colors.black)),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: Text('✦', style: TextStyle(fontSize: 12)),
              ),
              Expanded(child: Container(height: 1, color: Colors.black)),
            ],
          ),
          const SizedBox(height: 6),

          // رسالة الشكر في صندوق مميز
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!, width: 1),
              borderRadius: BorderRadius.circular(6),
              color: Colors.grey[50],
            ),            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.favorite, size: 16, color: Colors.red),
                const SizedBox(width: 4),
                Flexible(
                  child: const Text(
                    'شكراً لاستخدامكم خدماتنا',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                    textDirection: TextDirection.rtl,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.favorite, size: 16, color: Colors.red),
              ],
            ),
          ),
          const SizedBox(height: 6),
        ],
      ),
    );
  }
  /// بناء صف في الإيصال مع أيقونة
  static Widget _buildReceiptRow(String label, String value, [IconData? icon]) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 3),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!, width: 0.5),
        borderRadius: BorderRadius.circular(4),
        color: Colors.grey[50],
      ),
      child: Row(
        textDirection: TextDirection.rtl,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              '$label: ',
              style: const TextStyle(
                fontSize: 13,
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ),
              textDirection: TextDirection.rtl,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 13,
                color: Colors.black,
              ),
              textAlign: TextAlign.left,
              textDirection: TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  /// تحويل الإيصال إلى صورة
  static Future<Uint8List> convertReceiptToImage({
    required PrinterSettingsModel settings,
    required Map<String, dynamic> data,
    required String operationType,
  }) async {
    try {
      // بناء Widget الإيصال
      Widget receiptWidget = buildReceiptWidget(
        settings: settings,
        data: data,
        operationType: operationType,
      );

      // تحويل إلى صورة باستخدام Screenshot
      // Wrap the widget in an OverflowBox to ensure it renders at its intrinsic height
      Uint8List imageBytes = await _screenshotController.captureFromWidget(
        OverflowBox(
          maxWidth: double.infinity,
          maxHeight: double.infinity,
          child: receiptWidget,
        ),
        delay: const Duration(milliseconds: 100),
        context: null, // Context is not strictly needed for off-screen capture with OverflowBox
        pixelRatio: 2.0, // جودة عالية للطباعة
      );      // Decode the image, trim whitespace, resize, and convert to grayscale
      img.Image? image = img.decodeImage(imageBytes);
      if (image != null) {
        // قص الهوامش البيضاء أولاً
        image = img.trim(image, mode: img.TrimMode.transparent);
        
        // تأكد من أن العرض لا يتجاوز 384 بكسل
        if (image.width > 384) {
          image = img.copyResize(image, width: 384);
        }
        
        // تحويل إلى أبيض وأسود لضمان التوافق مع الطابعة
        image = img.grayscale(image);
        
        return Uint8List.fromList(img.encodePng(image));
      }

      return imageBytes; // Return original if trimming fails
    } catch (e) {
      throw Exception('فشل في تحويل الإيصال إلى صورة: $e');
    }
  }

  /// طباعة الإيصال كصورة
  static Future<void> printReceiptAsImage({
    required PrinterSettingsModel settings,
    required Map<String, dynamic> data,
    required String operationType,
  }) async {
    try {
      // تحويل الإيصال إلى صورة
      Uint8List imageBytes = await convertReceiptToImage(
        settings: settings,
        data: data,
        operationType: operationType,
      );

      // طباعة الصورة
      await PrinterService.printImage(
        imageBytes,
        settings: settings,
      );
    } catch (e) {
      throw Exception('فشل في طباعة الإيصال كصورة: $e');
    }
  }

  /// طريقة بديلة باستخدام RepaintBoundary (للـ widgets الموجودة بالفعل في الشاشة)
  static Future<Uint8List> captureWidgetAsImage(GlobalKey repaintBoundaryKey) async {
    try {
      RenderRepaintBoundary boundary = repaintBoundaryKey.currentContext!
          .findRenderObject()! as RenderRepaintBoundary;
      
      ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData == null) {
        throw Exception('فشل في تحويل الصورة');
      }
      
      return byteData.buffer.asUint8List();
    } catch (e) {
      throw Exception('فشل في التقاط الصورة: $e');
    }
  }

  /// إنشاء معاينة للإيصال قبل الطباعة
  static Widget buildReceiptPreview({
    required PrinterSettingsModel settings,
    required Map<String, dynamic> data,
    required String operationType,
    required VoidCallback onPrintAsText,
    required VoidCallback onPrintAsImage,
  }) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // معاينة الإيصال
          Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: buildReceiptWidget(
              settings: settings,
              data: data,
              operationType: operationType,
            ),
          ),
            // أزرار الطباعة
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: onPrintAsText,
                    icon: const Icon(Icons.text_fields, size: 16),
                    label: const Text(
                      'طباعة كنص',
                      style: TextStyle(fontSize: 12),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: onPrintAsImage,
                    icon: const Icon(Icons.image, size: 16),
                    label: const Text(
                      'طباعة كصورة',
                      style: TextStyle(fontSize: 12),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
