import 'dart:convert';

enum ServerType { SAS, Earthlink, MikroTik }

class SasServerModel {
  String adminId;
  final String? id;
  final String name;
  final String host;
  final String username;
  final String password;
  final String? depositPassword; // For Earthlink servers only
  final bool isConnected;
  final ServerType serverType;

  SasServerModel({
    this.id,
    required this.name,
    required this.adminId,
    required this.host,
    required this.username,
    required this.password,
    this.depositPassword, // Optional field for Earthlink servers
    this.isConnected = false,
    this.serverType = ServerType.SAS, // Default to SAS for backward compatibility
  });

  SasServerModel copyWith({
    String? id,
    String? name,
    String? host,
    String? username,
    String? password,
    String? depositPassword,
    bool? isConnected,
    ServerType? serverType,
    required String adminId,
  }) {
    return SasServerModel(
      adminId: adminId,
      id: id ?? this.id,
      name: name ?? this.name,
      host: host ?? this.host,
      username: username ?? this.username,
      password: password ?? this.password,
      depositPassword: depositPassword ?? this.depositPassword,
      isConnected: isConnected ?? this.isConnected,
      serverType: serverType ?? this.serverType,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'host': host,
      'username': username,
      'password': password,
      'depositPassword': depositPassword,
      'isConnected': isConnected ? 1 : 0,
      'adminId': adminId,
      'serverType': serverType.toString(), // Store enum as string
    };
  }

  factory SasServerModel.fromMap(Map<String, dynamic> map) {
    ServerType type = ServerType.SAS;
    if (map['serverType'] == ServerType.Earthlink.toString()) {
      type = ServerType.Earthlink;
    } else if (map['serverType'] == ServerType.MikroTik.toString()) {
      type = ServerType.MikroTik;
    }
    return SasServerModel(
      adminId: map['adminId'] ?? "",
      id: map['id'].toString(),
      name: map['name'] as String,
      host: map['host'] as String,
      username: map['username'] as String,
      password: map['password'] as String,
      depositPassword: map['depositPassword'] as String?,
      isConnected: map['isConnected'] == 1,
      serverType: type,
    );
  }

  String toJson() => json.encode(toMap());

  factory SasServerModel.fromJson(String source) =>
      SasServerModel.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'SasServerModel(id: $id, name: $name, host: $host, username: $username, password: $password, depositPassword: $depositPassword, isConnected: $isConnected, serverType: $serverType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is SasServerModel &&
        other.id == id &&
        other.name == name &&
        other.host == host &&
        other.username == username &&
        other.password == password &&
        other.depositPassword == depositPassword &&
        other.isConnected == isConnected &&
        other.serverType == serverType;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        host.hashCode ^
        username.hashCode ^
        password.hashCode ^
        depositPassword.hashCode ^
        isConnected.hashCode ^
        serverType.hashCode;
  }
}
