import 'dart:async';
import 'dart:convert';

import 'package:dartssh2/dartssh2.dart';
import 'package:isp_manager/models/mikrotik_device_model.dart';

class SshService {
  SSHClient? _client;

  Future<bool> connect(MikrotikDeviceModel device) async {
    try {
      // Disconnect if already connected to another device.
      await disconnect();

      final socket = await SSHSocket.connect(
        device.host,
        device.port,
        timeout: const Duration(seconds: 10),
      );

      _client = SSHClient(
        socket,
        username: device.username,
        onPasswordRequest: () => device.password,
      );

      // The `authenticated` getter returns a Future<void> which completes when
      // the authentication is successful. Awaiting it ensures we proceed only
      // after a successful login.
      await _client!.authenticated;
      return true;
    } catch (e) {
      print('SSH Connection error: $e');
      // Ensure client is null on error to reflect disconnected state.
      _client = null;
      return false;
    }
  }

  Future<String> executeCommand(String command) async {
    // Check if the client is null or if the connection is closed.
    if (_client == null || _client?.isClosed == true) {
      return 'Error: Not connected to any device.';
    }

    try {
      // The `run` method executes a command and returns the standard output
      // as a List<int>. We explicitly type the result for clarity.
      final List<int> result = await _client!.run(command);
      return utf8.decode(result);
    } catch (e) {
      print('Command execution error: $e');
      return 'Error executing command: $e';
    }
  }

  Future<void> disconnect() async {
    final client = _client;
    if (client == null) {
      return;
    }
    // Close the client synchronously and then set to null
    client.close();
    _client = null;
  }

  Future<String> connectAndExecute(
      MikrotikDeviceModel device, String command) async {
    // If not connected, or if the connection is closed, connect first.
    if (_client == null || _client?.isClosed == true) {
      final bool connected = await connect(device);
      if (!connected) {
        return 'Failed to connect to the device. Please check the connection settings.';
      }
    }

    return await executeCommand(command);
  }

  /// Disposes the SSH client. This should be called when the service is no
  /// longer needed to ensure resources are released.
  Future<void> dispose() async {
    await disconnect();
  }
}
