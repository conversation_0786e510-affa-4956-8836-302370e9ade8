import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:isp_manager/pages/earthlink_sync_progress_page.dart';
import 'package:isp_manager/pages/mikrotik_sync_progress_page.dart';
import 'package:isp_manager/services/earthlink_service.dart';
import 'package:isp_manager/services/mikrotik_sync_service.dart';
import 'package:router_os_client/router_os_client.dart';
import '../services/sqlite_service.dart';
import '../services/database_service.dart';
import '../models/sas_server_model.dart';
import '../services/sas_api_service.dart'; // For testing connection
import 'add_edit_sas_server_page.dart';
import 'sync_progress_page.dart';

class SasServersPage extends StatefulWidget {
  const SasServersPage({super.key});

  @override
  State<SasServersPage> createState() => _SasServersPageState();
}

class _SasServersPageState extends State<SasServersPage> {
  final SQLiteService _sqliteService = SQLiteService();
  final SasApiService _sasApiService = SasApiService();
  final EarthlinkService _earthlinkService = EarthlinkService();

  List<SasServerModel> _sasServers = [];
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
    syncServersToFirebase();
  }

  Future<void> _loadData() async {
    _sasServers = await _loadSasServers();
    if (_sasServers.isEmpty) {
      _sasServers = await _loadSasServersLocal();
    }
    setState(() {});
  }

  Future<List<SasServerModel>> _loadSasServers() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('sas_servers')
          .where('adminId', isEqualTo: DatabaseService().adminId)
          .get();
      setState(() {
        _isLoading = false;
        _errorMessage = null;
      });
      return snapshot.docs.map((doc) {
        return SasServerModel.fromMap(doc.data());
      }).toList();
    } catch (e) {
      print('Error fetching servers from Firestore: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = "Error fetching servers from Firestore: $e'";
      });
      return [];
    }
  }

  Future<List<SasServerModel>> _loadSasServersLocal() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final servers = await _sqliteService.getSasServers();
      return servers.map((map) => SasServerModel.fromMap(map)).toList();
    } catch (e) {
      _errorMessage = 'Failed to load SAS servers: $e';
      return [];
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> syncServersToFirebase() async {
    final localSas = await _loadSasServersLocal(); // من الدالة اللي عندك
    final firebaseSnapshot = await FirebaseFirestore.instance
        .collection('sas_servers')
        .get();

    // IDs الموجودة بالفعل في Firestore
    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final sas in localSas) {
      if (!firebaseIds.contains(sas.id)) {
        SasServerModel newSas = sas.copyWith(
          adminId: DatabaseService().adminId,
        );
        await FirebaseFirestore.instance
            .collection('sas_servers')
            .doc(newSas.id)
            .set(newSas.toMap());
      }
    }
  }

  Future<void> _toggleConnection(SasServerModel server) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Disconnect logic is the same for all types
      if (server.isConnected) {
        final updatedServer = server.copyWith(isConnected: false, adminId: server.adminId);
        await FirebaseFirestore.instance
            .collection('sas_servers')
            .doc(updatedServer.id)
            .update(updatedServer.toMap());
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Disconnected from ${server.name}')),
        );
      } else {
        // Connect logic depends on server type
        // First, disconnect any other currently connected server
        final currentlyConnected = _sasServers.where((s) => s.isConnected).toList();
        for (var connectedServer in currentlyConnected) {
            final disconnectedServer = connectedServer.copyWith(isConnected: false, adminId: connectedServer.adminId);
            await FirebaseFirestore.instance
                .collection('sas_servers')
                .doc(disconnectedServer.id)
                .update(disconnectedServer.toMap());
        }

        // Perform connection test based on server type
        final Map<String, dynamic> loginResult;
        if (server.serverType == ServerType.SAS) {
          loginResult = await _sasApiService.loginWithDetailedResult(
            host: server.host,
            username: server.username,
            password: server.password,
          );
        } else if (server.serverType == ServerType.Earthlink) {
          loginResult = await _earthlinkService.loginWithDetailedResult(
            host: server.host, // This is ignored by the service but good practice
            username: server.username,
            password: server.password,
          );
        } else if (server.serverType == ServerType.MikroTik) {
          RouterOSClient? client;
          bool success = false;
          try {
            client = RouterOSClient(
              address: server.host,
              user: server.username,
              password: server.password,
              port: 8728, // Default MikroTik API port
              useSsl: false,
            );
            success = await client.login();
          } catch (e) {
            print('MikroTik connection test error: $e');
            success = false;
          } finally {
            client?.close();
          }
          loginResult = {
            'success': success,
            'error': success ? null : 'Connection failed. Check host and credentials.',
            'error_type': success ? null : 'unknown',
          };
        } else {
          throw Exception('Unsupported server type for connection test.');
        }

        if (loginResult['success'] == true) {
          final updatedServer = server.copyWith(isConnected: true, adminId: server.adminId);
          await FirebaseFirestore.instance
              .collection('sas_servers')
              .doc(updatedServer.id)
              .update(updatedServer.toMap());
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Connected to ${server.name}'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          final errorType = loginResult['error_type'] as String?;
          final error = loginResult['error'] as String?;
          final humanReadableError = server.serverType == ServerType.SAS
              ? _sasApiService.getHumanReadableError(errorType ?? 'unknown', error)
              : (server.serverType == ServerType.Earthlink 
                  ? _earthlinkService.getHumanReadableError(errorType ?? 'unknown', error)
                  : error); // For MikroTik, use the direct error message

          _errorMessage = 'Failed to connect to ${server.name}: $humanReadableError';

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Connection failed: ${server.name}. $humanReadableError'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      _errorMessage = 'Error toggling connection: $e';
    } finally {
      _sasServers = await _loadSasServers();
      setState(() {}); // Reload to reflect changes
    }
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
    bool disabled = false,
  }) {
    return InkWell(
      onTap: disabled ? null : onPressed,
      borderRadius: BorderRadius.circular(6),
      child: Opacity(
        opacity: disabled ? 0.5 : 1.0,
        child: Container(
          constraints: const BoxConstraints(minWidth: 48, maxWidth: 65),
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: color.withOpacity(0.2), width: 1),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  fontSize: 9,
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _showDeleteDialog(SasServerModel server) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الخادم "${server.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _deleteServer(server.id!);
    }
  }

  Future<void> _deleteServer(String id) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      await FirebaseFirestore.instance.collection('sas_servers').doc(id).delete();
      await _sqliteService.deleteSasServer(int.parse(id));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Server deleted successfully')),
      );
    } catch (e) {
      _errorMessage = 'Failed to delete server: $e';
    } finally {
      _sasServers = await _loadSasServers();
      setState(() {});
    }
  }

  Future<void> _syncData(SasServerModel server) async {
    if (!server.isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please connect to ${server.name} first.')),
      );
      return;
    }

    if (server.serverType == ServerType.SAS) {
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SyncProgressPage(
              host: server.host,
              username: server.username,
              password: server.password,
            ),
          ),
        );
      }
    } else if (server.serverType == ServerType.Earthlink) {
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EarthlinkSyncProgressPage(
              username: server.username,
              password: server.password,
            ),
          ),
        );
      }
    } else if (server.serverType == ServerType.MikroTik) {
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MikrotikSyncProgressPage(server: server),
          ),
        );
      }
    }
  }

  Future<void> _showDiagnosticDialog(SasServerModel server) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text('تشخيص الاتصال - ${server.name}'),
        content: const SizedBox(
          width: 300,
          height: 100,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري تشخيص المشكلة...'),
            ],
          ),
        ),
      ),
    );

    try {
      final diagnosis;
      if (server.serverType == ServerType.SAS) {
        diagnosis = await _sasApiService.diagnoseLoginIssues(
          host: server.host,
          username: server.username,
          password: server.password,
        );
      } else {
        diagnosis = await _earthlinkService.diagnoseLoginIssues(
          host: server.host,
          username: server.username,
          password: server.password,
        );
      }

      if (mounted) {
        Navigator.pop(context); // Close progress dialog
        _showDiagnosisResults(diagnosis, server.serverType);
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close progress dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل التشخيص: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDiagnosisResults(Map<String, dynamic> diagnosis, ServerType serverType) {
    final overallStatus = diagnosis['overall_status'] as String;
    final recommendations = List<String>.from(diagnosis['recommendations'] ?? []);

    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (overallStatus) {
      case 'success':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = 'نجح الاتصال';
        break;
      case 'authentication':
        statusColor = Colors.red;
        statusIcon = Icons.key_off;
        statusText = 'خطأ في بيانات الاعتماد';
        break;
      case 'connectivity':
        statusColor = Colors.orange;
        statusIcon = Icons.signal_wifi_off;
        statusText = 'مشكلة في الاتصال';
        break;
      case 'server_error':
        statusColor = Colors.red;
        statusIcon = Icons.dns;
        statusText = 'خطأ في الخادم';
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.error;
        statusText = 'خطأ غير معروف';
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(statusIcon, color: statusColor),
            const SizedBox(width: 8),
            Text('نتائج التشخيص'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: statusColor.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(statusIcon, color: statusColor),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        statusText,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: statusColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              if (recommendations.isNotEmpty) ...[
                const Text(
                  'التوصيات:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: recommendations
                          .map(
                            (rec) => Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                rec,
                                style: const TextStyle(fontSize: 12),
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          if (overallStatus != 'success')
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _navigateToAddEditServer(
                  server: _sasServers.firstWhere(
                    (s) => s.host == diagnosis['host'],
                  ),
                );
              },
              child: const Text('تعديل الإعدادات'),
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _navigateToAddEditServer({SasServerModel? server}) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditSasServerPage(server: server),
      ),
    );
    if (result == true) {
      _sasServers = await _loadSasServers(); // Reload if a server was added or edited
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الخوادم'),
        centerTitle: true,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'جاري تحميل الخوادم...',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              ),
            )
          : _errorMessage != null
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 60, color: Colors.red[400]),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ',
                    style: Theme.of(
                      context,
                    ).textTheme.headlineSmall?.copyWith(color: Colors.red[600]),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      _errorMessage!,
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.red[700]),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: _loadSasServers,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            )
          : _sasServers.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.dns_outlined, size: 80, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد خوادم',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'قم بإضافة خادم للبدء',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () => _navigateToAddEditServer(),
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة خادم'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _sasServers.length,
              itemBuilder: (context, index) {
                final server = _sasServers[index];
                final isEarthlink = server.serverType == ServerType.Earthlink;
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: server.isConnected
                          ? (isEarthlink ? Colors.blue.withOpacity(0.3) : Colors.green.withOpacity(0.3))
                          : Colors.grey.withOpacity(0.2),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: server.isConnected
                            ? (isEarthlink ? Colors.blue.withOpacity(0.1) : Colors.green.withOpacity(0.1))
                            : Colors.black.withOpacity(0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: server.isConnected ? (isEarthlink ? Colors.blue : Colors.green) : Colors.grey,
                                boxShadow: server.isConnected
                                    ? [
                                        BoxShadow(
                                          color: (isEarthlink ? Colors.blue : Colors.green).withOpacity(0.4),
                                          blurRadius: 4,
                                          spreadRadius: 1,
                                        ),
                                      ]
                                    : null,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    server.name,
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          fontWeight: FontWeight.w600,
                                        ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      Icon(
                                        isEarthlink ? Icons.cloud_queue : Icons.language,
                                        size: 14,
                                        color: Colors.grey[600],
                                      ),
                                      const SizedBox(width: 4),
                                      Expanded(
                                        child: Text(
                                          server.host,
                                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                                color: Colors.grey[600],
                                              ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: server.isConnected
                                    ? (isEarthlink ? Colors.blue.withOpacity(0.1) : Colors.green.withOpacity(0.1))
                                    : Colors.grey.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: server.isConnected
                                      ? (isEarthlink ? Colors.blue.withOpacity(0.3) : Colors.green.withOpacity(0.3))
                                      : Colors.grey.withOpacity(0.3),
                                ),
                              ),
                              child: Text(
                                server.isConnected ? 'متصل' : 'غير متصل',
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w600,
                                  color: server.isConnected
                                      ? (isEarthlink ? Colors.blue[700] : Colors.green[700])
                                      : Colors.grey[700],
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Container(
                          height: 1,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.transparent,
                                Colors.grey.withOpacity(0.3),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 4,
                          runSpacing: 4,
                          alignment: WrapAlignment.spaceEvenly,
                          children: [
                            _buildActionButton(
                              icon: server.isConnected ? Icons.link_off : Icons.link,
                              label: server.isConnected ? 'قطع' : 'اتصال',
                              color: server.isConnected ? Colors.red : (isEarthlink ? Colors.blue : Colors.green),
                              onPressed: () => _toggleConnection(server),
                            ),
                            _buildActionButton(
                              icon: Icons.medical_services,
                              label: 'تشخيص',
                              color: Colors.blue,
                              onPressed: () => _showDiagnosticDialog(server),
                            ),
                            _buildActionButton(
                              icon: Icons.sync,
                              label: 'مزامنة',
                              color: Colors.orange,
                              onPressed: () => _syncData(server),
                              disabled: !server.isConnected,
                            ),
                            _buildActionButton(
                              icon: Icons.edit,
                              label: 'تعديل',
                              color: Colors.blue[700]!,
                              onPressed: () => _navigateToAddEditServer(server: server),
                            ),
                            _buildActionButton(
                              icon: Icons.delete,
                              label: 'حذف',
                              color: Colors.red,
                              onPressed: () => _showDeleteDialog(server),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddEditServer(),
        child: const Icon(Icons.add),
      ),
    );
  }
}
