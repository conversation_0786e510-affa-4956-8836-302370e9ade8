import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
// import 'package:zaincash/zaincash.dart'; // هذه المكتبة تحتاج transaction_id من الخادم
import '../models/device_subscription_model.dart';
import '../config/zaincash_config.dart';
import 'firebase_app_subscription_packages_service.dart';

class ZainCashService {
  final FirebaseAppSubscriptionPackagesService _packagesService = FirebaseAppSubscriptionPackagesService();

  /// التحقق من صحة الإعدادات عند إنشاء الخدمة
  ZainCashService() {
    if (!ZainCashConfig.validateConfig()) {
      debugPrint('ZainCash: Invalid configuration detected');
    }
  }

  /// إنشاء JWT token للمصادقة حسب مواصفات ZainCash
  String _createJwtToken(Map<String, dynamic> payload) {
    try {
      // Header حسب مواصفات JWT
      final header = {
        'typ': 'JWT',
        'alg': 'HS256',
      };

      // تحويل إلى JSON وترميز base64url
      final headerJson = jsonEncode(header);
      final payloadJson = jsonEncode(payload);

      debugPrint('ZainCash: Header JSON: $headerJson');
      debugPrint('ZainCash: Payload JSON: $payloadJson');

      final encodedHeader = base64Url.encode(utf8.encode(headerJson)).replaceAll('=', '');
      final encodedPayload = base64Url.encode(utf8.encode(payloadJson)).replaceAll('=', '');

      // إنشاء التوقيع
      final message = '$encodedHeader.$encodedPayload';
      final secretBytes = utf8.encode(ZainCashConfig.secret);
      final hmac = Hmac(sha256, secretBytes);
      final digest = hmac.convert(utf8.encode(message));
      final signature = base64Url.encode(digest.bytes).replaceAll('=', '');

      final token = '$message.$signature';
      debugPrint('ZainCash: Generated token parts:');
      debugPrint('  Header: $encodedHeader');
      debugPrint('  Payload: $encodedPayload');
      debugPrint('  Signature: $signature');

      return token;
    } catch (e) {
      debugPrint('ZainCash: Error creating JWT token: $e');
      rethrow;
    }
  }

  /// فك تشفير JWT token المستلم من ZainCash
  Map<String, dynamic>? _decodeJwtToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      // فك تشفير payload
      String payload = parts[1];

      // إزالة أي padding موجود أولاً
      payload = payload.replaceAll('=', '');

      // إضافة padding صحيح حسب الحاجة
      final paddingLength = (4 - (payload.length % 4)) % 4;
      if (paddingLength > 0) {
        payload += '=' * paddingLength;
      }

      debugPrint('ZainCash: Decoding payload: $payload');

      final decodedBytes = base64Url.decode(payload);
      final decodedString = utf8.decode(decodedBytes);

      debugPrint('ZainCash: Decoded string: $decodedString');

      return jsonDecode(decodedString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('ZainCash: Error decoding JWT token: $e');
      return null;
    }
  }

  /// إنشاء طلب دفع جديد حسب مواصفات ZainCash الرسمية
  Future<Map<String, dynamic>> createPaymentRequest({
    required String packageId,
    required String accountNumber,
    required double amount,
    required String packageName,
    required int durationDays,
  }) async {
    try {
      // التحقق من الحد الأدنى للمبلغ
      if (amount < ZainCashConfig.minAmount) {
        return {
          'success': false,
          'error': ZainCashConfig.errorMessages['min_amount']!,
        };
      }

      // إنشاء معرف فريد للمعاملة
      final orderId = 'SUB_${accountNumber}_${DateTime.now().millisecondsSinceEpoch}';

      // بيانات الطلب حسب مواصفات ZainCash
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final payload = {
        'amount': amount.toInt(), // المبلغ بالدينار العراقي
        'serviceType': packageName, // اسم الخدمة
        'msisdn': ZainCashConfig.msisdn, // رقم الهاتف كنص (ليس رقم)
        'orderId': orderId,
        'redirectUrl': ZainCashConfig.redirectUrl, // استخدام GitHub Pages - يعمل الآن! 🎉
        'iat': now,
        'exp': now + (ZainCashConfig.tokenExpiryHours * 60 * 60),
      };

      debugPrint('ZainCash: Creating payment request for order: $orderId');
      debugPrint('ZainCash: Amount: ${payload['amount']} IQD');

      // إنشاء JWT token
      final token = _createJwtToken(payload);

      debugPrint('ZainCash: JWT Token: $token');
      debugPrint('ZainCash: Payload: $payload');
      debugPrint('ZainCash: Merchant ID: ${ZainCashConfig.merchantId}');

      // إرسال الطلب إلى ZainCash API حسب المواصفات
      final response = await http.post(
        Uri.parse(ZainCashConfig.initUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'token': token,
          'merchantId': ZainCashConfig.merchantId,
          'lang': ZainCashConfig.defaultLanguage,
        },
      );

      debugPrint('ZainCash: Response status: ${response.statusCode}');
      debugPrint('ZainCash: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        // التحقق من وجود خطأ في الاستجابة
        if (responseData['err'] != null) {
          final errorMsg = responseData['err']['msg'] ?? 'خطأ غير معروف';
          debugPrint('ZainCash: API Error: $errorMsg');
          return {
            'success': false,
            'error': 'خطأ من ZainCash: $errorMsg',
          };
        }

        // التحقق من وجود transaction ID
        if (responseData['id'] != null) {
          final transactionId = responseData['id'];
          final paymentUrl = '${ZainCashConfig.payUrl}?id=$transactionId';

          // حفظ بيانات المعاملة محلياً للمتابعة لاحقاً
          await _saveTransactionData(orderId, {
            'packageId': packageId,
            'accountNumber': accountNumber,
            'amount': amount,
            'packageName': packageName,
            'durationDays': durationDays,
            'status': 'pending',
            'createdAt': DateTime.now().toIso8601String(),
            'transactionId': transactionId,
            'paymentUrl': paymentUrl,
            'token': token,
          });

          return {
            'success': true,
            'orderId': orderId,
            'transactionId': transactionId,
            'paymentUrl': paymentUrl,
            'message': 'تم إنشاء طلب الدفع بنجاح',
          };
        } else {
          return {
            'success': false,
            'error': responseData['msg'] ?? responseData['error'] ?? 'فشل في إنشاء طلب الدفع',
          };
        }
      } else {
        return {
          'success': false,
          'error': 'خطأ في الاتصال بخدمة ZainCash: ${response.statusCode}',
        };
      }
    } catch (e) {
      debugPrint('ZainCash: Error creating payment request: $e');
      return {
        'success': false,
        'error': 'خطأ في إنشاء طلب الدفع: $e',
      };
    }
  }

  /// التحقق من حالة الدفع حسب مواصفات ZainCash الرسمية
  Future<Map<String, dynamic>> checkPaymentStatus(String transactionId) async {
    try {
      debugPrint('ZainCash: Checking payment status for transaction: $transactionId');

      // إنشاء JWT token للتحقق حسب المواصفات
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final payload = {
        'id': transactionId,
        'msisdn': ZainCashConfig.msisdn, // رقم الهاتف كنص
        'iat': now,
        'exp': now + (ZainCashConfig.tokenExpiryHours * 60 * 60),
      };

      final token = _createJwtToken(payload);

      // إرسال طلب التحقق حسب المواصفات
      final response = await http.post(
        Uri.parse(ZainCashConfig.getUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'token': token,
          'merchantId': ZainCashConfig.merchantId,
        },
      );

      debugPrint('ZainCash: Check status response: ${response.statusCode}');
      debugPrint('ZainCash: Check status body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        // التحقق من البيانات المستلمة
        if (responseData != null) {
          final status = responseData['status'] ?? 'unknown';
          final isPaid = status == 'success' || status == 'completed';

          debugPrint('ZainCash: Payment status: $status');

          return {
            'success': true,
            'status': status,
            'paid': isPaid,
            'data': responseData,
          };
        } else {
          return {
            'success': false,
            'error': 'لم يتم العثور على بيانات المعاملة',
          };
        }
      } else {
        return {
          'success': false,
          'error': 'خطأ في الاتصال بخدمة ZainCash: ${response.statusCode}',
        };
      }
    } catch (e) {
      debugPrint('ZainCash: Error checking payment status: $e');
      return {
        'success': false,
        'error': 'خطأ في التحقق من حالة الدفع: $e',
      };
    }
  }

  /// معالجة الـ redirect token المستلم من ZainCash
  Map<String, dynamic>? handleRedirectToken(String token) {
    try {
      final decodedData = _decodeJwtToken(token);
      if (decodedData != null) {
        debugPrint('ZainCash: Redirect token decoded: $decodedData');
        return decodedData;
      }
      return null;
    } catch (e) {
      debugPrint('ZainCash: Error handling redirect token: $e');
      return null;
    }
  }

  /// استخراج Token من URL (للاستخدام اليدوي)
  Map<String, dynamic>? extractTokenFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final token = uri.queryParameters['token'];

      if (token != null) {
        debugPrint('ZainCash: Token extracted from URL: $token');
        return handleRedirectToken(token);
      }

      debugPrint('ZainCash: No token found in URL');
      return null;
    } catch (e) {
      debugPrint('ZainCash: Error extracting token from URL: $e');
      return null;
    }
  }

  /// فحص حالة الدفع من Token المستخرج
  Future<Map<String, dynamic>> processRedirectToken(String token) async {
    try {
      final tokenData = handleRedirectToken(token);

      if (tokenData == null) {
        return {
          'success': false,
          'error': 'فشل في قراءة بيانات المعاملة',
        };
      }

      final status = tokenData['status'];
      final orderId = tokenData['orderid'];
      final transactionId = tokenData['id'];

      debugPrint('ZainCash: Processing redirect - Status: $status, Order: $orderId');

      if (status == 'success') {
        // تفعيل الاشتراك مباشرة
        final activationResult = await activateSubscription(
          orderId: orderId,
          transactionId: transactionId,
        );

        return {
          'success': true,
          'status': 'completed',
          'message': 'تم الدفع والتفعيل بنجاح',
          'activation': activationResult,
        };
      } else if (status == 'failed') {
        return {
          'success': false,
          'status': status,
          'error': 'فشل في عملية الدفع. يرجى المحاولة مرة أخرى أو التأكد من رصيد المحفظة.',
        };
      } else if (status == 'pending') {
        return {
          'success': false,
          'status': status,
          'error': 'عملية الدفع معلقة. يرجى إكمال العملية في تطبيق زين كاش.',
        };
      } else {
        return {
          'success': false,
          'status': status,
          'error': 'حالة غير معروفة: $status',
        };
      }
    } catch (e) {
      debugPrint('ZainCash: Error processing redirect token: $e');
      return {
        'success': false,
        'error': 'خطأ في معالجة نتيجة الدفع: $e',
      };
    }
  }

  /// تفعيل الاشتراك بعد نجاح الدفع
  Future<Map<String, dynamic>> activateSubscription({
    required String orderId,
    required String transactionId,
  }) async {
    try {
      // استرجاع بيانات المعاملة
      final transactionData = await _getTransactionData(orderId);
      if (transactionData == null) {
        return {
          'success': false,
          'error': 'لم يتم العثور على بيانات المعاملة',
        };
      }

      // التحقق من حالة الدفع أولاً
      final paymentStatus = await checkPaymentStatus(transactionId);
      if (!paymentStatus['success'] || !paymentStatus['paid']) {
        return {
          'success': false,
          'error': 'الدفع لم يكتمل بعد',
        };
      }

      // تفعيل الاشتراك
      final accountNumber = transactionData['accountNumber'];
      final durationDays = transactionData['durationDays'];
      
      final activationResult = await _packagesService.activateSubscription(
        accountNumber: accountNumber,
        durationDays: durationDays,
        packageName: transactionData['packageName'],
        paymentMethod: 'ZainCash',
        transactionId: transactionId,
      );

      if (activationResult['success']) {
        // تحديث حالة المعاملة
        await _updateTransactionStatus(orderId, 'completed');
        
        debugPrint('ZainCash: Subscription activated successfully for account: $accountNumber');
        
        return {
          'success': true,
          'message': 'تم تفعيل الاشتراك بنجاح',
          'subscription': activationResult['subscription'],
        };
      } else {
        return {
          'success': false,
          'error': activationResult['error'] ?? 'فشل في تفعيل الاشتراك',
        };
      }
    } catch (e) {
      debugPrint('ZainCash: Error activating subscription: $e');
      return {
        'success': false,
        'error': 'خطأ في تفعيل الاشتراك: $e',
      };
    }
  }

  /// حفظ بيانات المعاملة محلياً باستخدام SharedPreferences
  Future<void> _saveTransactionData(String orderId, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final transactionKey = 'zaincash_transaction_$orderId';
      final jsonData = jsonEncode(data);
      await prefs.setString(transactionKey, jsonData);
      debugPrint('ZainCash: Transaction data saved for order: $orderId');
    } catch (e) {
      debugPrint('ZainCash: Error saving transaction data: $e');
    }
  }

  /// استرجاع بيانات المعاملة
  Future<Map<String, dynamic>?> _getTransactionData(String orderId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final transactionKey = 'zaincash_transaction_$orderId';
      final jsonData = prefs.getString(transactionKey);

      if (jsonData != null) {
        final data = jsonDecode(jsonData) as Map<String, dynamic>;
        debugPrint('ZainCash: Transaction data retrieved for order: $orderId');
        return data;
      }

      debugPrint('ZainCash: No transaction data found for order: $orderId');
      return null;
    } catch (e) {
      debugPrint('ZainCash: Error retrieving transaction data: $e');
      return null;
    }
  }

  /// تحديث حالة المعاملة
  Future<void> _updateTransactionStatus(String orderId, String status) async {
    try {
      final transactionData = await _getTransactionData(orderId);
      if (transactionData != null) {
        transactionData['status'] = status;
        transactionData['updatedAt'] = DateTime.now().toIso8601String();
        await _saveTransactionData(orderId, transactionData);
        debugPrint('ZainCash: Transaction status updated for order: $orderId to: $status');
      }
    } catch (e) {
      debugPrint('ZainCash: Error updating transaction status: $e');
    }
  }

  /// التحقق من صحة بيانات الدفع حسب مواصفات ZainCash
  bool validatePaymentData({
    required double amount,
    required String accountNumber,
  }) {
    // التحقق من الحد الأدنى للمبلغ
    if (amount < ZainCashConfig.minAmount) {
      debugPrint('ZainCash: Amount must be at least ${ZainCashConfig.minAmount} IQD: $amount');
      return false;
    }

    if (accountNumber.isEmpty) {
      debugPrint('ZainCash: Invalid account number: $accountNumber');
      return false;
    }

    return true;
  }

  /// الحصول على معلومات البيئة الحالية
  Map<String, dynamic> getEnvironmentInfo() {
    return ZainCashConfig.getEnvironmentInfo();
  }

  /// الحصول على بيانات الاختبار (للتطوير فقط)
  static Map<String, String> getTestCredentials() {
    return {
      'merchantId': ZainCashConfig.testMerchantId,
      'secret': ZainCashConfig.testSecret,
      'msisdn': ZainCashConfig.testMsisdn,
      'testCustomerMsisdn': ZainCashConfig.testCustomerMsisdn,
      'testCustomerPin': ZainCashConfig.testCustomerPin,
      'testCustomerOtp': ZainCashConfig.testCustomerOtp,
    };
  }

  /// عرض معلومات البيئة الحالية
  static void showEnvironmentInfo() {
    ZainCashConfig.switchEnvironment();
  }

  /// اختبار إنشاء JWT token (للتشخيص)
  String testJwtToken() {
    final testPayload = {
      'amount': 1000,
      'serviceType': 'Test Service',
      'msisdn': ZainCashConfig.msisdn,
      'orderId': 'TEST_123',
      'redirectUrl': ZainCashConfig.redirectUrl,
      'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'exp': (DateTime.now().millisecondsSinceEpoch ~/ 1000) + 3600,
    };

    final token = _createJwtToken(testPayload);
    debugPrint('ZainCash: Test token created: $token');
    return token;
  }

  /// تنسيق المبلغ للعرض
  String formatAmount(double amount) {
    return '${amount.toStringAsFixed(0)} د.ع';
  }

  /// الحصول على رسوم المعاملة (إن وجدت)
  double getTransactionFee(double amount) {
    // ZainCash عادة لا يفرض رسوم إضافية، لكن يمكن إضافة منطق هنا إذا لزم الأمر
    return 0.0;
  }

  /// تنظيف المعاملات القديمة (أكثر من 30 يوم)
  Future<void> cleanupOldTransactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final transactionKeys = keys.where((key) => key.startsWith('zaincash_transaction_'));

      final cutoffDate = DateTime.now().subtract(Duration(days: 30));

      for (final key in transactionKeys) {
        final jsonData = prefs.getString(key);
        if (jsonData != null) {
          try {
            final data = jsonDecode(jsonData) as Map<String, dynamic>;
            final createdAt = DateTime.parse(data['createdAt']);

            if (createdAt.isBefore(cutoffDate)) {
              await prefs.remove(key);
              debugPrint('ZainCash: Removed old transaction: $key');
            }
          } catch (e) {
            // إذا كانت البيانات تالفة، احذفها
            await prefs.remove(key);
            debugPrint('ZainCash: Removed corrupted transaction: $key');
          }
        }
      }
    } catch (e) {
      debugPrint('ZainCash: Error cleaning up old transactions: $e');
    }
  }

  /// الحصول على جميع المعاملات المحفوظة
  Future<List<Map<String, dynamic>>> getAllTransactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final transactionKeys = keys.where((key) => key.startsWith('zaincash_transaction_'));

      final transactions = <Map<String, dynamic>>[];

      for (final key in transactionKeys) {
        final jsonData = prefs.getString(key);
        if (jsonData != null) {
          try {
            final data = jsonDecode(jsonData) as Map<String, dynamic>;
            data['orderId'] = key.replaceFirst('zaincash_transaction_', '');
            transactions.add(data);
          } catch (e) {
            debugPrint('ZainCash: Error parsing transaction data for key: $key');
          }
        }
      }

      // ترتيب المعاملات حسب تاريخ الإنشاء (الأحدث أولاً)
      transactions.sort((a, b) {
        final dateA = DateTime.parse(a['createdAt']);
        final dateB = DateTime.parse(b['createdAt']);
        return dateB.compareTo(dateA);
      });

      return transactions;
    } catch (e) {
      debugPrint('ZainCash: Error getting all transactions: $e');
      return [];
    }
  }

  // ==================== دوال OTP الجديدة ====================

  /// إنشاء طلب دفع جديد مع دعم OTP باستخدام API مباشر
  Future<Map<String, dynamic>> createPaymentRequestWithOTP({
    required String packageId,
    required String accountNumber,
    required double amount,
    required String packageName,
    required int durationDays,
  }) async {
    try {
      // التحقق من الحد الأدنى للمبلغ
      if (amount < ZainCashConfig.minAmount) {
        return {
          'success': false,
          'error': ZainCashConfig.errorMessages['min_amount']!,
        };
      }

      // إنشاء معرف فريد للمعاملة
      final orderId = 'SUB_${accountNumber}_${DateTime.now().millisecondsSinceEpoch}';

      debugPrint('ZainCash: Creating payment request with OTP support for order: $orderId');
      debugPrint('ZainCash: Amount: ${amount.toInt()} IQD');

      // بيانات الطلب حسب مواصفات ZainCash
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final payload = {
        'amount': amount.toInt(), // المبلغ بالدينار العراقي
        'serviceType': packageName, // اسم الخدمة
        'msisdn': ZainCashConfig.msisdn, // رقم الهاتف كنص (ليس رقم)
        'orderId': orderId,
        'redirectUrl': ZainCashConfig.redirectUrl,
        'iat': now,
        'exp': now + (ZainCashConfig.tokenExpiryHours * 60 * 60),
      };

      // إنشاء JWT token
      final token = _createJwtToken(payload);

      debugPrint('ZainCash: JWT Token: $token');
      debugPrint('ZainCash: Payload: $payload');
      debugPrint('ZainCash: Merchant ID: ${ZainCashConfig.merchantId}');

      // إرسال الطلب إلى ZainCash API حسب المواصفات
      final response = await http.post(
        Uri.parse(ZainCashConfig.initUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'token': token,
          'merchantId': ZainCashConfig.merchantId,
          'lang': ZainCashConfig.defaultLanguage,
        },
      );

      debugPrint('ZainCash: Response status: ${response.statusCode}');
      debugPrint('ZainCash: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        // التحقق من وجود خطأ في الاستجابة
        if (responseData['err'] != null) {
          final errorMsg = responseData['err']['msg'] ?? 'خطأ غير معروف';
          debugPrint('ZainCash: API Error: $errorMsg');
          return {
            'success': false,
            'error': 'خطأ من ZainCash: $errorMsg',
          };
        }

        // التحقق من وجود transaction ID
        if (responseData['id'] != null) {
          final transactionId = responseData['id'];

          // حفظ بيانات المعاملة محلياً للمتابعة لاحقاً
          await _saveTransactionData(orderId, {
            'packageId': packageId,
            'accountNumber': accountNumber,
            'amount': amount,
            'packageName': packageName,
            'durationDays': durationDays,
            'status': 'pending',
            'createdAt': DateTime.now().toIso8601String(),
            'transactionId': transactionId,
            'requiresOTP': true, // علامة أن هذه المعاملة تحتاج OTP
            'token': token,
          });

          return {
            'success': true,
            'orderId': orderId,
            'transactionId': transactionId,
            'message': 'تم إنشاء طلب الدفع بنجاح. يرجى إدخال بيانات الدفع.',
            'requiresOTP': true,
          };
        } else {
          return {
            'success': false,
            'error': responseData['msg'] ?? responseData['error'] ?? 'فشل في إنشاء طلب الدفع',
          };
        }
      } else {
        return {
          'success': false,
          'error': 'خطأ في الاتصال بخدمة ZainCash: ${response.statusCode}',
        };
      }
    } catch (e) {
      debugPrint('ZainCash: Error creating payment request with OTP: $e');
      return {
        'success': false,
        'error': 'خطأ في إنشاء طلب الدفع: $e',
      };
    }
  }

  /// معالجة المعاملة (الخطوة الأولى - إدخال رقم الهاتف والPIN)
  Future<Map<String, dynamic>> processTransaction({
    required String transactionId,
    required String phoneNumber,
    required String pin,
  }) async {
    try {
      debugPrint('ZainCash: Processing transaction: $transactionId');
      debugPrint('ZainCash: Phone: $phoneNumber');

      // إرسال طلب معالجة المعاملة
      final response = await http.post(
        Uri.parse('${ZainCashConfig.baseUrl}/transaction/processing'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'id': transactionId,
          'phonenumber': phoneNumber,
          'pin': pin,
        },
      );

      debugPrint('ZainCash: Processing response: ${response.statusCode}');
      debugPrint('ZainCash: Processing body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData['success'] == 1) {
          return {
            'success': true,
            'message': 'تم إرسال رمز OTP إلى هاتفك. يرجى إدخاله لإكمال العملية.',
            'requiresOTP': true,
            'data': responseData,
          };
        } else {
          return {
            'success': false,
            'error': responseData['error'] ?? 'فشل في معالجة المعاملة',
          };
        }
      } else {
        return {
          'success': false,
          'error': 'خطأ في الاتصال بخدمة ZainCash: ${response.statusCode}',
        };
      }
    } catch (e) {
      debugPrint('ZainCash: Error processing transaction: $e');
      return {
        'success': false,
        'error': 'خطأ في معالجة المعاملة: $e',
      };
    }
  }

  /// إكمال الدفع (الخطوة الثانية - إدخال OTP)
  Future<Map<String, dynamic>> completePayment({
    required String transactionId,
    required String phoneNumber,
    required String pin,
    required String otp,
  }) async {
    try {
      debugPrint('ZainCash: Completing payment for transaction: $transactionId');

      // إرسال طلب إكمال الدفع مع OTP
      final response = await http.post(
        Uri.parse('${ZainCashConfig.baseUrl}/transaction/processingOTP?type=MERCHANT_PAYMENT'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'id': transactionId,
          'phonenumber': phoneNumber,
          'pin': pin,
          'otp': otp,
        },
      );

      debugPrint('ZainCash: Payment completion response: ${response.statusCode}');
      debugPrint('ZainCash: Payment completion body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData['success'] == 1) {
          // البحث عن المعاملة المحفوظة محلياً
          final orderId = await findOrderIdByTransactionId(transactionId);
          if (orderId != null) {
            // تفعيل الاشتراك مباشرة
            final activationResult = await activateSubscription(
              orderId: orderId,
              transactionId: transactionId,
            );

            return {
              'success': true,
              'message': 'تم الدفع والتفعيل بنجاح',
              'status': 'completed',
              'activation': activationResult,
            };
          } else {
            return {
              'success': true,
              'message': 'تم الدفع بنجاح',
              'status': 'completed',
            };
          }
        } else {
          return {
            'success': false,
            'error': responseData['msg'] ?? responseData['error'] ?? 'فشل في إكمال الدفع',
          };
        }
      } else {
        return {
          'success': false,
          'error': 'خطأ في الاتصال بخدمة ZainCash: ${response.statusCode}',
        };
      }
    } catch (e) {
      debugPrint('ZainCash: Error completing payment: $e');
      return {
        'success': false,
        'error': 'خطأ في إكمال الدفع: $e',
      };
    }
  }

  /// إلغاء المعاملة
  Future<Map<String, dynamic>> cancelTransaction(String transactionId) async {
    try {
      debugPrint('ZainCash: Cancelling transaction: $transactionId');

      // إرسال طلب إلغاء المعاملة
      final response = await http.post(
        Uri.parse('${ZainCashConfig.baseUrl}/transaction/cancel'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'id': transactionId,
          'type': 'MERCHANT_PAYMENT',
        },
      );

      debugPrint('ZainCash: Cancel response: ${response.statusCode}');
      debugPrint('ZainCash: Cancel body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        // تحديث حالة المعاملة محلياً
        final orderId = await findOrderIdByTransactionId(transactionId);
        if (orderId != null) {
          await _updateTransactionStatus(orderId, 'cancelled');
        }

        return {
          'success': true,
          'message': responseData['msg'] ?? 'تم إلغاء المعاملة بنجاح',
        };
      } else {
        return {
          'success': false,
          'error': 'خطأ في الاتصال بخدمة ZainCash: ${response.statusCode}',
        };
      }
    } catch (e) {
      debugPrint('ZainCash: Error cancelling transaction: $e');
      return {
        'success': false,
        'error': 'خطأ في إلغاء المعاملة: $e',
      };
    }
  }

  /// البحث عن orderId باستخدام transactionId
  Future<String?> findOrderIdByTransactionId(String transactionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final transactionKeys = keys.where((key) => key.startsWith('zaincash_transaction_'));

      for (final key in transactionKeys) {
        final jsonData = prefs.getString(key);
        if (jsonData != null) {
          try {
            final data = jsonDecode(jsonData) as Map<String, dynamic>;
            if (data['transactionId'] == transactionId) {
              return key.replaceFirst('zaincash_transaction_', '');
            }
          } catch (e) {
            debugPrint('ZainCash: Error parsing transaction data for key: $key');
          }
        }
      }
      return null;
    } catch (e) {
      debugPrint('ZainCash: Error finding order ID: $e');
      return null;
    }
  }
}
