import 'package:flutter_test/flutter_test.dart';
import 'package:isp_manager/services/earthlink_service.dart';
import 'package:isp_manager/models/earthlink_user_model.dart';
import 'package:isp_manager/models/earthlink_account_model.dart';

void main() {
  group('EarthlinkService', () {
    late EarthlinkService earthlinkService;

    setUp(() {
      earthlinkService = EarthlinkService();
    });

    tearDown(() {
      earthlinkService.close();
    });

    test('should initialize properly', () {
      expect(earthlinkService, isNotNull);
      expect(earthlinkService.isLoggedIn, isFalse);
    });

    test('should handle date parsing correctly', () {
      // This would require mocking HTTP responses which is beyond the scope of this simple test
      // but we can at least verify the service initializes correctly
      expect(earthlinkService, isNotNull);
    });

    test('should set base URL correctly', () {
      earthlinkService.setBaseUrl('https://rapi.earthlink.iq/api/reseller');
      // The service should automatically add trailing slash
      // expect(earthlinkService.baseUrl, 'https://rapi.earthlink.iq/api/reseller/');
    });

    test('EarthlinkAccount model should parse JSON correctly', () {
      final json = {
        'accountIndex': 1,
        'accountName': 'Test Package',
        'accountImagePath': 'path/to/image.png',
        'count': 10,
        'available': 5,
        'accountCost': 25.99,
        'needed': 0,
        'description': 'Test package description',
        'speed': '10Mbps',
        'durationDays': 30,
        'isActive': true,
        'accountType': 'Residential',
        'deviceLimit': 3,
      };

      final account = EarthlinkAccount.fromJson(json);
      
      expect(account.accountIndex, 1);
      expect(account.accountName, 'Test Package');
      expect(account.accountCost, 25.99);
      expect(account.isActive, true);
      expect(account.durationDays, 30);
    });

    test('EarthlinkUser model should parse JSON correctly', () {
      final json = {
        'userIndex': 12345,
        'userId': 'testuser@hus',
        'firstName': 'Test',
        'lastName': 'User',
        'displayName': 'Test User',
        'accountName': 'Test Package',
        'accountIndex': 1,
        'accountCost': 25.99,
        'subscriptionEnd': '2024-12-31T23:59:59',
        'isActive': true,
        'status': 'Active',
        'affiliateIndex': 9876,
        'affiliateName': 'Test Affiliate',
      };

      final user = EarthlinkUser.fromJson(json);
      
      expect(user.userIndex, 12345);
      expect(user.userID, 'testuser@hus');
      expect(user.firstName, 'Test');
      expect(user.lastName, 'User');
      expect(user.fullName, 'Test User');
      expect(user.accountName, 'Test Package');
      expect(user.accountCost, 25.99);
      expect(user.isActive, true);
      expect(user.status, 'Active');
    });

    test('EarthlinkUser model should handle various date formats', () {
      // Test ISO format
      final json1 = {
        'userIndex': 1,
        'subscriptionEnd': '2024-11-29T17:21:00',
      };
      
      final user1 = EarthlinkUser.fromJson(json1);
      expect(user1.subscriptionEnd, isNotNull);
      
      // Test DD/MM/YYYY format
      final json2 = {
        'userIndex': 2,
        'subscriptionEnd': '29/11/2024 05:21 PM',
      };
      
      final user2 = EarthlinkUser.fromJson(json2);
      expect(user2.subscriptionEnd, isNotNull);
      
      // Test YYYY-MM-DD format
      final json3 = {
        'userIndex': 3,
        'subscriptionEnd': '2024-11-29',
      };
      
      final user3 = EarthlinkUser.fromJson(json3);
      expect(user3.subscriptionEnd, isNotNull);
    });

    test('EarthlinkUser model should handle null values gracefully', () {
      final json = {
        'userIndex': null,
        'userId': null,
        'firstName': null,
        'lastName': null,
        'displayName': null,
        'accountName': null,
        'accountIndex': null,
        'accountCost': null,
        'subscriptionEnd': null,
        'isActive': null,
        'status': null,
        'affiliateIndex': null,
        'affiliateName': null,
      };

      final user = EarthlinkUser.fromJson(json);
      
      expect(user.userIndex, isNull);
      expect(user.userID, isNull);
      expect(user.firstName, isNull);
      expect(user.lastName, isNull);
      expect(user.fullName, '');
      expect(user.accountName, isNull);
      expect(user.accountIndex, isNull);
      expect(user.accountCost, 0.0); // Default value when null
      expect(user.subscriptionEnd, isNull);
      expect(user.isActive, false); // Default value when null
      expect(user.status, isNull);
      expect(user.affiliateIndex, isNull);
      expect(user.affiliateName, isNull);
    });

    test('EarthlinkAccount model should handle null values gracefully', () {
      final json = {
        'accountIndex': null,
        'accountName': 'Test Package',
        'accountImagePath': null,
        'count': null,
        'available': null,
        'accountCost': null,
        'needed': null,
        'description': null,
        'speed': null,
        'durationDays': null,
        'isActive': null,
        'accountType': null,
        'deviceLimit': null,
      };

      final account = EarthlinkAccount.fromJson(json);
      
      expect(account.accountIndex, isNull);
      expect(account.accountName, 'Test Package');
      expect(account.accountCost, 0.0); // Default value for accountCost when null
      expect(account.isActive, false); // When all fields are null, isActive defaults to false
      expect(account.durationDays, 30); // Default value when null
    });
  });
}