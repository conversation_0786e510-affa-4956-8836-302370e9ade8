/// نموذج بيانات مستخدم SAS Radius 4
class SasUser {
  final String? id;
  final String username;
  final String? firstname;
  final String? lastname;
  final String? email;
  final String? phone;
  final int? profileId;
  final int? parentId;
  final String? status;
  final DateTime? createdAt;
  final DateTime? lastLogin;
  final double? balance;
  final Map<String, dynamic>? usage;
  final DateTime? expiration; // Add expiration field
  String? packageName;
  String? address;
  String? city;
  SasUser({
    this.id,
    required this.username,
    this.firstname,
    this.lastname,
    this.email,
    this.phone,
    this.profileId,
    this.parentId,
    this.status,
    this.createdAt,
    this.lastLogin,
    this.balance,
    this.usage,
    this.packageName,
    this.address,
    this.city,
    this.expiration, // Add to constructor
  });

  /// إنشاء مستخدم من JSON
  factory SasUser.fromJson(Map<String, dynamic> json) {
    return SasUser(
      id: json['id'].toString(),
      username: json['username']?.toString() ?? '',
      firstname: json['firstname']?.toString(),
      lastname: json['lastname']?.toString(),
      email: json['email']?.toString(),
      phone: json['phone']?.toString(),
      profileId: json['profile_id']?.toInt(),
      parentId: json['parent_id']?.toInt(),
      status: json['status']?.toString(),
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'].toString())
          : null,
      lastLogin: json['last_login'] != null
          ? DateTime.tryParse(json['last_login'].toString())
          : null,
      balance: json['balance'] != null
          ? double.tryParse(json['balance'].toString())
          : null,
      usage: json['usage'] as Map<String, dynamic>?,
      expiration:
          json['expiration'] !=
              null // Parse expiration
          ? DateTime.tryParse(json['expiration'].toString())
          : null,
      packageName: json['profile_name'] ?? "",
      address: json['address'] ?? "",
      city: json['city'] ?? "",
    );
  }

  /// تحويل المستخدم إلى JSON
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'username': username,
      if (firstname != null) 'firstname': firstname,
      if (lastname != null) 'lastname': lastname,
      if (email != null) 'email': email,
      if (phone != null) 'phone': phone,
      if (profileId != null) 'profile_id': profileId,
      if (parentId != null) 'parent_id': parentId,
      if (status != null) 'status': status,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (lastLogin != null) 'last_login': lastLogin!.toIso8601String(),
      if (balance != null) 'balance': balance,
      if (usage != null) 'usage': usage,
      if (expiration != null)
        'expiration': expiration!.toIso8601String(), // Add to JSON
    };
  }

  /// الحصول على الاسم الكامل
  String get fullName {
    final first = firstname ?? '';
    final last = lastname ?? '';
    if (first.isEmpty && last.isEmpty) return username;
    return '$first $last'.trim();
  }

  /// التحقق من نشاط المستخدم
  bool get isActive => status?.toLowerCase() == 'active';

  /// نسخ المستخدم مع تعديل بعض القيم
  SasUser copyWith({
    String? id,
    String? username,
    String? firstname,
    String? lastname,
    String? email,
    String? phone,
    int? profileId,
    int? parentId,
    String? status,
    DateTime? createdAt,
    DateTime? lastLogin,
    double? balance,
    Map<String, dynamic>? usage,
    DateTime? expiration, // Add to copyWith
  }) {
    return SasUser(
      id: id ?? this.id,
      username: username ?? this.username,
      firstname: firstname ?? this.firstname,
      lastname: lastname ?? this.lastname,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profileId: profileId ?? this.profileId,
      parentId: parentId ?? this.parentId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      balance: balance ?? this.balance,
      usage: usage ?? this.usage,
      expiration: expiration ?? this.expiration,

      // Add to copyWith
    );
  }
}

/// نموذج إحصائيات الاستخدام
class SasUsageStats {
  final double? uploadBytes;
  final double? downloadBytes;
  final double? totalBytes;
  final int? sessionTime;
  final DateTime? lastUpdate;

  SasUsageStats({
    this.uploadBytes,
    this.downloadBytes,
    this.totalBytes,
    this.sessionTime,
    this.lastUpdate,
  });

  factory SasUsageStats.fromJson(Map<String, dynamic> json) {
    return SasUsageStats(
      uploadBytes: json['upload_bytes'] != null
          ? double.tryParse(json['upload_bytes'].toString())
          : null,
      downloadBytes: json['download_bytes'] != null
          ? double.tryParse(json['download_bytes'].toString())
          : null,
      totalBytes: json['total_bytes'] != null
          ? double.tryParse(json['total_bytes'].toString())
          : null,
      sessionTime: json['session_time'] != null
          ? int.tryParse(json['session_time'].toString())
          : null,
      lastUpdate: json['last_update'] != null
          ? DateTime.tryParse(json['last_update'].toString())
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (uploadBytes != null) 'upload_bytes': uploadBytes,
      if (downloadBytes != null) 'download_bytes': downloadBytes,
      if (totalBytes != null) 'total_bytes': totalBytes,
      if (sessionTime != null) 'session_time': sessionTime,
      if (lastUpdate != null) 'last_update': lastUpdate!.toIso8601String(),
    };
  }

  /// تحويل البايتات إلى وحدة قابلة للقراءة
  String formatBytes(double? bytes) {
    if (bytes == null) return 'غير محدد';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    int unitIndex = 0;
    double size = bytes;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(2)} ${units[unitIndex]}';
  }

  /// تحويل الوقت إلى نص قابل للقراءة
  String formatTime(int? seconds) {
    if (seconds == null) return 'غير محدد';

    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final secs = seconds % 60;

    if (hours > 0) {
      return '$hoursس $minutesد $secsث';
    } else if (minutes > 0) {
      return '$minutesد $secsث';
    } else {
      return '$secsث';
    }
  }
}

/// نموذج بيانات الدفع
class SasPayment {
  final int? id;
  final int userId;
  final double amount;
  final String paymentMethod;
  final String? notes;
  final DateTime? createdAt;
  final String? status;

  SasPayment({
    this.id,
    required this.userId,
    required this.amount,
    required this.paymentMethod,
    this.notes,
    this.createdAt,
    this.status,
  });

  factory SasPayment.fromJson(Map<String, dynamic> json) {
    return SasPayment(
      id: json['id']?.toInt(),
      userId: json['user_id'] != null
          ? int.tryParse(json['user_id'].toString()) ?? 0
          : 0,
      amount: json['amount'] != null
          ? double.tryParse(json['amount'].toString()) ?? 0.0
          : 0.0,
      paymentMethod: json['payment_method']?.toString() ?? '',
      notes: json['notes']?.toString(),
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'].toString())
          : null,
      status: json['status']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'user_id': userId,
      'amount': amount,
      'payment_method': paymentMethod,
      if (notes != null) 'notes': notes,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (status != null) 'status': status,
    };
  }
}
