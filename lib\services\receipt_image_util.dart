import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

/// يحول نص الإيصال العربي إلى صورة (Uint8List) تدعم الطباعة على طابعات لا تدعم العربية.
Future<Uint8List> receiptTextToImage({
  required String text,
  double maxWidth = 384, // عرض شائع للطابعات الحرارية (نقطة)
  double? maxHeight, // إضافة إمكانية تحديد الارتفاع الأقصى
  double fontSize = 18, // حجم خط مناسب للطباعة
  String fontFamily = 'Arial', // خط عربي جيد للطباعة
  Color textColor = Colors.black,
  Color backgroundColor = Colors.white,
  TextAlign textAlign = TextAlign.right,
  TextDirection textDirection = TextDirection.rtl,
  bool compressImage = false, // خيار ضغط الصورة لتقليل مشاكل الطابعة
}) async {
  try {
    // التحقق من أن النص ليس فارغاً
    if (text.trim().isEmpty) {
      throw ArgumentError('Cannot create image from empty text');
    }
    
    // إعداد TextPainter
    final textSpan = TextSpan(
      text: text,
      style: TextStyle(
        fontSize: fontSize,
        fontFamily: fontFamily,
        color: textColor,
      ),
    );
    final textPainter = TextPainter(
      text: textSpan,
      textAlign: textAlign,
      textDirection: textDirection,
      maxLines: null,
    );
    textPainter.layout(maxWidth: maxWidth);

    // لا نضيف أي زيادة على الارتفاع إطلاقاً
    final calculatedHeight = textPainter.height;
    final size = Size(maxWidth, calculatedHeight);

    final pictureRecorder = ui.PictureRecorder();
    final canvas = Canvas(pictureRecorder);
    // رسم الخلفية
    final paint = Paint()..color = backgroundColor;
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);

    // بدون أي offset
    textPainter.paint(canvas, Offset(0, 0));

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.width.toInt(), size.height.toInt());
    // اختيار تنسيق الصورة حسب خيار الضغط
    final imageFormat = compressImage ? ui.ImageByteFormat.rawRgba : ui.ImageByteFormat.png;
    final byteData = await img.toByteData(format: imageFormat);
    
    // التحقق من أن البيانات تم إنشاؤها بنجاح
    if (byteData == null) {
      throw Exception('Failed to convert image to bytes');
    }
    
    return byteData.buffer.asUint8List();
  } catch (e) {
    // في حالة حدوث خطأ، إرجاع خطأ واضح
    throw Exception('Error creating receipt image: $e');
  }
}
