import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/payment_method_model.dart';
import '../models/payment_request_model.dart'; // Added import for PaymentRequestModel
import 'package:flutter/foundation.dart';

class PaymentMethodsSettingsPage extends StatefulWidget {
  const PaymentMethodsSettingsPage({Key? key}) : super(key: key);

  @override
  State<PaymentMethodsSettingsPage> createState() => _PaymentMethodsSettingsPageState();
}

class _PaymentMethodsSettingsPageState extends State<PaymentMethodsSettingsPage> with SingleTickerProviderStateMixin {
  late String adminId;
  bool _isLoading = true;
  List<PaymentMethodModel> _methods = [];
  // New for requests tab
  bool _isLoadingRequests = true;
  List<PaymentRequestModel> _requests = [];
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadAdminIdAndMethods();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAdminIdAndMethods() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;
    final userDoc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
    adminId = userDoc['adminId'];
    await _fetchMethods();
    await _fetchRequests();
  }

  Future<void> _fetchMethods() async {
    setState(() => _isLoading = true);
    final snapshot = await FirebaseFirestore.instance
        .collection('payment_methods')
        .where('adminId', isEqualTo: adminId)
        .get();
    _methods = snapshot.docs.map((doc) => PaymentMethodModel.fromMap(doc.data())).toList();
    setState(() => _isLoading = false);
  }

  Future<void> _fetchRequests() async {
    setState(() => _isLoadingRequests = true);
    final snapshot = await FirebaseFirestore.instance
        .collection('payment_requests')
        .where('adminId', isEqualTo: adminId)
        .orderBy('createdAt', descending: true)
        .get();
    _requests = snapshot.docs.map((doc) => PaymentRequestModel.fromMap(doc.data())).toList();
    setState(() => _isLoadingRequests = false);
  }

  Future<void> _addOrEditMethod({PaymentMethodModel? method}) async {
    final nameController = TextEditingController(text: method?.name ?? '');
    final accountController = TextEditingController(text: method?.accountInfo ?? '');
    PaymentDisplayType displayType = method?.displayType ?? PaymentDisplayType.number;
    final formKey = GlobalKey<FormState>();
    final isEdit = method != null;
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEdit ? 'تعديل وسيلة دفع' : 'إضافة وسيلة دفع'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: nameController,
                decoration: const InputDecoration(labelText: 'اسم الوسيلة'),
                validator: (v) => v == null || v.trim().isEmpty ? 'مطلوب' : null,
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: accountController,
                decoration: const InputDecoration(labelText: 'رقم الحساب أو بيانات QR'),
                validator: (v) => v == null || v.trim().isEmpty ? 'مطلوب' : null,
              ),
              const SizedBox(height: 12),
              DropdownButtonFormField<PaymentDisplayType>(
                value: displayType,
                items: PaymentDisplayType.values.map((e) => DropdownMenuItem(
                  value: e,
                  child: Text(e == PaymentDisplayType.number ? 'رقم عادي' : 'QR'),
                )).toList(),
                onChanged: (v) => displayType = v!,
                decoration: const InputDecoration(labelText: 'طريقة العرض'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (!formKey.currentState!.validate()) return;
              final data = PaymentMethodModel(
                id: isEdit ? method.id : DateTime.now().millisecondsSinceEpoch.toString(),
                adminId: adminId,
                name: nameController.text.trim(),
                accountInfo: accountController.text.trim(),
                displayType: displayType,
              );
              await FirebaseFirestore.instance
                  .collection('payment_methods')
                  .doc(data.id)
                  .set(data.toMap());
              Navigator.of(context).pop();
              await _fetchMethods();
            },
            child: Text(isEdit ? 'حفظ' : 'إضافة'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteMethod(PaymentMethodModel method) async {
    await FirebaseFirestore.instance.collection('payment_methods').doc(method.id).delete();
    await _fetchMethods();
  }

  Future<void> _updateRequestStatus(PaymentRequestModel request, PaymentRequestStatus newStatus) async {
    await FirebaseFirestore.instance
        .collection('payment_requests')
        .doc(request.id)
        .update({'status': describeEnum(newStatus)});
    await _fetchRequests();
  }

  void _showRequestDetails(PaymentRequestModel r) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل طلب الدفع'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('اسم المشترك: ${r.subscriberName}'),
            Text('وسيلة الدفع: ${r.paymentMethodName}'),
            Text('المبلغ: ${r.amount}'),
            Text('رقم العملية: ${r.transactionNumber}'),
            Text('الحالة: ${_statusText(r.status)}'),
            Text('تاريخ الطلب: ${_formatDateTime(r.createdAt)}'),
            Text('معرّف الطلب: ${r.id}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات وسائل الدفع'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'وسائل الدفع', icon: Icon(Icons.account_balance_wallet)),
            Tab(text: 'طلبات الدفع', icon: Icon(Icons.receipt_long)),
          ],
        ),
        actions: [
          if (_tabController.index == 0)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _addOrEditMethod(),
              tooltip: 'إضافة وسيلة دفع',
            ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Tab 1: Payment Methods
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _methods.isEmpty
                  ? const Center(child: Text('لا توجد وسائل دفع مضافة بعد.'))
                  : ListView.separated(
                      padding: const EdgeInsets.all(16),
                      itemCount: _methods.length,
                      separatorBuilder: (_, __) => const Divider(),
                      itemBuilder: (context, i) {
                        final m = _methods[i];
                        return ListTile(
                          leading: Icon(m.displayType == PaymentDisplayType.number ? Icons.numbers : Icons.qr_code),
                          title: Text(m.name),
                          subtitle: Text(m.accountInfo),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(Icons.edit),
                                onPressed: () => _addOrEditMethod(method: m),
                                tooltip: 'تعديل',
                              ),
                              IconButton(
                                icon: const Icon(Icons.delete),
                                onPressed: () => _deleteMethod(m),
                                tooltip: 'حذف',
                              ),
                            ],
                          ),
                        );
                      },
                    ),
          // Tab 2: Payment Requests
          _isLoadingRequests
              ? const Center(child: CircularProgressIndicator())
              : _requests.isEmpty
                  ? const Center(child: Text('لا توجد طلبات دفع بعد.'))
                  : ListView.separated(
                      padding: const EdgeInsets.all(16),
                      itemCount: _requests.length,
                      separatorBuilder: (_, __) => const Divider(),
                      itemBuilder: (context, i) {
                        final r = _requests[i];
                        Color statusColor;
                        IconData statusIcon;
                        switch (r.status) {
                          case PaymentRequestStatus.pending:
                            statusColor = Colors.orange;
                            statusIcon = Icons.hourglass_top;
                            break;
                          case PaymentRequestStatus.approved:
                            statusColor = Colors.green;
                            statusIcon = Icons.check_circle;
                            break;
                          case PaymentRequestStatus.rejected:
                            statusColor = Colors.red;
                            statusIcon = Icons.cancel;
                            break;
                        }
                        return Card(
                          elevation: 1,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                          child: ListTile(
                            leading: Icon(statusIcon, color: statusColor, size: 32),
                            title: Text('${r.subscriberName} - ${r.paymentMethodName}'),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('المبلغ: ${r.amount}'),
                                Text('رقم العملية: ${r.transactionNumber}'),
                                Text('الحالة: ${_statusText(r.status)}', style: TextStyle(color: statusColor, fontWeight: FontWeight.bold)),
                                Text('تاريخ الطلب: ${_formatDateTime(r.createdAt)}'),
                              ],
                            ),
                            trailing: r.status == PaymentRequestStatus.pending
                                ? Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(Icons.check_circle, color: Colors.green),
                                        tooltip: 'موافقة',
                                        onPressed: () => _updateRequestStatus(r, PaymentRequestStatus.approved),
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.cancel, color: Colors.red),
                                        tooltip: 'رفض',
                                        onPressed: () => _updateRequestStatus(r, PaymentRequestStatus.rejected),
                                      ),
                                    ],
                                  )
                                : null,
                            onLongPress: () => _showRequestDetails(r),
                          ),
                        );
                      },
                    ),
        ],
      ),
    );
  }

  String _statusText(PaymentRequestStatus status) {
    switch (status) {
      case PaymentRequestStatus.pending:
        return 'بانتظار الموافقة';
      case PaymentRequestStatus.approved:
        return 'تمت الموافقة';
      case PaymentRequestStatus.rejected:
        return 'مرفوض';
    }
  }

  String _formatDateTime(DateTime dt) {
    return '${dt.year}/${dt.month.toString().padLeft(2, '0')}/${dt.day.toString().padLeft(2, '0')} - ${dt.hour.toString().padLeft(2, '0')}:${dt.minute.toString().padLeft(2, '0')}';
  }
} 