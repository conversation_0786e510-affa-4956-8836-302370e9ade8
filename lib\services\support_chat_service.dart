import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/support_chat_model.dart';
import '../models/support_message_model.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class SupportChatService {
  static final firebaseFirestore = FirebaseFirestore.instance;
  final _firestore = firebaseFirestore;
  final String chatsCollection = 'support_chats';

  // إنشاء أو جلب محادثة بين مشترك ومدير
  Future<SupportChatModel> createOrGetChat({
    required String subscriberId,
    required String adminId,
  }) async {
    // ابحث عن محادثة مفتوحة بين المشترك والمدير
    final openChats = await _firestore
        .collection(chatsCollection)
        .where('subscriberId', isEqualTo: subscriberId)
        .where('adminId', isEqualTo: adminId)
        .where('isOpen', isEqualTo: true)
        .limit(1)
        .get();
    if (openChats.docs.isNotEmpty) {
      return SupportChatModel.fromMap(openChats.docs.first.data());
    } else {
      // أنشئ محادثة جديدة بهوية جديدة (id فريد)
      final newDoc = _firestore.collection(chatsCollection).doc();
      final chat = SupportChatModel(
        id: newDoc.id,
        subscriberId: subscriberId,
        adminId: adminId,
        createdAt: DateTime.now(),
        lastMessage: '',
        lastSender: '',
        isOpen: true,
      );
      await newDoc.set(chat.toMap());
      return chat;
    }
  }

  // إرسال رسالة دعم
  Future<void> sendMessage({
    required String chatId,
    required SupportMessageModel message,
    required String lastSender,
  }) async {
    final msgRef = _firestore.collection(chatsCollection).doc(chatId).collection('messages').doc(message.id);
    await msgRef.set(message.toMap());
    // تحديث بيانات المحادثة
    await _firestore.collection(chatsCollection).doc(chatId).update({
      'lastMessage': message.text,
      'lastSender': lastSender,
      'isOpen': true,
    });
  }

  // جلب رسائل المحادثة (Stream)
  Stream<List<SupportMessageModel>> getMessagesStream(String chatId) {
    return _firestore
        .collection(chatsCollection)
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => SupportMessageModel.fromMap(doc.data()))
            .toList());
  }

  // تحديث حالة القراءة
  Future<void> markMessagesAsRead(String chatId, String userId) async {
    final msgs = await _firestore
        .collection(chatsCollection)
        .doc(chatId)
        .collection('messages')
        .where('senderId', isNotEqualTo: userId)
        .where('isRead', isEqualTo: false)
        .get();
    for (final doc in msgs.docs) {
      await doc.reference.update({'isRead': true});
    }
  }

  // جلب كل المحادثات لمسؤول
  Stream<List<SupportChatModel>> getChatsForAdmin(String adminId) {
    return _firestore
        .collection(chatsCollection)
        .where('adminId', isEqualTo: adminId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => SupportChatModel.fromMap(doc.data()))
            .toList());
  }

  // جلب كل المحادثات لمشترك
  Stream<List<SupportChatModel>> getChatsForSubscriber(String subscriberId) {
    return _firestore
        .collection(chatsCollection)
        .where('subscriberId', isEqualTo: subscriberId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => SupportChatModel.fromMap(doc.data()))
            .toList());
  }

  // إرسال رسالة دعم + إشعار FCM تلقائي
  Future<void> sendSupportMessageAndNotify({
    required String chatId,
    required SupportMessageModel message,
    required String lastSender,
    required String receiverUserId, // id الطرف الآخر
    required String receiverType, // 'admin' أو 'subscriber'
  }) async {
    await sendMessage(chatId: chatId, message: message, lastSender: lastSender);
    // جلب توكن الطرف الآخر
    final userDoc = await FirebaseFirestore.instance.collection('users').doc(receiverUserId).get();
    final token = userDoc.data()?['fcmToken'];
    if (token != null && token.toString().isNotEmpty) {
      await sendFcmNotification(
        token: token,
        title: receiverType == 'admin' ? 'رسالة دعم جديدة من مشترك' : 'رد جديد من الدعم الفني',
        body: message.text,
      );
    }
  }

  // إرسال إشعار FCM
  Future<void> sendFcmNotification({
    required String token,
    required String title,
    required String body,
  }) async {
    const String serverKey = 'nYql-mcvbi40f4wE8MknlduIhWNbzo7jEihNF_Aeeqk'; // FCM Server Key
    final url = Uri.parse('https://fcm.googleapis.com/fcm/send');
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'key=$serverKey',
    };
    final payload = {
      'to': token,
      'notification': {
        'title': title,
        'body': body,
        'sound': 'default',
      },
      'data': {
        'click_action': 'FLUTTER_NOTIFICATION_CLICK',
      }
    };
    await http.post(url, headers: headers, body: jsonEncode(payload));
  }
} 