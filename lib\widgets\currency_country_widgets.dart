// Currency Display Widget
import 'package:flutter/material.dart';
import '../services/app_settings_service.dart';
import '../models/package_model.dart';

class CurrencyText extends StatefulWidget {
  final double amount;
  final TextStyle? style;
  final bool showSymbol;

  const CurrencyText({
    super.key,
    required this.amount,
    this.style,
    this.showSymbol = true,
  });

  @override
  State<CurrencyText> createState() => _CurrencyTextState();
}

class _CurrencyTextState extends State<CurrencyText> {
  String _formattedAmount = '';

  @override
  void initState() {
    super.initState();
    _formatAmount();
  }

  @override
  void didUpdateWidget(CurrencyText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.amount != widget.amount) {
      _formatAmount();
    }
  }

  Future<void> _formatAmount() async {
    if (widget.showSymbol) {
      final formatted = await AppSettingsService.formatCurrency(widget.amount);
      setState(() {
        _formattedAmount = formatted;
      });
    } else {
      setState(() {
        _formattedAmount = widget.amount.toStringAsFixed(0);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _formattedAmount,
      style: widget.style,
    );
  }
}

// Phone Number Display Widget
class PhoneText extends StatefulWidget {
  final String phoneNumber;
  final TextStyle? style;
  final bool showCountryCode;

  const PhoneText({
    super.key,
    required this.phoneNumber,
    this.style,
    this.showCountryCode = true,
  });

  @override
  State<PhoneText> createState() => _PhoneTextState();
}

class _PhoneTextState extends State<PhoneText> {
  String _formattedPhone = '';

  @override
  void initState() {
    super.initState();
    _formatPhone();
  }

  @override
  void didUpdateWidget(PhoneText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.phoneNumber != widget.phoneNumber) {
      _formatPhone();
    }
  }

  Future<void> _formatPhone() async {
    if (widget.showCountryCode) {
      final formatted = await AppSettingsService.formatPhoneNumber(widget.phoneNumber);
      setState(() {
        _formattedPhone = formatted;
      });
    } else {
      setState(() {
        _formattedPhone = widget.phoneNumber;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _formattedPhone,
      style: widget.style,
    );
  }
}

// Settings Summary Widget for quick display
class SettingsSummaryCard extends StatefulWidget {
  const SettingsSummaryCard({super.key});

  @override
  State<SettingsSummaryCard> createState() => _SettingsSummaryCardState();
}

class _SettingsSummaryCardState extends State<SettingsSummaryCard> {
  String _currencyInfo = '';
  String _countryInfo = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await AppSettingsService.getSettings();
      setState(() {
        _currencyInfo = '${settings.currencySymbol} ${settings.currencyCode}';
        _countryInfo = '${settings.countryFlag} ${settings.phoneCode}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _currencyInfo = 'د.ع IQD';
        _countryInfo = '🇮🇶 +964';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإعدادات الحالية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.attach_money, size: 16, color: Colors.green),
                          const SizedBox(width: 4),
                          Text(
                            'العملة:',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _currencyInfo,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.flag, size: 16, color: Colors.blue),
                          const SizedBox(width: 4),
                          Text(
                            'الدولة:',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _countryInfo,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Package Price Display Widget
class PackagePriceText extends StatefulWidget {
  final double price;
  final TextStyle? style;

  const PackagePriceText({
    super.key,
    required this.price,
    this.style,
  });

  @override
  State<PackagePriceText> createState() => _PackagePriceTextState();
}

class _PackagePriceTextState extends State<PackagePriceText> {
  String _formattedPrice = '';

  @override
  void initState() {
    super.initState();
    _formatPrice();
  }

  @override
  void didUpdateWidget(PackagePriceText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.price != widget.price) {
      _formatPrice();
    }
  }

  Future<void> _formatPrice() async {
    final formatted = await AppSettingsService.formatCurrency(widget.price);
    setState(() {
      _formattedPrice = formatted;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _formattedPrice,
      style: widget.style,
    );
  }
}

// Package Display Widget with Currency
class PackageDisplayText extends StatefulWidget {
  final PackageModel package;
  final TextStyle? style;
  final bool showPrice;
  final String separator;

  const PackageDisplayText({
    super.key,
    required this.package,
    this.style,
    this.showPrice = true,
    this.separator = ' - ',
  });

  @override
  State<PackageDisplayText> createState() => _PackageDisplayTextState();
}

class _PackageDisplayTextState extends State<PackageDisplayText> {
  String _displayText = '';

  @override
  void initState() {
    super.initState();
    _buildDisplayText();
  }

  @override
  void didUpdateWidget(PackageDisplayText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.package != widget.package) {
      _buildDisplayText();
    }
  }

  Future<void> _buildDisplayText() async {
    if (widget.showPrice) {
      final formattedPrice = await AppSettingsService.formatCurrency(widget.package.price);
      setState(() {
        _displayText = '${widget.package.name}${widget.separator}$formattedPrice';
      });
    } else {
      setState(() {
        _displayText = widget.package.name;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _displayText,
      style: widget.style,
    );
  }
}

// Phone Input Field Widget with Country Code
class PhoneInputField extends StatefulWidget {
  final TextEditingController controller;
  final String labelText;
  final FormFieldValidator<String>? validator;
  final bool isRequired;

  const PhoneInputField({
    super.key,
    required this.controller,
    this.labelText = 'رقم الهاتف',
    this.validator,
    this.isRequired = false,
  });

  @override
  State<PhoneInputField> createState() => _PhoneInputFieldState();
}

class _PhoneInputFieldState extends State<PhoneInputField> {
  String _countryCode = '+964';

  @override
  void initState() {
    super.initState();
    _loadCountryCode();
  }

  Future<void> _loadCountryCode() async {
    final phoneCode = await AppSettingsService.getPhoneCode();
    setState(() {
      _countryCode = phoneCode;
    });
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      decoration: InputDecoration(
        labelText: widget.isRequired ? '${widget.labelText} *' : widget.labelText,
        border: const OutlineInputBorder(),
        prefixText: '$_countryCode ',
        prefixStyle: TextStyle(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
        prefixIcon: const Icon(Icons.phone),
      ),
      keyboardType: TextInputType.phone,
      validator: widget.validator,
    );
  }
}

// Currency Input Field Widget
class CurrencyInputField extends StatefulWidget {
  final TextEditingController controller;
  final String labelText;
  final String hintText;
  final FormFieldValidator<String>? validator;
  final bool isRequired;

  const CurrencyInputField({
    super.key,
    required this.controller,
    this.labelText = 'المبلغ',
    this.hintText = 'أدخل المبلغ',
    this.validator,
    this.isRequired = false,
  });

  @override
  State<CurrencyInputField> createState() => _CurrencyInputFieldState();
}

class _CurrencyInputFieldState extends State<CurrencyInputField> {
  String _currencySymbol = 'د.ع';

  @override
  void initState() {
    super.initState();
    _loadCurrencySymbol();
  }

  Future<void> _loadCurrencySymbol() async {
    final symbol = await AppSettingsService.getCurrencySymbol();
    setState(() {
      _currencySymbol = symbol;
    });
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      decoration: InputDecoration(
        labelText: widget.isRequired ? '${widget.labelText} *' : widget.labelText,
        hintText: widget.hintText,
        border: const OutlineInputBorder(),
        suffixText: _currencySymbol,
        prefixIcon: const Icon(Icons.attach_money),
      ),
      keyboardType: TextInputType.number,
      validator: widget.validator,
    );
  }
}
