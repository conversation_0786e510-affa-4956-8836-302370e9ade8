import 'package:flutter/material.dart';
import '../models/expense_model.dart';
import '../models/expense_category_model.dart';
import '../services/database_service.dart';
import '../widgets/currency_country_widgets.dart';

class ExpenseLogPage extends StatefulWidget {
  const ExpenseLogPage({super.key});

  @override
  State<ExpenseLogPage> createState() => _ExpenseLogPageState();
}

class _ExpenseLogPageState extends State<ExpenseLogPage> {
  List<ExpenseModel> _expenses = [];
  Map<String, ExpenseCategoryModel> _categories = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadExpenses();
  }

  Future<void> _loadExpenses() async {
    setState(() => _isLoading = true);
    try {
      var expenses = await DatabaseService().getExpensesFire();
      if (expenses.isEmpty) {
        expenses = await DatabaseService().getExpenses();
      }
      var categories = await DatabaseService().getExpenseCategoriesFire();
      if (categories.isEmpty) {
        categories = await DatabaseService().getExpenseCategories();
      }
      setState(() {
        _expenses = List.from(expenses)..sort((a, b) => b.timestamp.compareTo(a.timestamp));
        _categories = {for (var c in categories) c.id: c};
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل سجل المصاريف: $e')),
        );
      }
    }
  }

  Future<void> _deleteExpense(String expenseId) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المصروف'),
        content: const Text('هل أنت متأكد أنك تريد حذف هذا المصروف؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
    if (confirm == true) {
      setState(() => _isLoading = true);
      try {
        await DatabaseService().deleteExpense(expenseId);
        await _loadExpenses();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف المصروف بنجاح.')),
          );
        }
      } catch (e) {
        setState(() => _isLoading = false);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف المصروف: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('سجل المصاريف'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _expenses.isEmpty
              ? const Center(child: Text('لا توجد مصاريف مسجلة.'))
              : RefreshIndicator(
                  onRefresh: _loadExpenses,
                  child: ListView.builder(
                    itemCount: _expenses.length,
                    itemBuilder: (context, index) {
                      final expense = _expenses[index];
                      final category = _categories[expense.categoryId];
                      return Card(
                        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),                        child: ListTile(
                          leading: Icon(Icons.money, color: Colors.green.shade700),
                          title: CurrencyText(
                            amount: expense.amount,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (category != null)
                                Text('الفئة: ${category.name}', style: const TextStyle(fontSize: 13)),
                              Text('التاريخ: ${expense.timestamp.day}/${expense.timestamp.month}/${expense.timestamp.year}', style: const TextStyle(fontSize: 13)),
                              if (expense.notes.isNotEmpty)
                                Text('ملاحظات: ${expense.notes}', style: const TextStyle(fontSize: 13, color: Colors.grey)),
                            ],
                          ),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => _deleteExpense(expense.id),
                          ),
                        ),
                      );
                    },
                  ),
                ),
    );
  }
}
