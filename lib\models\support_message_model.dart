import 'package:cloud_firestore/cloud_firestore.dart';

class SupportMessageModel {
  final String id;
  final String chatId;
  final String senderId;
  final String senderType; // 'subscriber' أو 'admin'
  final String text;
  final DateTime timestamp;
  final bool isRead;

  SupportMessageModel({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.senderType,
    required this.text,
    required this.timestamp,
    required this.isRead,
  });

  factory SupportMessageModel.fromMap(Map<String, dynamic> map) {
    return SupportMessageModel(
      id: map['id'] ?? '',
      chatId: map['chatId'] ?? '',
      senderId: map['senderId'] ?? '',
      senderType: map['senderType'] ?? '',
      text: map['text'] ?? '',
      timestamp: (map['timestamp'] as Timestamp).toDate(),
      isRead: map['isRead'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'chatId': chatId,
      'senderId': senderId,
      'senderType': senderType,
      'text': text,
      'timestamp': timestamp,
      'isRead': isRead,
    };
  }
} 