<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تم الدفع بنجاح - ISP Manager</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 500px;
            width: 100%;
        }
        .success-icon {
            font-size: 80px;
            color: #4CAF50;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        p {
            color: #666;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #45a049;
        }
        .info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        .transaction-id {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✅</div>
        <h1>تم الدفع بنجاح!</h1>
        <p>شكراً لك! تم إكمال عملية الدفع بنجاح عبر زين كاش.</p>
        
        <div class="info">
            <h3>ماذا بعد؟</h3>
            <p>• ارجع إلى تطبيق ISP Manager<br>
            • سيتم تفعيل اشتراكك تلقائياً<br>
            • يمكنك الآن الاستمتاع بجميع الميزات</p>
        </div>

        <div id="payment-details" style="display: none;">
            <div class="info">
                <h3>تفاصيل الدفع</h3>
                <p id="payment-amount"></p>
                <p id="payment-method"></p>
                <p id="payment-date"></p>
            </div>
        </div>

        <div id="transaction-info" style="display: none;">
            <p>رقم المعاملة: <span class="transaction-id" id="transaction-id"></span></p>
        </div>

        <a href="#" class="btn" onclick="closeWindow()">العودة للتطبيق</a>
        
        <p style="margin-top: 30px; font-size: 14px; color: #999;">
            إذا لم يتم تفعيل اشتراكك تلقائياً، يرجى التواصل مع الدعم الفني.
        </p>
    </div>

    <script>
        // استخراج معلومات المعاملة من URL
        function getUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            
            if (token) {
                try {
                    // فك تشفير JWT token (الجزء الثاني فقط - payload)
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    
                    if (payload.orderid) {
                        document.getElementById('transaction-id').textContent = payload.orderid;
                        document.getElementById('transaction-info').style.display = 'block';
                    }

                    // عرض تفاصيل الدفع إضافية
                    if (payload.amount) {
                        document.getElementById('payment-amount').textContent = `المبلغ: ${payload.amount} د.ع`;
                    }
                    if (payload.serviceType) {
                        document.getElementById('payment-method').textContent = `الخدمة: ${payload.serviceType}`;
                    }
                    if (payload.iat) {
                        const date = new Date(payload.iat * 1000);
                        document.getElementById('payment-date').textContent = `التاريخ: ${date.toLocaleString('ar-IQ')}`;
                    }

                    // إظهار تفاصيل الدفع
                    if (payload.amount || payload.serviceType || payload.iat) {
                        document.getElementById('payment-details').style.display = 'block';
                    }

                    // إظهار حالة المعاملة
                    if (payload.status === 'success' || payload.status === 'completed') {
                        document.querySelector('h1').textContent = 'تم الدفع بنجاح! ✅';
                        document.querySelector('.success-icon').textContent = '✅';
                        document.querySelector('p').textContent = 'شكراً لك! تم إكمال عملية الدفع بنجاح عبر زين كاش مع تأكيد OTP.';
                    } else if (payload.status === 'failed') {
                        document.querySelector('h1').textContent = 'فشل في الدفع ❌';
                        document.querySelector('.success-icon').textContent = '❌';
                        document.querySelector('.success-icon').style.color = '#f44336';
                        document.querySelector('p').textContent = 'عذراً، لم تكتمل عملية الدفع. يرجى المحاولة مرة أخرى أو التأكد من صحة بيانات المحفظة ورمز OTP.';
                    } else if (payload.status === 'pending' || payload.status === 'pending_otp') {
                        document.querySelector('h1').textContent = 'الدفع في الانتظار ⏳';
                        document.querySelector('.success-icon').textContent = '⏳';
                        document.querySelector('.success-icon').style.color = '#ff9800';
                        document.querySelector('p').textContent = 'عملية الدفع في انتظار تأكيد رمز OTP. يرجى إكمال العملية في التطبيق.';
                    }
                } catch (e) {
                    console.log('Error parsing token:', e);
                }
            }
        }

        function closeWindow() {
            // محاولة إغلاق النافذة أو العودة
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.close();
            }
        }

        // تشغيل عند تحميل الصفحة
        window.onload = getUrlParams;

        // إعادة توجيه تلقائي بعد 10 ثوان
        setTimeout(() => {
            closeWindow();
        }, 10000);
    </script>
</body>
</html>
