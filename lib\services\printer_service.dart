import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:esc_pos_utils/esc_pos_utils.dart'; // For CapabilityProfile, Generator, PosAlign, PosTextSize
import '../models/printer_settings_model.dart';
import 'dart:typed_data';
import 'receipt_image_service.dart';
import 'package:image/image.dart' as img; // Import image package

class PrinterService {
  static final BlueThermalPrinter _bluetooth = BlueThermalPrinter.instance;// تم إزالة طريقة printReceipt() النصية لحل مشاكل التشفير العربي
  // الآن يتم استخدام printReceiptUnified() مع asImage: true فقط

  /// طباعة الإيصال - تستخدم الآن الطباعة بالصورة فقط
  static Future<void> printReceipt({
    required PrinterSettingsModel settings,
    required Map<String, dynamic> data,
    required String operationType,
  }) async {
    await printReceiptUnified(
      settings: settings,
      data: data,
      operationType: operationType,
      asImage: true, // دائماً بالصورة
    );
  }

  /// التأكد من الاتصال أو إعادة الاتصال بالطابعة
  static Future<bool> _ensureConnection(PrinterSettingsModel settings) async {
    try {
      // التحقق من حالة الاتصال الحالية
      bool isConnected = await _bluetooth.isConnected ?? false;
      
      if (!isConnected && settings.connectedDeviceAddress != null) {
        // محاولة إعادة الاتصال
        List<BluetoothDevice> devices = await _bluetooth.getBondedDevices();
        BluetoothDevice? targetDevice = devices.firstWhere(
          (device) => device.address == settings.connectedDeviceAddress,
          orElse: () => throw Exception('الطابعة غير متوفرة'),
        );
        
        await _bluetooth.connect(targetDevice);
        await Future.delayed(Duration(milliseconds: 500)); // انتظار للتأكد من الاتصال
        return true;
      }
      
      return isConnected;
    } catch (e) {
      return false;
    }
  }  /// إعادة ضبط الطابعة في حالة حدوث مشاكل
  static Future<void> resetPrinter() async {
    try {
      bool isConnected = await _bluetooth.isConnected ?? false;
      if (isConnected) {
        // إرسال أوامر إعادة ضبط بسيطة
        await _bluetooth.writeBytes(Uint8List.fromList([0x1B, 0x40])); // ESC @ (reset)
        await Future.delayed(Duration(milliseconds: 200));
        
        // إعادة ضبط نهائية
        await _bluetooth.writeBytes(Uint8List.fromList([0x1B, 0x40])); // Reset نهائي
      }
    } catch (e) {
      // تجاهل أخطاء إعادة الضبط
    }
  }
    /// دالة طوارئ لإيقاف الطابعة في حالة تعليقها
  static Future<void> emergencyStop() async {
    try {
      bool isConnected = await _bluetooth.isConnected ?? false;
      if (isConnected) {
        // 1. إرسال أمر إلغاء فوري
        await _bluetooth.writeBytes(Uint8List.fromList([0x18])); // CAN (إلغاء فوري)
        await Future.delayed(Duration(milliseconds: 50));
        
        // 2. إرسال أمر إيقاف الطباعة
        await _bluetooth.writeBytes(Uint8List.fromList([0x1B, 0x53])); // ESC S (إيقاف طباعة)
        await Future.delayed(Duration(milliseconds: 50));

        // 3. مسح ذاكرة التخزين المؤقت
        await _bluetooth.writeBytes(Uint8List.fromList([0x0C])); // FF (Form Feed - مسح البافر)
        await Future.delayed(Duration(milliseconds: 50));
        
        // 4. إعادة ضبط كاملة
        await _bluetooth.writeBytes(Uint8List.fromList([0x1B, 0x40])); // ESC @ (reset كامل)
        await Future.delayed(Duration(milliseconds: 100));
        
        // 6. إعادة ضبط نهائية للتأكد
        await _bluetooth.writeBytes(Uint8List.fromList([0x1B, 0x40])); // Reset نهائي
        await Future.delayed(Duration(milliseconds: 100));
        
        // 7. إرسال أمر إيقاف تغذية الورق
        await _bluetooth.writeBytes(Uint8List.fromList([0x1B, 0x4A, 0x00])); // ESC J 0 (إيقاف تغذية الورق)
      }
    } catch (e) {
      // حتى لو فشل الإرسال، جرب قطع الاتصال وإعادة الربط
      try {
        await _bluetooth.disconnect();
        await Future.delayed(Duration(milliseconds: 500));
      } catch (disconnectError) {
        // تجاهل أخطاء قطع الاتصال
      }
    }
  }  /// طباعة صورة (Uint8List)
  static Future<void> printImage(Uint8List bytes, {required PrinterSettingsModel settings}) async {
    if (settings.connectedDeviceAddress == null) return;

    bool connected = await _ensureConnection(settings);
    if (!connected) {
      throw Exception('فشل في الاتصال بالطابعة');
    }

    try {
      final profile = await CapabilityProfile.load();
      final generator = Generator(PaperSize.mm80, profile); // Changed to mm80
      List<int> ticket = [];

      // طباعة الصورة
      final img.Image? image = img.decodeImage(bytes);
      if (image != null) {
        ticket += generator.image(image); 
      }
      await _bluetooth.writeBytes(Uint8List.fromList(ticket));
      
    } catch (e) {
      try {
        await emergencyStop(); // استخدام الإيقاف الطارئ عند الخطأ
      } catch (resetError) {
        // تجاهل أخطاء إعادة الضبط
      }
      rethrow;
    }
  }
  /// طباعة الإيصال بالصورة فقط (تم إزالة الطباعة النصية لحل مشاكل التشفير العربي)
  static Future<void> printReceiptUnified({
    required PrinterSettingsModel settings,
    required Map<String, dynamic> data,
    required String operationType,
    bool asImage = true, // دائماً true - طباعة كصورة فقط
  }) async {
    // طباعة كصورة للحصول على أفضل جودة للنصوص العربية وحل مشاكل التشفير
    await ReceiptImageService.printReceiptAsImage(
      settings: settings,
      data: data,
      operationType: operationType,
    );
  }
}
