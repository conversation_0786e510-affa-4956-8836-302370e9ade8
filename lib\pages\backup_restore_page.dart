import 'package:flutter/material.dart';
import 'dart:io';
import 'dart:convert';
import 'dart:math';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:file_selector/file_selector.dart';
import '../services/database_service.dart';
import '../services/telegram_service.dart';

class BackupRestorePage extends StatefulWidget {
  const BackupRestorePage({super.key});

  @override
  State<BackupRestorePage> createState() => _BackupRestorePageState();
}

class _BackupRestorePageState extends State<BackupRestorePage> {
  bool _isProcessing = false;
  String? _statusMessage;
  List<FileSystemEntity> _backupFiles = [];

  @override
  void initState() {
    super.initState();
    _loadBackupFiles();
  }

  Future<void> _loadBackupFiles() async {
    final dir = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${dir.path}/backups');
    if (!await backupDir.exists()) {
      setState(() {
        _backupFiles = [];
      });
      return;
    }
    final files = backupDir
        .listSync()
        .whereType<File>()
        .where((f) => f.path.endsWith('.json') || f.path.endsWith('.db'))
        .toList();
    files.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
    setState(() {
      _backupFiles = files;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('النسخ الاحتياطي والاستعادة'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'يمكنك هنا إنشاء نسخة احتياطية من بيانات التطبيق أو استعادة نسخة سابقة. ننصح بحفظ النسخة الاحتياطية في مكان آمن.',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              icon: const Icon(Icons.backup),
              label: const Text('إنشاء نسخة احتياطية'),
              onPressed: _isProcessing ? null : () async {
                await _createBackup();
                await _loadBackupFiles();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              icon: const Icon(Icons.restore),
              label: const Text('استعادة نسخة احتياطية'),
              onPressed: _isProcessing ? null : _restoreBackup,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            const SizedBox(height: 32),
            if (_statusMessage != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _statusMessage!,
                  style: const TextStyle(color: Colors.black87),
                  textAlign: TextAlign.center,
                ),
              ),
            const SizedBox(height: 24),
            Expanded(
              child: _buildBackupHistorySection(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupHistorySection() {
    if (_backupFiles.isEmpty) {
      return Center(
        child: Text(
          'لا توجد نسخ احتياطية محفوظة.',
          style: TextStyle(color: Colors.grey[600]),
        ),
      );
    }
    return ListView.separated(
      itemCount: _backupFiles.length,
      separatorBuilder: (_, __) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final file = _backupFiles[index] as File;
        final stat = file.statSync();
        final fileName = file.path.split(Platform.pathSeparator).last;
        final fileSize = stat.size;
        final modified = stat.modified;
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            leading: CircleAvatar(
              backgroundColor: fileName.endsWith('.json')
                  ? Colors.blue[50]
                  : Colors.green[50],
              child: Icon(
                fileName.endsWith('.json') ? Icons.description : Icons.storage,
                color: fileName.endsWith('.json') ? Colors.blue : Colors.green,
              ),
            ),
            title: Text(
              fileName,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 2),
                Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  spacing: 8,
                  runSpacing: 2,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.calendar_today, size: 14, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          _formatDate(modified),
                          style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.sd_storage, size: 14, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          _formatBytes(fileSize),
                          style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Tooltip(
                    message: 'استعادة هذه النسخة',
                    child: IconButton(
                      icon: const Icon(Icons.restore, color: Colors.green),
                      onPressed: _isProcessing ? null : () async {
                        setState(() => _isProcessing = true);
                        try {
                          await _restoreFromFile(file.path);
                          setState(() {
                            _statusMessage = 'تمت الاستعادة من النسخة بنجاح.';
                          });
                          _showSuccessDialog('تمت الاستعادة بنجاح', 'تم استعادة جميع البيانات من النسخة الاحتياطية.', Icons.check_circle);
                        } catch (e) {
                          setState(() {
                            _statusMessage = 'فشل في الاستعادة: $e';
                          });
                          _showErrorDialog('فشل في استعادة النسخة الاحتياطية', e.toString());
                        } finally {
                          setState(() => _isProcessing = false);
                        }
                      },
                    ),
                  ),
                ),
                Flexible(
                  child: Tooltip(
                    message: 'مشاركة النسخة',
                    child: IconButton(
                      icon: const Icon(Icons.share, color: Colors.blue),
                      onPressed: () async {
                        await Share.shareXFiles([XFile(file.path)]);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _formatBytes(int bytes, [int decimals = 2]) {
    if (bytes <= 0) return '0 B';
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    final i = (bytes == 0) ? 0 : (log(bytes) / log(1024)).floor();
    final size = bytes / pow(1024, i);
    return '${size.toStringAsFixed(decimals)} ${suffixes[i]}';
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')} - ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _restoreFromFile(String filePath) async {
    final file = File(filePath);
    if (!await file.exists()) throw Exception('الملف غير موجود');
    final fileName = filePath.toLowerCase();
    if (fileName.endsWith('.json')) {
      await DatabaseService().restoreFromJsonFile(filePath);
    } else if (fileName.endsWith('.db')) {
      await DatabaseService().restoreFromSqliteFile(filePath);
    } else {
      throw Exception('نوع الملف غير مدعوم');
    }
  }

  Future<void> _createBackup() async {
    setState(() {
      _isProcessing = true;
      _statusMessage = null;
    });
    final backupType = await _showBackupOptionsDialog();
    if (!mounted) return;
    if (backupType == null) {
      setState(() => _isProcessing = false);
      return;
    }
    String? tempDirectoryPath;
    try {
      tempDirectoryPath = await _getBackupDirectory();
      final backupDir = Directory('$tempDirectoryPath/backups');
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }
      tempDirectoryPath = backupDir.path;
      if (!mounted) return;
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isProcessing = false;
        _statusMessage = 'خطأ في تحديد مجلد الحفظ: $e';
      });
      await TelegramService().sendBackupNotification(
        backupType: backupType,
        fileName: 'N/A',
        fileSize: 0,
        success: false,
        errorMessage: 'خطأ في تحديد مجلد الحفظ: $e',
      );
      return;
    }
    _showLoadingDialog('جاري إنشاء النسخة الاحتياطية...');
    String tempBackupPath = '';
    try {
      if (backupType == 'json') {
        tempBackupPath = await DatabaseService().saveJsonBackupToFile(tempDirectoryPath);
      } else {
        tempBackupPath = await DatabaseService().createSqliteBackup(tempDirectoryPath);
      }
      if (!mounted) return;
      if (Navigator.of(context, rootNavigator: true).canPop()) {
        Navigator.of(context, rootNavigator: true).pop(); // Close loading dialog
      }
      await TelegramService().sendBackupNotification(
        backupType: backupType,
        fileName: tempBackupPath.split(Platform.pathSeparator).last,
        fileSize: File(tempBackupPath).lengthSync(),
        success: true,
      );
      await _showBackupActionDialog(tempBackupPath);
      if (!mounted) return;
      setState(() {
        _isProcessing = false;
        _statusMessage = 'تم إنشاء النسخة الاحتياطية بنجاح.';
      });
    } catch (e) {
      if (mounted && Navigator.of(context, rootNavigator: true).canPop()) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      if (!mounted) return;
      setState(() {
        _isProcessing = false;
        _statusMessage = 'فشل في إنشاء النسخة الاحتياطية: $e';
      });
      await TelegramService().sendBackupNotification(
        backupType: backupType,
        fileName: 'N/A',
        fileSize: 0,
        success: false,
        errorMessage: e.toString(),
      );
    }
  }

  Future<String?> _getBackupDirectory() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    } catch (e) {
      return null;
    }
  }

  Future<String?> _showBackupOptionsDialog() {
    return showDialog<String>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              Icon(Icons.backup, color: Theme.of(dialogContext).colorScheme.primary),
              const SizedBox(width: 8),
              const Expanded(child: Text('اختر نوع النسخة الاحتياطية', overflow: TextOverflow.ellipsis)),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.description),
                title: const Text('ملف JSON'),
                subtitle: const Text('نسخة احتياطية بصيغة JSON يمكن فتحها وتعديلها'),
                onTap: () => Navigator.of(dialogContext).pop('json'),
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.storage),
                title: const Text('قاعدة بيانات SQLite'),
                subtitle: const Text('نسخة احتياطية كاملة من قاعدة البيانات'),
                onTap: () => Navigator.of(dialogContext).pop('sqlite'),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                color: Theme.of(dialogContext).colorScheme.primary,
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: Theme.of(dialogContext).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _showBackupActionDialog(String backupPath) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            const SizedBox(width: 8),
            const Expanded(
              child: Text('تم إنشاء النسخة الاحتياطية', overflow: TextOverflow.ellipsis),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('تم إنشاء النسخة الاحتياطية بنجاح في:'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                backupPath,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              Share.shareXFiles([XFile(backupPath)]);
            },
            icon: const Icon(Icons.share),
            label: const Text('مشاركة'),
          ),
        ],
      ),
    );
  }

  Future<void> _restoreBackup() async {
    setState(() {
      _isProcessing = true;
      _statusMessage = null;
    });
    try {
      final restoreSource = await _showRestoreOptionsDialog();
      if (!mounted) return;
      if (restoreSource == null) {
        setState(() => _isProcessing = false);
        return;
      }
      if (restoreSource == 'internal') {
        await _restoreFromInternalStorage();
      } else if (restoreSource == 'external') {
        await _restoreFromExternalFile();
      }
      if (!mounted) return;
      setState(() {
        _isProcessing = false;
        _statusMessage = 'تمت الاستعادة بنجاح.';
      });
      _showSuccessDialog('تمت الاستعادة بنجاح', 'تم استعادة جميع البيانات من النسخة الاحتياطية.', Icons.check_circle);
      await TelegramService().sendSystemNotification(
        title: 'استعادة النسخة الاحتياطية',
        description: 'تمت استعادة البيانات بنجاح من النسخة الاحتياطية.',
        additionalInfo: 'المصدر: ${restoreSource == 'internal' ? 'التخزين الداخلي' : 'ملف خارجي'}',
      );
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isProcessing = false;
        _statusMessage = 'فشل في الاستعادة: $e';
      });
      _showErrorDialog('فشل في استعادة النسخة الاحتياطية', e.toString());
      await TelegramService().sendSystemNotification(
        title: 'فشل استعادة النسخة الاحتياطية',
        description: 'حدث خطأ أثناء محاولة استعادة البيانات.',
        additionalInfo: 'الخطأ: ${e.toString()}',
      );
    }
  }

  Future<String?> _showRestoreOptionsDialog() async {
    return await showDialog<String>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.restore, color: Theme.of(context).colorScheme.primary),
            const SizedBox(width: 8),
            const Expanded(child: Text('خيارات الاستعادة', overflow: TextOverflow.ellipsis)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.phone_android, color: Theme.of(context).colorScheme.primary),
              title: const Text('التخزين الداخلي'),
              subtitle: const Text('استعادة من ملف محفوظ في التطبيق'),
              onTap: () => Navigator.of(context).pop('internal'),
            ),
            const Divider(),
            ListTile(
              leading: Icon(Icons.folder_open, color: Theme.of(context).colorScheme.secondary),
              title: const Text('ملف خارجي'),
              subtitle: const Text('اختيار ملف نسخة احتياطية من الجهاز'),
              onTap: () => Navigator.of(context).pop('external'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog(String title, String message, IconData icon) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(icon, color: Colors.green),
            const SizedBox(width: 8),
            Expanded(child: Text(title, overflow: TextOverflow.ellipsis)),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Expanded(child: Text(title, overflow: TextOverflow.ellipsis)),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Future<void> _restoreFromInternalStorage() async {
    final dir = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${dir.path}/backups');
    if (!await backupDir.exists()) {
      throw Exception('لا يوجد مجلد نسخ احتياطي داخلي');
    }
    final files = backupDir
        .listSync()
        .whereType<File>()
        .where((f) => f.path.endsWith('.json') || f.path.endsWith('.db'))
        .toList();
    if (files.isEmpty) {
      throw Exception('لا يوجد ملفات نسخ احتياطي في التخزين الداخلي');
    }
    files.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
    final latestBackup = files.first;
    if (latestBackup.path.endsWith('.json')) {
      await DatabaseService().restoreFromJsonFile(latestBackup.path);
    } else {
      await DatabaseService().restoreFromSqliteFile(latestBackup.path);
    }
  }

  Future<void> _restoreFromExternalFile() async {
    const XTypeGroup jsonGroup = XTypeGroup(
      label: 'ملفات JSON',
      extensions: <String>['json'],
      mimeTypes: <String>['application/json', 'text/json'],
    );
    const XTypeGroup dbGroup = XTypeGroup(
      label: 'ملفات قاعدة البيانات',
      extensions: <String>['db', 'sqlite', 'sqlite3'],
      mimeTypes: <String>[
        'application/x-sqlite3',
        'application/vnd.sqlite3',
        'application/octet-stream',
      ],
    );
    const XTypeGroup allGroup = XTypeGroup(
      label: 'جميع الملفات المدعومة',
      extensions: <String>['json', 'db', 'sqlite', 'sqlite3'],
    );
    final XFile? file = await openFile(
      acceptedTypeGroups: <XTypeGroup>[allGroup, jsonGroup, dbGroup],
      confirmButtonText: 'اختيار ملف الاستعادة',
    );
    if (file != null) {
      final filePath = file.path;
      final fileObj = File(filePath);
      if (await fileObj.exists()) {
        final fileName = filePath.toLowerCase();
        if (fileName.endsWith('.json')) {
          try {
            final content = await fileObj.readAsString();
            jsonDecode(content);
            await DatabaseService().restoreFromJsonFile(filePath);
          } catch (e) {
            throw Exception('ملف JSON غير صحيح: $e');
          }
        } else if (fileName.endsWith('.db') ||
            fileName.endsWith('.sqlite') ||
            fileName.endsWith('.sqlite3')) {
          final fileSize = await fileObj.length();
          if (fileSize == 0) {
            throw Exception('ملف قاعدة البيانات فارغ');
          }
          await DatabaseService().restoreFromSqliteFile(filePath);
        } else {
          final bytes = await fileObj.readAsBytes();
          if (bytes.isEmpty) {
            throw Exception('الملف فارغ');
          }
          if (bytes.length >= 16) {
            final header = String.fromCharCodes(bytes.take(16));
            if (header.startsWith('SQLite format 3')) {
              await DatabaseService().restoreFromSqliteFile(filePath);
              return;
            }
          }
          try {
            final content = String.fromCharCodes(bytes);
            jsonDecode(content);
            await DatabaseService().restoreFromJsonFile(filePath);
            return;
          } catch (e) {
            // لم يتم التعرف على نوع الملف
          }
          throw Exception('نوع الملف غير مدعوم. يجب أن يكون:\n• ملف JSON (.json)\n• قاعدة بيانات SQLite (.db, .sqlite, .sqlite3)\n\nالملف المحدد: $fileName');
        }
      } else {
        throw Exception('الملف المحدد غير موجود');
      }
    } else {
      throw Exception('لم يتم اختيار أي ملف');
    }
  }
} 