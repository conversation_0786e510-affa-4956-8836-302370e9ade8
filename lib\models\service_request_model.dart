import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

enum RequestStatus {
  pending,
  approved,
  rejected,
  completed,
  cancelled
}

class ServiceRequestModel {
  final String id;
  final String subscriberName;
  final String subscriberPhone;
  final double subscriberLatitude;
  final double subscriberLongitude;
  final String towerId;
  final String towerName;
  final String selectedPackage;
  final double packagePrice;
  final RequestStatus status;
  final String? adminId;
  final String? notes;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ServiceRequestModel({
    required this.id,
    required this.subscriberName,
    required this.subscriberPhone,
    required this.subscriberLatitude,
    required this.subscriberLongitude,
    required this.towerId,
    required this.towerName,
    required this.selectedPackage,
    required this.packagePrice,
    required this.status,
    this.adminId,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'subscriberName': subscriberName,
      'subscriberPhone': subscriberPhone,
      'subscriberLatitude': subscriberLatitude,
      'subscriberLongitude': subscriberLongitude,
      'towerId': towerId,
      'towerName': towerName,
      'selectedPackage': selectedPackage,
      'packagePrice': packagePrice,
      'status': status.name,
      'adminId': adminId,
      'notes': notes,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  factory ServiceRequestModel.fromMap(Map<String, dynamic> map) {
    return ServiceRequestModel(
      id: map['id'] ?? '',
      subscriberName: map['subscriberName'] ?? '',
      subscriberPhone: map['subscriberPhone'] ?? '',
      subscriberLatitude: (map['subscriberLatitude'] ?? 0.0).toDouble(),
      subscriberLongitude: (map['subscriberLongitude'] ?? 0.0).toDouble(),
      towerId: map['towerId'] ?? '',
      towerName: map['towerName'] ?? '',
      selectedPackage: map['selectedPackage'] ?? '',
      packagePrice: (map['packagePrice'] ?? 0.0).toDouble(),
      status: RequestStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => RequestStatus.pending,
      ),
      adminId: map['adminId'],
      notes: map['notes'],
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: map['updatedAt'] != null 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : null,
    );
  }

  ServiceRequestModel copyWith({
    String? id,
    String? subscriberName,
    String? subscriberPhone,
    double? subscriberLatitude,
    double? subscriberLongitude,
    String? towerId,
    String? towerName,
    String? selectedPackage,
    double? packagePrice,
    RequestStatus? status,
    String? adminId,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ServiceRequestModel(
      id: id ?? this.id,
      subscriberName: subscriberName ?? this.subscriberName,
      subscriberPhone: subscriberPhone ?? this.subscriberPhone,
      subscriberLatitude: subscriberLatitude ?? this.subscriberLatitude,
      subscriberLongitude: subscriberLongitude ?? this.subscriberLongitude,
      towerId: towerId ?? this.towerId,
      towerName: towerName ?? this.towerName,
      selectedPackage: selectedPackage ?? this.selectedPackage,
      packagePrice: packagePrice ?? this.packagePrice,
      status: status ?? this.status,
      adminId: adminId ?? this.adminId,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get statusText {
    switch (status) {
      case RequestStatus.pending:
        return 'في الانتظار';
      case RequestStatus.approved:
        return 'تمت الموافقة';
      case RequestStatus.rejected:
        return 'مرفوض';
      case RequestStatus.completed:
        return 'مكتمل';
      case RequestStatus.cancelled:
        return 'ملغي';
    }
  }

  Color get statusColor {
    switch (status) {
      case RequestStatus.pending:
        return Colors.orange;
      case RequestStatus.approved:
        return Colors.blue;
      case RequestStatus.rejected:
        return Colors.red;
      case RequestStatus.completed:
        return Colors.green;
      case RequestStatus.cancelled:
        return Colors.grey;
    }
  }
} 