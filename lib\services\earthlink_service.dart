import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/earthlink_user_model.dart';
import '../models/earthlink_account_model.dart';

/// خدمة API للاتصال مع Earthlink Reseller
class EarthlinkService {
  static EarthlinkService? _authenticatedInstance;
  
  static EarthlinkService? getAuthenticatedInstance() {
    return _authenticatedInstance;
  }
  
  static void setAuthenticatedInstance(EarthlinkService instance) {
    _authenticatedInstance = instance;
  }
  
  static void clearAuthenticatedInstance() {
    _authenticatedInstance = null;
  }
  
  String? _token;
  String _baseUrl = '';

  /// تسجيل الدخول والحصول على التوكن
  Future<Map<String, dynamic>> login({
    required String username,
    required String password,
    int loginType = 1,
  }) async {
    // Set the base URL if it's not already set
    if (_baseUrl.isEmpty) {
      _baseUrl = 'https://rapi.earthlink.iq/api/reseller/';
    }
    
    final url = Uri.parse('$_baseUrl' + 'Token');
    final body = {
      'username': username,
      'password': password,
      'loginType': loginType.toString(),
      'grant_type': 'password',
    };

    print('[EarthlinkService] إرسال طلب تسجيل دخول POST إلى: $url');
    print('[EarthlinkService] البيانات المرسلة: $body');

    try {
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        body: body,
      );

      print('[EarthlinkService] كود الاستجابة لتسجيل الدخول: ${response.statusCode}');
      print('[EarthlinkService] محتوى الاستجابة: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data.containsKey('access_token')) {
          _token = data['access_token'];
          return {
            'success': true,
            'token': _token,
            'data': data,
          };
        } else {
          return {
            'success': false,
            'error': 'لم يتم العثور على access_token في الاستجابة',
            'data': data,
          };
        }
      } else {
        return {
          'success': false,
          'error': 'فشل تسجيل الدخول: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] خطأ في الاتصال: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة الاتصال بالخادم',
        'details': e.toString(),
      };
    }
  }

  /// جلب التوكن الحالي
  String? get token => _token;

  /// هل المستخدم مسجل دخول؟
  bool get isLoggedIn => _token != null;
  
  /// تعيين عنوان URL الأساسي
  void setBaseUrl(String baseUrl) {
    _baseUrl = baseUrl;
    // Ensure it ends with a slash
    if (!_baseUrl.endsWith('/')) {
      _baseUrl = '$_baseUrl/';
    }
  }

  /// تسجيل الخروج من Earthlink API
  Future<void> logout() async {
    _token = null;
    _baseUrl = '';
    // Clear this instance from the authenticated instances if it's the current one
    if (_authenticatedInstance == this) {
      _authenticatedInstance = null;
    }
    print('[EarthlinkService] Logged out from Earthlink API');
  }

  /// تسجيل الدخول مع إرجاع معلومات مفصلة عن الخطأ (متوافق مع واجهة SasApiService)
  Future<Map<String, dynamic>> loginWithDetailedResult({
    required String host,
    required String username,
    required String password,
    String portal = 'acp',
  }) async {
    // Set the base URL using the host parameter
    // Ensure the host has the proper protocol
    String cleanHost = host;
    if (!host.startsWith('http://') && !host.startsWith('https://')) {
      cleanHost = 'https://$host';
    }
    
    // Ensure the URL ends with the proper path
    if (!cleanHost.endsWith('/api/reseller/')) {
      if (cleanHost.endsWith('/')) {
        cleanHost = '${cleanHost}api/reseller/';
      } else {
        cleanHost = '$cleanHost/api/reseller/';
      }
    }
    
    _baseUrl = cleanHost;
    
    try {
      final loginResult = await login(
        username: username,
        password: password,
        loginType: 1,
      );

      if (loginResult['success'] == true) {
        return {
          'success': true,
          'token': loginResult['token'],
          'error': null,
          'error_type': null,
          'server_response': loginResult['data'],
        };
      } else {
        final error = loginResult['error'] as String?;
        return {
          'success': false,
          'token': null,
          'error': error,
          'error_type': _categorizeLoginError(error),
          'server_response': loginResult['data'],
        };
      }
    } catch (e) {
      return {
        'success': false,
        'token': null,
        'error': e.toString(),
        'error_type': 'connectivity',
        'server_response': null,
      };
    }
  }

  /// تصنيف نوع خطأ تسجيل الدخول
  String _categorizeLoginError(String? error) {
    if (error == null) return 'unknown';

    final errorLower = error.toLowerCase();

    if (errorLower.contains('invalid') ||
        errorLower.contains('password') ||
        errorLower.contains('username') ||
        errorLower.contains('credentials') ||
        errorLower.contains('unauthorized') ||
        errorLower.contains('forbidden')) {
      return 'authentication';
    }

    if (errorLower.contains('timeout') ||
        errorLower.contains('connection') ||
        errorLower.contains('network') ||
        errorLower.contains('unreachable')) {
      return 'connectivity';
    }

    if (errorLower.contains('server') ||
        errorLower.contains('internal') ||
        errorLower.contains('500') ||
        errorLower.contains('503')) {
      return 'server_error';
    }

    if (errorLower.contains('not found') || errorLower.contains('404')) {
      return 'endpoint_not_found';
    }

    return 'unknown';
  }

  /// الحصول على رسالة خطأ مفهومة للمستخدم
  String getHumanReadableError(String errorType, String? error) {
    switch (errorType) {
      case 'authentication':
        return 'اسم المستخدم أو كلمة المرور غير صحيحة';
      case 'connectivity':
        return 'مشكلة في الاتصال بالخادم. تحقق من اتصال الإنترنت';
      case 'server_error':
        return 'خطأ في الخادم. حاول مرة أخرى لاحقاً';
      case 'endpoint_not_found':
        return 'خدمة API غير متاحة على هذا الخادم';
      default:
        return error ?? 'خطأ غير معروف';
    }
  }

  /// تشخيص شامل لمشاكل تسجيل الدخول مع تقرير مفصل
  Future<Map<String, dynamic>> diagnoseLoginIssues({
    required String host,
    required String username,
    required String password,
    String portal = 'acp',
  }) async {
    final diagnosis = <String, dynamic>{
      'host': host,
      'username': username,
      'portal': portal,
      'timestamp': DateTime.now().toIso8601String(),
      'steps': <Map<String, dynamic>>[],
      'recommendations': <String>[],
      'overall_status': 'unknown',
    };

    print('[EarthlinkService] === بدء التشخيص الشامل لمشاكل تسجيل الدخول ===');

    try {
      diagnosis['steps'].add({
        'step': 1,
        'name': 'محاولة تسجيل الدخول',
        'status': 'running',
      });

      final loginResult = await loginWithDetailedResult(
        host: host,
        username: username,
        password: password,
        portal: portal,
      );

      diagnosis['login_result'] = loginResult;
      diagnosis['steps'].last['status'] = loginResult['success']
          ? 'success'
          : 'failed';
      diagnosis['steps'].last['details'] = loginResult;

      diagnosis['steps'].add({
        'step': 2,
        'name': 'تحليل النتائج',
        'status': 'running',
      });

      final recommendations = _generateDetailedRecommendations(loginResult);
      diagnosis['recommendations'] = recommendations;
      diagnosis['steps'].last['status'] = 'completed';

      if (loginResult['success'] == true) {
        diagnosis['overall_status'] = 'success';
      } else {
        final errorType = loginResult['error_type'] as String?;
        diagnosis['overall_status'] = errorType ?? 'unknown_error';
      }
    } catch (e) {
      diagnosis['error'] = e.toString();
      diagnosis['overall_status'] = 'diagnostic_failed';
      diagnosis['recommendations'].add('فشل التشخيص: $e');
    }

    print('[EarthlinkService] === انتهاء التشخيص الشامل ===');
    print('[EarthlinkService] النتيجة الإجمالية: ${diagnosis['overall_status']}');

    return diagnosis;
  }

  /// إنشاء توصيات مفصلة بناءً على نتائج تسجيل الدخول
  List<String> _generateDetailedRecommendations(Map<String, dynamic> loginResult) {
    final recommendations = <String>[];

    final loginSuccess = loginResult['success'] as bool? ?? false;
    final errorType = loginResult['error_type'] as String?;

    if (loginSuccess) {
      recommendations.add('✅ تم تسجيل الدخول بنجاح! الاتصال يعمل بشكل صحيح.');
      return recommendations;
    }

    switch (errorType) {
      case 'authentication':
        recommendations.add('🔑 مشكلة في بيانات الاعتماد:');
        recommendations.add('  • تحقق من صحة اسم المستخدم');
        recommendations.add('  • تحقق من صحة كلمة المرور');
        recommendations.add('  • تأكد من أنك تستخدم بيانات الاعتماد الصحيحة لـ Earthlink');
        recommendations.add('  • جرب تسجيل الدخول من المتصفح الموقع الرسمي');
        break;

      case 'connectivity':
        recommendations.add('🌐 مشكلة في الاتصال:');
        recommendations.add('  • فحص اتصال الإنترنت');
        recommendations.add('  • جرب VPN إذا كان الخادم محجوب');
        recommendations.add('  • تحقق من إعدادات DNS');
        recommendations.add('  • تأكد من أن الموقع rapi.earthlink.iq قابل للوصول');
        break;

      case 'server_error':
        recommendations.add('🖥️ خطأ في الخادم:');
        recommendations.add('  • فحص حالة موقع Earthlink الرسمي');
        recommendations.add('  • تحقق من وجود أعمال صيانة على الخادم');
        recommendations.add('  • انتظر قليلاً وحاول مرة أخرى');
        break;

      case 'endpoint_not_found':
        recommendations.add('📍 endpoint غير موجود:');
        recommendations.add('  • تتحقق من صحة عنوان API');
        recommendations.add('  • قد يكون هناك تغيير في واجهة برمجة التطبيقات');
        recommendations.add('  • تواصل مع دعم Earthlink');
        break;
    }

    recommendations.add('');
    recommendations.add('💡 خطوات إضافية للتشخيص:');
    recommendations.add('  • جرب الاتصال من جهاز آخر أو شبكة مختلفة');
    recommendations.add('  • تحقق من تحديثات Earthlink API');
    recommendations.add('  • راجع وثائق Earthlink للحصول على المساعدة');

    return recommendations;
  }

  /// جلب جميع أنواع الحسابات (الباقات) من Earthlink API
  Future<Map<String, dynamic>> getAllAccounts() async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    final url = Uri.parse('${_baseUrl}accounts/all');
    print('[EarthlinkService] Sending GET request to: $url');

    try {
      final response = await http.get(
        url,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
      );

      print('[EarthlinkService] Get all accounts response code: ${response.statusCode}');
      print('[EarthlinkService] Get all accounts response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data is List) {
          final accounts = data
              .map((account) => EarthlinkAccount.fromJson(account))
              .toList();
          return {
            'success': true,
            'data': accounts,
          };
        } else if (data is Map && data.containsKey('value') && data['value'] is List) {
          final accounts = (data['value'] as List)
              .map((account) => EarthlinkAccount.fromJson(account))
              .toList();
          return {
            'success': true,
            'data': accounts,
          };
        } else {
          return {
            'success': false,
            'error': 'Unexpected response format',
            'details': data,
          };
        }
      } else {
        return {
          'success': false,
          'error': 'Failed to get accounts: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] Error getting accounts: $e');
      return {
        'success': false,
        'error': 'An error occurred while trying to get accounts',
        'details': e.toString(),
      };
    }
  }

  /// جلب جميع المستخدمين باستخدام البحث المتقدم مع تصفح صحيح
  /// This method properly uses pagination to fetch all users efficiently
  Future<Map<String, dynamic>> fetchAllUsersWithPagination({
    int userStatusID = 0, // Default to "All" (0)
    int? timePeriodID,
    int batchSize = 100,
    int maxUsers = 10000, // Safety limit to prevent infinite loops
  }) async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    print('[EarthlinkService] Fetching all users with proper pagination...');
    
    try {
      final allUsers = <EarthlinkUser>[];
      int startIndex = 0;
      bool hasMoreUsers = true;
      int totalFetched = 0;
      
      while (hasMoreUsers && totalFetched < maxUsers) {
        print('[EarthlinkService] Fetching users batch: $startIndex - ${startIndex + batchSize} (Total so far: $totalFetched)');
        
        final result = await searchUsers(
          userStatusID: userStatusID,
          timePeriodID: timePeriodID,
          startIndex: startIndex,
          rowCount: batchSize,
        );
        
        if (result['success'] && result['data'] is Map) {
          final data = result['data'] as Map<String, dynamic>;
          if (data.containsKey('value') && data['value'] is Map) {
            final valueData = data['value'] as Map<String, dynamic>;
            if (valueData.containsKey('itemsList') && valueData['itemsList'] is List) {
              final items = valueData['itemsList'] as List;
              if (items.isEmpty) {
                // No more users to fetch
                hasMoreUsers = false;
                break;
              }
              
              int usersInBatch = 0;
              for (final item in items) {
                if (item is Map<String, dynamic> && item.containsKey('userIndex')) {
                  // Create a mutable copy of the item map
                  Map<String, dynamic> finalUserData = Map.from(item);

                  // If there's a nested userObject, merge its data.
                  // The fields in userObject will overwrite fields in the parent if they conflict.
                  if (item.containsKey('userObject') && item['userObject'] is Map<String, dynamic>) {
                    finalUserData.addAll(item['userObject'] as Map<String, dynamic>);
                  }
                  
                  // Create the user from the merged map
                  final user = EarthlinkUser.fromJson(finalUserData);
                  allUsers.add(user);
                  usersInBatch++;
                }
              }
              
              print('[EarthlinkService] Batch contained $usersInBatch users');
              totalFetched += usersInBatch;
              
              // If we got fewer users than requested, we've reached the end
              if (usersInBatch < batchSize) {
                hasMoreUsers = false;
              }
              
              // Move to next batch
              startIndex += batchSize;
              
              // Add a small delay to avoid overwhelming the API
              await Future.delayed(Duration(milliseconds: 100));
            } else {
              hasMoreUsers = false;
            }
          } else {
            hasMoreUsers = false;
          }
        } else {
          // Handle error case
          print('[EarthlinkService] Error fetching user batch: ${result['error']}');
          hasMoreUsers = false;
        }
      }
      
      print('[EarthlinkService] Successfully fetched ${allUsers.length} users using pagination');
      return {
        'success': true,
        'data': allUsers,
      };
    } catch (e) {
      print('[EarthlinkService] Error fetching all users with pagination: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة جلب جميع المستخدمين',
        'details': e.toString(),
      };
    }
  }

  /// تجديد مستخدم باستخدام رصيد المدير - نفس منطق SAS
  Future<Map<String, dynamic>> renewUserWithDeposit({
    required String userId,
    required String depositPassword,
    required int accountIndex,
    required String agentIndex,
    required String affiliateIndex,
    String? earthMaxMAC,
    String? affiliateTypeID,
    String? firstName,
    String? lastName,
    String? displayName,
    String? phoneNumber,
    String? email,
    String? address,
  }) async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    final url = Uri.parse('${_baseUrl}user/newuserdeposit');
    print('[EarthlinkService] Renewing user via deposit: $url');
    print('[EarthlinkService] User ID: $userId');
    print('[EarthlinkService] Account Index: $accountIndex');

    try {
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: {
          'DepositPassword': depositPassword,
          'AgentIndex': agentIndex,
          'AffiliateIndex': affiliateIndex,
          'AccountIndex': accountIndex.toString(),
          'UserID': userId,
          'UserPass': '1',
          'EarthMaxMAC': earthMaxMAC ?? '',
          'AffiliateTypeID': affiliateTypeID ?? '',
          'FirstName': firstName ?? '',
          'LastName': lastName ?? '',
          'Company': '',
          'Address': address ?? '',
          'City': '',
          'State': '',
          'Country': '',
          'Zip': '',
          'Email': email ?? '',
          'MobileNumber': phoneNumber ?? '',
          'HomePhone': '',
          'WorkPhone': '',
          'ArName': displayName ?? '',
          'EnName': '',
          'DisplayName': displayName ?? '',
        },
      );

      print('[EarthlinkService] Renew user response code: ${response.statusCode}');
      print('[EarthlinkService] Renew user response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        // Check if the API actually succeeded
        bool isSuccessful = data is Map && 
            ((data.containsKey('isSuccessful') && data['isSuccessful'] == true) ||
             (data.containsKey('success') && data['success'] == true));
        
        // If isSuccessful key doesn't exist, check for error key
        bool hasError = data is Map && data.containsKey('error') && data['error'] != null;
        
        if (isSuccessful) {
          // Success case
          if (data.containsKey('value') && data['value'] != null) {
            final userIndex = data['value'];
            return {
              'success': true,
              'userIndex': userIndex,
              'message': 'تم تجديد المستخدم بنجاح عبر رصيد المدير',
              'data': data,
            };
          } else {
            return {
              'success': true,
              'message': 'تم تجديد المستخدم بنجاح عبر رصيد المدير',
              'data': data,
            };
          }
        } else if (hasError) {
          // Error case with detailed error message
          String errorMessage = 'فشل تجديد المستخدم';
          List<String> validationErrors = [];
          
          if (data['error'] is Map && data['error'].containsKey('message')) {
            errorMessage = data['error']['message'] ?? errorMessage;
          }
          
          // Handle validation errors
          if (data['error'] is Map && 
              data['error'].containsKey('validationErrors') && 
              data['error']['validationErrors'] is List) {
            for (var validationError in data['error']['validationErrors']) {
              if (validationError is Map && validationError.containsKey('validationMessage')) {
                validationErrors.add(validationError['validationMessage']);
              }
            }
          }
          
          return {
            'success': false,
            'error': errorMessage,
            'validationErrors': validationErrors,
            'details': data,
          };
        } else {
          // Unexpected response format
          return {
            'success': false,
            'error': 'استجابة غير متوقعة من الخادم',
            'details': data,
          };
        }
      } else {
        return {
          'success': false,
          'error': 'فشل تجديد المستخدم: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] Error renewing user with deposit: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة تجديد المستخدم',
        'details': e.toString(),
      };
    }
  }

  /// الحصول على رصيد المدير
  Future<Map<String, dynamic>> getAdminBalance() async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    final url = Uri.parse('${_baseUrl}affiliate/deposit/balance');
    print('[EarthlinkService] Getting admin balance: $url');

    try {
      final response = await http.get(
        url,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
      );

      print('[EarthlinkService] Balance response code: ${response.statusCode}');
      print('[EarthlinkService] Balance response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data.containsKey('value')) {
          return {
            'success': true,
            'balance': data['value'],
            'data': data,
          };
        } else {
          return {
            'success': false,
            'error': 'لم يتم العثور على رصيد في الاستجابة',
            'details': data,
          };
        }
      } else {
        return {
          'success': false,
          'error': 'فشل جلب الرصيد: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] Error getting admin balance: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة جلب الرصيد',
        'details': e.toString(),
      };
    }
  }

  /// التحقق من تكلفة الحساب قبل التجديد
  Future<Map<String, dynamic>> getAccountCost({
    required int accountIndex,
    String? targetAffiliateId,
    String? affiliateTypeId,
  }) async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    final url = Uri.parse('${_baseUrl}affiliate/deposit/accountCost');
    print('[EarthlinkService] Getting account cost: $url');

    try {
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: {
          'UserID': '',
          'TargetAffiliateID': targetAffiliateId ?? '',
          'AccountID': accountIndex.toString(),
          'AffiliateTypeID': affiliateTypeId ?? '',
        },
      );

      print('[EarthlinkService] Account cost response code: ${response.statusCode}');
      print('[EarthlinkService] Account cost response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        // Check if the API actually succeeded
        bool isSuccessful = data is Map && 
            ((data.containsKey('isSuccessful') && data['isSuccessful'] == true) ||
             (data.containsKey('success') && data['success'] == true));
        
        // If isSuccessful key doesn't exist, check for error key
        bool hasError = data is Map && data.containsKey('error') && data['error'] != null;
        
        if (isSuccessful) {
          // Success case - extract cost information
          if (data.containsKey('value')) {
            return {
              'success': true,
              'cost': data['value']['value'] ?? 0.0,
              'currency': data['value']['currencySign'] ?? '\$',
              'data': data,
            };
          } else {
            return {
              'success': false,
              'error': 'لم يتم العثور على تكلفة في الاستجابة',
              'details': data,
            };
          }
        } else if (hasError) {
          // Error case with detailed error message
          String errorMessage = 'فشل جلب تكلفة الحساب';
          
          if (data['error'] is Map && data['error'].containsKey('message')) {
            errorMessage = data['error']['message'] ?? errorMessage;
          }
          
          return {
            'success': false,
            'error': errorMessage,
            'details': data,
          };
        } else {
          // Unexpected response format
          return {
            'success': false,
            'error': 'استجابة غير متوقعة من الخادم',
            'details': data,
          };
        }
      } else {
        return {
          'success': false,
          'error': 'فشل جلب تكلفة الحساب: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] Error getting account cost: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة جلب تكلفة الحساب',
        'details': e.toString(),
      };
    }
  }

  /// جلب تفاصيل المستخدم الكاملة من Earthlink API
  /// Note: Earthlink API has limited user detail endpoints, so we enhance data through other means
  Future<Map<String, dynamic>> getUserDetails({
    required int userIndex,
  }) async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    // Earthlink API doesn't have a direct endpoint for detailed user information.
    // The autocomplete endpoint with user index doesn't provide additional details.
    // We'll return a success response with basic user data and let the caller enhance it.
    
    return {
      'success': true,
      'data': {
        'userIndex': userIndex,
        // Additional fields will be populated by the caller using other API endpoints
      },
    };
  }

  /// جلب تفاصيل المستخدم مع معلومات الاشتراك من Earthlink API
  /// This method attempts to get more detailed user information
  Future<Map<String, dynamic>> getDetailedUserSubscriptionInfo({
    required String userId,
  }) async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    try {
      // First, try to get user invoices which may contain subscription information
      final invoicesResult = await getUsersInvoices(query: userId);
      
      if (invoicesResult['success']) {
        final data = invoicesResult['data'];
        if (data is Map && data.containsKey('value') && data['value'] is Map) {
          final valueData = data['value'];
          if (valueData.containsKey('itemsList') && valueData['itemsList'] is List) {
            final items = valueData['itemsList'] as List;
            if (items.isNotEmpty) {
              // Look for the most recent invoice which might contain subscription details
              final latestInvoice = items.firstWhere(
                (item) => item is Map && item.containsKey('userId') && item['userId'] == userId,
                orElse: () => items.isNotEmpty ? items.first : null
              );
              
              if (latestInvoice != null) {
                return {
                  'success': true,
                  'data': latestInvoice,
                };
              }
              
              // If we couldn't find exact match, return the first item
              if (items.isNotEmpty) {
                return {
                  'success': true,
                  'data': items.first,
                };
              }
            }
          }
        }
      }
      
      // If invoices don't provide the info, try the dashboard stats
      final dashboardResult = await getDashboardStats();
      if (dashboardResult['success']) {
        return {
          'success': true,
          'data': {
            'userId': userId,
            'dashboardInfo': dashboardResult['data'],
          },
        };
      }
      
      // Try to get user details from the autocomplete endpoint with more specific query
      final userSearchResult = await getUsers(query: userId);
      if (userSearchResult['success']) {
        final userData = userSearchResult['data'];
        if (userData is List && userData.isNotEmpty) {
          // Look for exact match
          final exactMatch = userData.firstWhere(
            (user) => user is Map && 
                     ((user.containsKey('userId') && user['userId'] == userId) ||
                      (user.containsKey('userID') && user['userID'] == userId)),
            orElse: () => userData.first
          );
          
          if (exactMatch != null) {
            return {
              'success': true,
              'data': exactMatch,
            };
          }
        }
      }
      
      // Try to get user subscription report using the correct endpoint from documentation
      final userSubscriptionResult = await getUsersSubscriptions();
      if (userSubscriptionResult['success']) {
        // Try to find the specific user in the subscriptions data
        final subscriptionData = userSubscriptionResult['data'];
        if (subscriptionData is Map) {
          return {
            'success': true,
            'data': {
              'userId': userId,
              'subscriptionInfo': subscriptionData,
            },
          };
        }
      }
      
      // Try advanced search as last resort
      final searchResult = await searchUsers(userId: userId, rowCount: 1);
      if (searchResult['success']) {
        final data = searchResult['data'];
        if (data is Map && data.containsKey('value') && data['value'] is Map) {
          final valueData = data['value'];
          if (valueData.containsKey('itemsList') && valueData['itemsList'] is List) {
            final items = valueData['itemsList'] as List;
            if (items.isNotEmpty) {
              final userItem = items.first;
              if (userItem is Map) {
                return {
                  'success': true,
                  'data': userItem,
                };
              }
            }
          }
        }
      }
      
      return {
        'success': false,
        'error': 'Could not retrieve detailed user subscription information',
      };
    } catch (e) {
      print('[EarthlinkService] Error getting detailed user subscription info: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة جلب معلومات الاشتراك المفصلة',
        'details': e.toString(),
      };
    }
  }

  /// جلب معلومات المستخدمين المنتهية صلاحيتها قريباً
  Future<Map<String, dynamic>> getExpiringUsers() async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    try {
      // Try the correct endpoint for expiring users
      final url = Uri.parse('${_baseUrl}dash/ExpiringUsers');
      print('[EarthlinkService] Getting expiring users: $url');

      final response = await http.get(
        url,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
      );

      print('[EarthlinkService] Expiring users response code: ${response.statusCode}');
      print('[EarthlinkService] Expiring users response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return {
          'success': true,
          'data': data,
        };
      } else if (response.statusCode == 404) {
        // If the endpoint doesn't exist, try alternative approach
        print('[EarthlinkService] ExpiringUsers endpoint not found, trying alternative approach');
        
        // Use usersSubscriptions as fallback
        final subscriptionsResult = await getUsersSubscriptions();
        if (subscriptionsResult['success']) {
          return {
            'success': true,
            'data': subscriptionsResult['data'],
            'message': 'Used usersSubscriptions endpoint as ExpiringUsers endpoint is not available',
          };
        }
      }
      
      return {
        'success': false,
        'error': 'فشل جلب المستخدمين المنتهية صلاحيتهم: ${response.statusCode}',
        'details': response.body,
      };
    } catch (e) {
      print('[EarthlinkService] Error getting expiring users: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة جلب المستخدمين المنتهية صلاحيتهم',
        'details': e.toString(),
      };
    }
  }

  /// جلب معلومات الإحصائيات من لوحة التحكم
  Future<Map<String, dynamic>> getDashboardStats() async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    final url = Uri.parse('${_baseUrl}home/Dashboard');
    print('[EarthlinkService] Getting dashboard stats: $url');

    try {
      final response = await http.get(
        url,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
      );

      print('[EarthlinkService] Dashboard stats response code: ${response.statusCode}');
      print('[EarthlinkService] Dashboard stats response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data.containsKey('value')) {
          return {
            'success': true,
            'data': data['value'],
          };
        } else {
          return {
            'success': false,
            'error': 'لم يتم العثور على إحصائيات لوحة التحكم',
            'details': data,
          };
        }
      } else {
        return {
          'success': false,
          'error': 'فشل جلب إحصائيات لوحة التحكم: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] Error getting dashboard stats: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة جلب إحصائيات لوحة التحكم',
        'details': e.toString(),
      };
    }
  }

  /// جلب المعلومات إذا كان النظام محدودًا بمعدل الطلبات
  Future<bool> isRateLimited() async {
    if (!isLoggedIn) return false;
    
    try {
      final url = Uri.parse('${_baseUrl}home/Dashboard');
      final response = await http.get(
        url,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
      );
      
      return response.statusCode == 429;
    } catch (e) {
      return false;
    }
  }

  /// إغلاق خدمة Earthlink وتنظيف الموارد
  Future<void> close() async {
    await logout();
    print('[EarthlinkService] Earthlink service closed');
  }

  /// جلب معلومات المستخدمين من خلال تقارير الاشتراكات
  Future<Map<String, dynamic>> getUsersSubscriptions() async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    final url = Uri.parse('${_baseUrl}reports/usersSubscriptions');
    print('[EarthlinkService] Getting users subscriptions: $url');

    try {
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: {},
      );

      print('[EarthlinkService] Users subscriptions response code: ${response.statusCode}');
      print('[EarthlinkService] Users subscriptions response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data.containsKey('value')) {
          return {
            'success': true,
            'data': data['value'],
          };
        } else {
          return {
            'success': false,
            'error': 'لم يتم العثور على تقارير الاشتراكات',
            'details': data,
          };
        }
      } else {
        return {
          'success': false,
          'error': 'فشل جلب تقارير الاشتراكات: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] Error getting users subscriptions: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة جلب تقارير الاشتراكات',
        'details': e.toString(),
      };
    }
  }

  /// جلب تقارير فواتير المستخدمين
  Future<Map<String, dynamic>> getUsersInvoices({
    String query = '',
    int startIndex = 0,
    int rowCount = 50,
  }) async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    final url = Uri.parse('${_baseUrl}userpayment/usersInvoice');
    print('[EarthlinkService] Getting users invoices: $url');

    try {
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: {
          'Query': query,
          'StartIndex': startIndex.toString(),
          'RowCount': rowCount.toString(),
        },
      );

      print('[EarthlinkService] Users invoices response code: ${response.statusCode}');
      print('[EarthlinkService] Users invoices response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return {
          'success': true,
          'data': data,
        };
      } else if (response.statusCode == 429) {
        // Rate limiting response
        print('[EarthlinkService] Rate limit exceeded. Please wait before making more requests.');
        return {
          'success': false,
          'error': 'تم تجاوز الحد المسموح من الطلبات. يرجى الانتظار قبل إجراء المزيد من الطلبات.',
          'rateLimited': true,
        };
      } else {
        return {
          'success': false,
          'error': 'فشل جلب فواتير المستخدمين: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] Error getting users invoices: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة جلب فواتير المستخدمين',
        'details': e.toString(),
      };
    }
  }

  /// جلب معلومات ملف المستخدم
  Future<Map<String, dynamic>> getUserProfile({
    required String userId,
  }) async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    final url = Uri.parse('${_baseUrl}user/profile?userId=$userId');
    print('[EarthlinkService] Getting user profile: $url');

    try {
      final response = await http.get(
        url,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
      );

      print('[EarthlinkService] User profile response code: ${response.statusCode}');
      print('[EarthlinkService] User profile response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return {
          'success': true,
          'data': data,
        };
      } else {
        return {
          'success': false,
          'error': 'فشل جلب معلومات ملف المستخدم: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] Error getting user profile: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة جلب معلومات ملف المستخدم',
        'details': e.toString(),
      };
    }
  }

  /// جلب تقرير اشتراك المستخدم
  Future<Map<String, dynamic>> getUserSubscriptionReport({
    required String userId,
  }) async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    final url = Uri.parse('${_baseUrl}reports/userSubscription?userId=$userId');
    print('[EarthlinkService] Getting user subscription report: $url');

    try {
      final response = await http.get(
        url,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
      );

      print('[EarthlinkService] User subscription report response code: ${response.statusCode}');
      print('[EarthlinkService] User subscription report response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return {
          'success': true,
          'data': data,
        };
      } else {
        return {
          'success': false,
          'error': 'فشل جلب تقرير اشتراك المستخدم: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] Error getting user subscription report: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة جلب تقرير اشتراك المستخدم',
        'details': e.toString(),
      };
    }
  }

  /// البحث عن المستخدمين باستخدام معايير متقدمة
  Future<Map<String, dynamic>> searchUsers({
    int? userStatusID,
    int? timePeriodID,
    int? subAffiliateIndex,
    int? accountStatusID,
    String? accountType,
    List<int>? accountIDs,
    String? userId,
    String? firstName,
    String? lastName,
    String? callerId,
    String? phoneFax,
    String? arName,
    String? enName,
    String? notes,
    DateTime? createDate1,
    DateTime? createDate2,
    String? createDate1S,
    String? createDate2S,
    DateTime? lastModified1,
    DateTime? lastModified2,
    String? lastModified1S,
    String? lastModified2S,
    DateTime? expiration1,
    DateTime? expiration2,
    String? expiration1S,
    String? expiration2S,
    int startIndex = 0,
    int rowCount = 50,
    String? orderBy,
    bool orderDescending = false,
    String? query,
  }) async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    final url = Uri.parse('${_baseUrl}user/all');
    print('[EarthlinkService] Searching users: $url');
    
    // Check for rate limiting before making request
    if (await isRateLimited()) {
      print('[EarthlinkService] Currently rate limited, waiting before search request...');
      await Future.delayed(Duration(seconds: 5));
    }
    
    try {
      final body = <String, String>{};
      
      // Add parameters to body if they are not null
      if (userStatusID != null) body['UserStatusID'] = userStatusID.toString();
      if (timePeriodID != null) body['TimePeriodID'] = timePeriodID.toString();
      if (subAffiliateIndex != null) body['SubAffliateIndex'] = subAffiliateIndex.toString();
      if (accountStatusID != null) body['AccountStatusID'] = accountStatusID.toString();
      if (accountType != null) body['AccountType'] = accountType;
      if (accountIDs != null && accountIDs.isNotEmpty) body['AccountIDs'] = accountIDs.join(',');
      if (userId != null) body['UserId'] = userId;
      if (firstName != null) body['FirstName'] = firstName;
      if (lastName != null) body['LastName'] = lastName;
      if (callerId != null) body['CallerId'] = callerId;
      if (phoneFax != null) body['PhoneFax'] = phoneFax;
      if (arName != null) body['ArName'] = arName;
      if (enName != null) body['EnName'] = enName;
      if (notes != null) body['Notes'] = notes;
      if (createDate1 != null) body['CreateDate1'] = createDate1.toIso8601String();
      if (createDate2 != null) body['CreateDate2'] = createDate2.toIso8601String();
      if (createDate1S != null) body['CreateDate1S'] = createDate1S;
      if (createDate2S != null) body['CreateDate2S'] = createDate2S;
      if (lastModified1 != null) body['LastModified1'] = lastModified1.toIso8601String();
      if (lastModified2 != null) body['LastModified2'] = lastModified2.toIso8601String();
      if (lastModified1S != null) body['LastModified1S'] = lastModified1S;
      if (lastModified2S != null) body['LastModified2S'] = lastModified2S;
      if (expiration1 != null) body['Expiration1'] = expiration1.toIso8601String();
      if (expiration2 != null) body['Expiration2'] = expiration2.toIso8601String();
      if (expiration1S != null) body['Expiration1S'] = expiration1S;
      if (expiration2S != null) body['Expiration2S'] = expiration2S;
      body['Startindex'] = startIndex.toString();
      body['Rowcount'] = rowCount.toString();
      if (orderBy != null) body['OrderBy'] = orderBy;
      body['OrderDescending'] = orderDescending ? 'true' : 'false';
      if (query != null) body['Query'] = query;

      print('[EarthlinkService] Search users request body: $body');

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: body,
      );

      print('[EarthlinkService] Search users response code: ${response.statusCode}');
      print('[EarthlinkService] Search users response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return {
          'success': true,
          'data': data,
        };
      } else {
        return {
          'success': false,
          'error': 'فشل البحث عن المستخدمين: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] Error searching users: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة البحث عن المستخدمين',
        'details': e.toString(),
      };
    }
  }

  /// جلب المستخدمين المنتهية صلاحيتهم مؤخراً
  Future<Map<String, dynamic>> getRecentlyExpiredUsers() async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    final url = Uri.parse('${_baseUrl}dash/RecentlyExpired');
    print('[EarthlinkService] Getting recently expired users: $url');

    try {
      final response = await http.get(
        url,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
      );

      print('[EarthlinkService] Recently expired users response code: ${response.statusCode}');
      print('[EarthlinkService] Recently expired users response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return {
          'success': true,
          'data': data,
        };
      } else {
        return {
          'success': false,
          'error': 'فشل جلب المستخدمين المنتهية صلاحيتهم مؤخراً: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] Error getting recently expired users: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة جلب المستخدمين المنتهية صلاحيتهم مؤخراً',
        'details': e.toString(),
      };
    }
  }

  /// جلب معلومات الاشتراك للمستخدم مباشرة من تقرير الاشتراكات
  Future<Map<String, dynamic>> getUserSubscriptionInfoDirect({
    required String userId,
  }) async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }

    try {
      // Get all users subscriptions report
      final subscriptionsResult = await getUsersSubscriptions();
      if (subscriptionsResult['success']) {
        // The usersSubscriptions endpoint returns general stats, not individual user info
        // But we can use it to get general information about user counts
        final subscriptionData = subscriptionsResult['data'];
        if (subscriptionData is Map) {
          return {
            'success': true,
            'data': {
              'userId': userId,
              'generalStats': subscriptionData,
            },
          };
        }
      }
      
      // Try to get dashboard stats which might contain relevant information
      final dashboardResult = await getDashboardStats();
      if (dashboardResult['success']) {
        final dashboardData = dashboardResult['data'];
        if (dashboardData is Map) {
          return {
            'success': true,
            'data': {
              'userId': userId,
              'dashboardStats': dashboardData,
            },
          };
        }
      }

      return {
        'success': false,
        'error': 'Could not retrieve user subscription information',
      };
    } catch (e) {
      print('[EarthlinkService] Error getting user subscription info directly: $e');
      return {
        'success': false,
        'error': 'حدث خطأ أثناء محاولة جلب معلومات الاشتراك للمستخدم',
        'details': e.toString(),
      };
    }
  }

  /// جلب المستخدمين (باستخدام autocomplete) - متوافق مع الوثائق
  Future<Map<String, dynamic>> getUsers({String query = ''}) async {
    if (!isLoggedIn) {
      return {
        'success': false,
        'error': 'User not logged in',
      };
    }
    
    // Check for rate limiting before making request
    if (await isRateLimited()) {
      print('[EarthlinkService] Currently rate limited, waiting before request...');
      await Future.delayed(Duration(seconds: 5));
    }

    final url = Uri.parse('${_baseUrl}user/autocomplete?key=$query');
    print('[EarthlinkService] Sending GET request to: $url');

    try {
      final response = await http.get(
        url,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $_token',
        },
      );

      print('[EarthlinkService] Get users response code: ${response.statusCode}');
      print('[EarthlinkService] Get users response body: ${response.body}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': json.decode(response.body),
        };
      } else {
        return {
          'success': false,
          'error': 'Failed to get users: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      print('[EarthlinkService] Error getting users: $e');
      return {
        'success': false,
        'error': 'An error occurred while trying to get users',
        'details': e.toString(),
      };
    }
  }
}