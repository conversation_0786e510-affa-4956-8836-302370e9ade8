import 'dart:convert';
import 'dart:async';
import 'package:router_os_client/router_os_client.dart';
import '../models/package_model.dart';
import '../models/sas_server_model.dart';
import '../models/mikrotik_user_model.dart';

// Custom RouterOSClient wrapper to handle UTF-8 decoding issues
class SafeRouterOSClient {
  final RouterOSClient _client;
  
  SafeRouterOSClient(this._client);
  
  Future<bool> login() async {
    try {
      return await _client.login();
    } catch (e) {
      print('[SafeRouterOSClient] Login failed: $e');
      rethrow;
    }
  }
  
  Future<List<Map<String, dynamic>>> talk(List<String> commands) async {
    int retryCount = 0;
    const maxRetries = 3;
    
    while (retryCount < maxRetries) {
      try {
        return await _client.talk(commands);
      } on FormatException catch (e) {
        retryCount++;
        print('[SafeRouterOSClient] UTF-8 error detected (attempt $retryCount/$maxRetries): $e');
        
        if (retryCount >= maxRetries) {
          print('[SafeRouterOSClient] Max retries reached, throwing error');
          throw Exception('Failed to decode MikroTik response after $maxRetries attempts: $e');
        }
        
        // Wait before retrying
        await Future.delayed(Duration(milliseconds: 100 * retryCount));
        
        // Try to recover by using a different approach
        if (retryCount == 1) {
          // First retry: try with a simplified command
          if (commands.length > 1) {
            print('[SafeRouterOSClient] Trying with simplified command');
            final simplified = [commands.first];
            return await _client.talk(simplified);
          }
        } else if (retryCount == 2) {
          // Second retry: try with a longer delay
          await Future.delayed(const Duration(milliseconds: 500));
          return await _client.talk(commands);
        }
      } catch (e) {
        print('[SafeRouterOSClient] Non-format error during talk: $e');
        rethrow;
      }
    }
    
    // Should not reach here
    throw Exception('Failed to complete MikroTik talk operation after $maxRetries attempts.');
  }
  

  
  void close() => _client.close();
}

class MikrotikSyncService {
  final SasServerModel server;

  MikrotikSyncService(this.server);

  // Helper method to manage MikroTik client connection and disconnection with retry
  Future<T> _withMikrotikClient<T>(Future<T> Function(SafeRouterOSClient client) action, {int retryCount = 3, Duration delay = const Duration(seconds: 2)}) async {
    RouterOSClient? client;
    SafeRouterOSClient? safeClient;
    int attempts = 0;
    
    while (attempts < retryCount) {
      attempts++;
      try {
        print('[MikrotikSyncService] Attempt $attempts of $retryCount to connect to MikroTik device: ${server.host}');
        
        client = RouterOSClient(
          address: server.host,
          user: server.username,
          password: server.password,
          port: 8728, // Default MikroTik API port
          useSsl: false,
        );
        
        // Wrap the client in our SafeRouterOSClient
        safeClient = SafeRouterOSClient(client);
        
        final bool loggedIn = await safeClient.login();
        if (!loggedIn) {
          throw Exception('Login failed to MikroTik device: ${server.host}');
        }
        
        print('[MikrotikSyncService] Successfully connected to MikroTik device: ${server.host}');
        
        // Use the safe client in the action
        return await action(safeClient);
      } on FormatException catch (e) { // Catch FormatException specifically
        print('[MikrotikSyncService] FormatException during MikroTik operation (Attempt $attempts/$retryCount): $e');
        
        // If we encounter UTF-8 errors, increase retry count
        if (e.toString().contains('Unexpected extension byte')) {
          print('[MikrotikSyncService] UTF-8 decoding error detected, will retry with different strategy');
          
          // For UTF-8 errors, use a shorter delay before retry
          await Future.delayed(const Duration(milliseconds: 500));
        } else {
          // For other FormatExceptions, use the standard delay
          if (attempts < retryCount) {
            await Future.delayed(delay);
          }
        }
        
        if (attempts == retryCount) {
          throw Exception('Failed to decode MikroTik response after $retryCount attempts: $e');
        }
      } catch (e) { // Catch other exceptions
        print('[MikrotikSyncService] Error during MikroTik operation (Attempt $attempts/$retryCount): $e');
        
        if (attempts == retryCount) {
          rethrow; // Re-throw the original exception after all retries
        }
        
        await Future.delayed(delay); // Wait before retrying
      } finally {
        safeClient?.close();
        client?.close();
      }
    }
    
    throw Exception('Failed to complete MikroTik operation after $retryCount attempts.');
  }

  // Fetches PPP secrets (users) from the router
  Future<List<MikrotikUser>> fetchPppSecrets() async {
    return _withMikrotikClient((safeClient) async {
      print('[MikrotikSyncService] Fetching PPP secrets using a robust one-by-one method...');
      
      // Step 1: Fetch only the .id of all secrets. This is a safe and fast operation.
      print('[MikrotikSyncService] Step 1: Fetching all secret IDs...');
      final idResponse = await safeClient.talk([
        '/ppp/secret/print',
        '=.proplist=.id',
      ]);
      final secretIds = idResponse.map((r) => r['.id'] as String).toList();
      print('[MikrotikSyncService] Found ${secretIds.length} secrets. Now fetching details for each.');

      final List<MikrotikUser> validUsers = [];
      int successCount = 0;
      int failedCount = 0;

      // Step 2: Loop through each ID and fetch the full details for that secret.
      // This is slower but more reliable, as an error with one user won't stop the whole process.
      for (final id in secretIds) {
        try {
          final userResponse = await safeClient.talk([
            '/ppp/secret/print',
            '=.proplist=.id,name,profile,comment,password',
            '?.id=$id',
          ]);

          if (userResponse.isNotEmpty) {
            // Ensure the data is a Map before processing
            final data = userResponse.first;
            if (data is Map<String, dynamic>) {
              validUsers.add(MikrotikUser.fromMap(data));
              successCount++;
            } else {
              print('[MikrotikSyncService] Skipping non-Map data for ID $id: $data');
              failedCount++;
            }
          } else {
            print('[MikrotikSyncService] Warning: Could not fetch details for secret with ID: $id');
            failedCount++;
          }
        } catch (e) {
          print('[MikrotikSyncService] Error fetching details for secret ID $id: $e. Skipping.');
          failedCount++;
        }
      }

      print('[MikrotikSyncService] Finished fetching details. Success: $successCount, Failed: $failedCount.');
      return validUsers;
    });
  }

  // Fetches PPP profiles from the router
  Future<List<PackageModel>> fetchPppProfiles() async {
    return _withMikrotikClient((safeClient) async {
      print('[MikrotikSyncService] Executing /ppp/profile/print...');
      
      try {
        // Use .proplist to specify which fields to return without pagination
        final response = await safeClient.talk([
          '/ppp/profile/print', 
          '=.proplist=.id,name'
        ]);
        print('[MikrotikSyncService] RAW PPP Profiles Response (count: ${response.length}): $response');
        
        if (response.isEmpty) {
          print('[MikrotikSyncService] No PPP profiles found on the router.');
          return [];
        }
        
        final List<PackageModel> packages = [];
        int skippedCount = 0;

        for (final data in response) {
          try {
            // Ensure the data is a Map before processing
            if (data is! Map<String, dynamic>) {
              print('[MikrotikSyncService] Skipping non-Map profile data: $data');
              skippedCount++;
              continue;
            }
            
            final name = data['name'];
            if (name != null && name is String && name.isNotEmpty) {
              // Try to extract speed from name if it contains a hyphen
              String speed = name.split('-').first;
              if (speed.isEmpty) {
                speed = 'غير محدد'; // Use "unspecified" if empty
              }
              
              packages.add(PackageModel(
                id: 'mikrotik-' + name, // Generating a unique ID
                name: name,
                price: 0, // Price is not available from PPP profiles
                serverId: server.id ?? '',
                adminId: server.adminId,
                durationInDays: 30, // Default duration
                speed: speed,
                deviceCount: 1, // Default device count
                createdAt: DateTime.now(),
                mikrotikProfileName: name,
              ));
            } else {
              print('[MikrotikSyncService] Warning: Skipping profile with invalid or null name: $data');
              skippedCount++;
            }
          } catch (e) {
            print('[MikrotikSyncService] Error processing profile data: $data, Error: $e');
            skippedCount++;
          }
        }
        
        if (skippedCount > 0) {
          print('[MikrotikSyncService] Successfully parsed ${packages.length} profiles, skipped $skippedCount malformed entries.');
        } else {
          print('[MikrotikSyncService] Successfully parsed all ${packages.length} profiles.');
        }
        
        return packages;
      } catch (e) {
        print('[MikrotikSyncService] Error fetching PPP profiles: $e');
        rethrow;
      }
    });
  }

  String generateExpirationScript(String targetProfile, List<String> activeProfiles) {
    // This script checks for expired users and moves them to the target profile.
    // It supports both RouterOS v6/v7 date formats based on the provided script.
    return '''
:local currentVersion [/system resource get version];
:local versionNumber [:pick \$currentVersion 0 [:find \$currentVersion " "]];
:if (\$versionNumber > 7.9) do={
    :log info "Running RouterOS version 7.10 or higher";
    :local currentDate [/system clock get date];
    :local currentTime [/system clock get time];
    :local currentYear [:pick \$currentDate 0 4];
    :local currentMonth [:pick \$currentDate 5 7];
    :local currentDay [:pick \$currentDate 8 10];
    :local currentDateInt [:tonum ("\$currentYear\$currentMonth\$currentDay")];
    :local currentHour [:tonum [:pick \$currentTime 0 2]];
    :local currentMinute [:tonum [:pick \$currentTime 3 5]];
    :local currentTimeInt (\$currentHour*60+\$currentMinute);
    :local searchProfile "default";
    :local targetProfile "$targetProfile";
    :foreach i in=[/ppp secret find where profile=\$searchProfile] do={
        :local comment [/ppp secret get \$i comment];
        :local name2 [/ppp secret get \$i name];
        :if ([:len \$comment]>=10) do={
            :local expYear [:pick \$comment 0 4];
            :local expMonth [:pick \$comment 5 7];
            :local expDay [:pick \$comment 8 10];
            :if ([:tonum \$expYear]>0&&[:tonum \$expMonth]>0&&[:tonum \$expDay]>0) do={
                :local expDateInt [:tonum ("\$expYear\$expMonth\$expDay")];
                :local expTime [:pick \$comment 11 19];
                :local expHour [:tonum [:pick \$expTime 0 2]];
                :local expMinute [:tonum [:pick \$expTime 3 5]];
                :local expTimeInt (\$expHour*60+\$expMinute);
                :if (\$expDateInt<\$currentDateInt) do={
                    :log warning ("User \$name has an expired date: \$comment");
                    /ppp secret set \$i profile=\$targetProfile;
                    /ppp active remove [find where name=\$name2];
                } else={
                    :if (\$expDateInt=\$currentDateInt&&\$expTimeInt<=\$currentTimeInt) do={
                        :log warning ("User \$name has a due date: \$comment");
                        /ppp secret set \$i profile=\$targetProfile;
                        /ppp active remove [find where name=\$name2];
                    }
                }
            }
        }
    }
} else={
    :log info message="Current version is \$currentVersion, which is 7.9 or lower";
    :local searchProfile "default";
    :local targetProfile "$targetProfile";
    :local dateint do={
        :local montharray ("jan","feb","mar","apr","may","jun","jul","aug","sep","oct","nov","dec");
        :local days [:pick \$d 4 6];
        :local month [:pick \$d 0 3];
        :local year [:pick \$d 7 11];
        :local monthint ([:find \$montharray \$month]);
        :local month (\$monthint + 1);
        :if ([:len \$month] = 1) do={
            :local zero "0";
            :return [:tonum ("\$year\$zero\$month\$days")];
        } else={
            :return [:tonum ("\$year\$month\$days")];
        }
    };
    :local timeint do={
        :local hours [:pick \$t 0 2];
        :local minutes [:pick \$t 3 5];
        :return (\$hours * 60 + \$minutes);
    };
    :local date [/system clock get date];
    :local time [/system clock get time];
    :local today [\$dateint d=\$date];
    :local curtime [\$timeint t=\$time];
    :foreach i in=[/ppp secret find where profile=\$searchProfile] do={
        :local comment [/ppp secret get \$i comment];
        :local name1 [/ppp secret get \$i name];
        :local gettime [:pick \$comment 12 20];
        :if ([:pick \$comment 3] = "/" && [:pick \$comment 6] = "/") do={
            :local expd [\$dateint d=\$comment];
            :local expt [\$timeint t=\$gettime];
            :if ((\$expd < \$today && \$expt < \$curtime) || (\$expd < \$today && \$expt > \$curtime) || (\$expd = \$today && \$expt < \$curtime)) do={
                /ppp secret set \$i profile=\$targetProfile;
                /ppp active remove [find where name=\$name1];
            }
        }
    }
}
''';
  }

  Future<void> createOrUpdateExpirationScheduler({
    required String targetProfile,
    required List<String> activeProfiles,
  }) async {
    return _withMikrotikClient((client) async {
      print('[MikrotikSyncService] Creating/Updating Expiration Scheduler...');
      
      // 1. Build the script string
      final String script = generateExpirationScript(targetProfile, activeProfiles);
      print('[MikrotikSyncService] Generated Scheduler Script: $script');

      // 2. Find existing scheduler
      final findResult = await client.talk(['/system/scheduler/print', '?name=ExpirationChecker']);

      // 3. Remove existing scheduler if found
      if (findResult.isNotEmpty && findResult.first is Map<String, dynamic>) {
        final schedulerId = findResult.first['.id'];
        if (schedulerId != null) {
          print('[MikrotikSyncService] Found existing scheduler with ID: $schedulerId. Removing it.');
          await client.talk(['/system/scheduler/remove', '=.id=$schedulerId']);
        }
      } else {
        print('[MikrotikSyncService] No existing scheduler found with name ExpirationChecker.');
      }

      // 4. Add the new scheduler
      print('[MikrotikSyncService] Adding new scheduler...');
      await client.talk([
        '/system/scheduler/add',
        '=name=ExpirationChecker',
        '=interval=5m', // Run every 5 minutes
        '=policy=read,write,test,policy',
        '=on-event=$script',
        '=start-time=startup',
      ]);
      
      print('[MikrotikSyncService] Successfully created/updated Expiration Scheduler.');
    });
  }

  // Adds a new PPP profile to the router
  Future<void> addPppProfile({
    required String name,
    required String rateLimit,
  }) async {
    return _withMikrotikClient((safeClient) async {
      print('[MikrotikSyncService] Adding PPP profile: $name');

      final command = [
        '/ppp/profile/add',
        '=name=$name',
        '=rate-limit=$rateLimit',
      ];

      await safeClient.talk(command);

      print('[MikrotikSyncService] Successfully created PPP profile: $name');
    });
  }

  // Adds a new PPP secret to the router
  Future<void> addPppSecret({
    required String name,
    required String password,
    required String profile,
    required String comment,
    String service = 'pppoe',
  }) async {
    return _withMikrotikClient((safeClient) async {
      print('[MikrotikSyncService] Adding PPP secret for user: $name');

      final command = [
        '/ppp/secret/add',
        '=name=$name',
        '=password=$password',
        '=profile=$profile',
        '=comment=$comment',
        '=service=$service',
      ];

      await safeClient.talk(command);

      print('[MikrotikSyncService] Successfully sent add PPP secret command for user: $name');
    });
  }

  // Updates an existing PPP secret
  Future<void> updatePppSecret({
    required String username,
    required String newProfile,
    required String newComment,
  }) async {
    return _withMikrotikClient((safeClient) async {
      print('[MikrotikSyncService] Updating PPP secret for user: $username');

      // Find the user first
      final findResult = await safeClient.talk(['/ppp/secret/print', '?name=$username']);

      if (findResult.isEmpty) {
        throw Exception('User $username not found in MikroTik');
      }

      final userId = findResult.first['.id'];
      if (userId == null) {
        throw Exception('Could not get user ID for $username');
      }

      final command = [
        '/ppp/secret/set',
        '=.id=$userId',
        '=profile=$newProfile',
        '=comment=$newComment',
      ];

      await safeClient.talk(command);

      print('[MikrotikSyncService] Successfully updated PPP secret for user: $username');
    });
  }

  // Renews a PPP secret on the router
  Future<void> renewPppSecret({
    required String username,
    required String newProfile,
    required String newComment,
  }) async {
    return _withMikrotikClient((safeClient) async {
      print('[MikrotikSyncService] Renewing PPP secret for user: $username');

      // 1. Find the user's secret to get its ID. 
      // We fetch all properties to ensure compatibility and avoid potential issues with `.proplist`.
      final findResponse = await safeClient.talk([
        '/ppp/secret/print',
        '?name=$username',
      ]);

      if (findResponse.isEmpty) {
        throw Exception('المستخدم "$username" غير موجود في المايكروتك.');
      }
      final secretId = findResponse.first['.id'];

      // 2. Update the secret with the new profile and comment
      await safeClient.talk([
        '/ppp/secret/set',
        '=.id=$secretId',
        '=profile=$newProfile',
        '=comment=$newComment',
      ]);
      print('[MikrotikSyncService] Successfully updated secret for $username.');

      // 3. Find the active connection for the user and remove it
      final findActiveResponse = await safeClient.talk([
        '/ppp/active/print',
        '?.proplist=.id',
        '?name=$username',
      ]);

      if (findActiveResponse.isNotEmpty) {
        final activeId = findActiveResponse.first['.id'];
        await safeClient.talk([
          '/ppp/active/remove',
          '=.id=$activeId',
        ]);
        print('[MikrotikSyncService] Successfully kicked active connection for $username.');
      } else {
        print('[MikrotikSyncService] No active connection found for $username to kick.');
      }
    });
  }

  // Removes a PPP secret from the router
  Future<void> removePppSecret(String username) async {
    return _withMikrotikClient((safeClient) async {
      print('[MikrotikSyncService] Removing PPP secret for user: $username');

      // Find the user first
      final findResult = await safeClient.talk(['/ppp/secret/print', '?name=$username']);

      if (findResult.isEmpty) {
        print('[MikrotikSyncService] User $username not found, nothing to remove');
        return;
      }

      final userId = findResult.first['.id'];
      if (userId == null) {
        throw Exception('Could not get user ID for $username');
      }

      // Remove the secret
      await safeClient.talk(['/ppp/secret/remove', '=.id=$userId']);

      // Also remove any active connection
      final findActiveResponse = await safeClient.talk([
        '/ppp/active/print',
        '?.proplist=.id',
        '?name=$username',
      ]);

      if (findActiveResponse.isNotEmpty) {
        final activeId = findActiveResponse.first['.id'];
        await safeClient.talk(['/ppp/active/remove', '=.id=$activeId']);
        print('[MikrotikSyncService] Also removed active connection for $username');
      }

      print('[MikrotikSyncService] Successfully removed PPP secret for user: $username');
    });
  }

  // Updates password for an existing PPP secret
  Future<void> updatePppSecretPassword({
    required String username,
    required String newPassword,
  }) async {
    return _withMikrotikClient((safeClient) async {
      print('[MikrotikSyncService] Updating password for user: $username');

      // Find the user first
      final findResult = await safeClient.talk(['/ppp/secret/print', '?name=$username']);

      if (findResult.isEmpty) {
        throw Exception('User $username not found in MikroTik');
      }

      final userId = findResult.first['.id'];
      if (userId == null) {
        throw Exception('Could not get user ID for $username');
      }

      // Update only the password
      await safeClient.talk([
        '/ppp/secret/set',
        '=.id=$userId',
        '=password=$newPassword',
      ]);

      print('[MikrotikSyncService] Successfully updated password for user: $username');
    });
  }

  // Fetches a single active PPP session for a given username
  Future<Map<String, dynamic>?> getActivePppSecret(String username) async {
    return _withMikrotikClient((safeClient) async {
      print('[MikrotikSyncService] Fetching active session for user: $username');

      final findActiveResponse = await safeClient.talk([
        '/ppp/active/print',
        '?name=$username',
      ]);

      if (findActiveResponse.isNotEmpty) {
        print('[MikrotikSyncService] Found active session for $username.');
        return findActiveResponse.first as Map<String, dynamic>;
      } else {
        print('[MikrotikSyncService] No active session found for $username.');
        return null;
      }
    });
  }

  // Fetches detailed PPP session information for a given username
  Future<Map<String, dynamic>?> getDetailedPppSession(String username) async {
    return _withMikrotikClient((safeClient) async {
      print('[MikrotikSyncService] Fetching detailed PPP session for user: $username');

      try {
        // First, get the PPP secret details
        final secretResponse = await safeClient.talk([
          '/ppp/secret/print',
          '?name=$username',
        ]);

        Map<String, dynamic> sessionDetails = {};

        // Add secret information if found
        if (secretResponse.isNotEmpty) {
          final secret = secretResponse.first as Map<String, dynamic>;
          sessionDetails['secret'] = {
            'name': secret['name'] ?? username,
            'profile': secret['profile'] ?? 'غير محدد',
            'comment': secret['comment'] ?? 'لا توجد ملاحظات',
            'password': secret['password'] ?? 'غير محدد',
            'service': secret['service'] ?? 'pppoe',
            'disabled': secret['disabled'] ?? 'false',
          };
          print('[MikrotikSyncService] Found PPP secret details for $username');
        } else {
          print('[MikrotikSyncService] No PPP secret found for $username');
          sessionDetails['secret'] = null;
        }

        // Then, get active session details if user is online
        final activeResponse = await safeClient.talk([
          '/ppp/active/print',
          '?name=$username',
        ]);

        if (activeResponse.isNotEmpty) {
          final activeSession = activeResponse.first as Map<String, dynamic>;
          sessionDetails['active'] = {
            'name': activeSession['name'] ?? username,
            'address': activeSession['address'] ?? 'غير محدد',
            'uptime': activeSession['uptime'] ?? 'غير محدد',
            'encoding': activeSession['encoding'] ?? 'غير محدد',
            'session-id': activeSession['session-id'] ?? 'غير محدد',
            'radius': activeSession['radius'] ?? 'false',
            'service': activeSession['service'] ?? 'pppoe',
            'caller-id': activeSession['caller-id'] ?? 'غير محدد',
            'bytes-in': activeSession['bytes-in'] ?? '0',
            'bytes-out': activeSession['bytes-out'] ?? '0',
            'packets-in': activeSession['packets-in'] ?? '0',
            'packets-out': activeSession['packets-out'] ?? '0',
            'limit-bytes-in': activeSession['limit-bytes-in'] ?? 'غير محدود',
            'limit-bytes-out': activeSession['limit-bytes-out'] ?? 'غير محدود',
          };
          sessionDetails['isOnline'] = true;
          print('[MikrotikSyncService] Found active session details for $username');
        } else {
          sessionDetails['active'] = null;
          sessionDetails['isOnline'] = false;
          print('[MikrotikSyncService] User $username is not currently online');
        }

        // Get profile details if profile name is available
        if (sessionDetails['secret'] != null && sessionDetails['secret']['profile'] != null) {
          final profileName = sessionDetails['secret']['profile'];
          try {
            final profileResponse = await safeClient.talk([
              '/ppp/profile/print',
              '?name=$profileName',
            ]);

            if (profileResponse.isNotEmpty) {
              final profile = profileResponse.first as Map<String, dynamic>;
              sessionDetails['profile'] = {
                'name': profile['name'] ?? profileName,
                'rate-limit': profile['rate-limit'] ?? 'غير محدود',
                'session-timeout': profile['session-timeout'] ?? 'غير محدود',
                'idle-timeout': profile['idle-timeout'] ?? 'غير محدود',
                'keepalive-timeout': profile['keepalive-timeout'] ?? 'غير محدود',
                'address-list': profile['address-list'] ?? 'غير محدد',
                'dns-server': profile['dns-server'] ?? 'غير محدد',
                'wins-server': profile['wins-server'] ?? 'غير محدد',
              };
              print('[MikrotikSyncService] Found profile details for $profileName');
            } else {
              sessionDetails['profile'] = null;
              print('[MikrotikSyncService] Profile $profileName not found');
            }
          } catch (e) {
            print('[MikrotikSyncService] Error fetching profile details: $e');
            sessionDetails['profile'] = null;
          }
        } else {
          sessionDetails['profile'] = null;
        }

        return sessionDetails;
      } catch (e) {
        print('[MikrotikSyncService] Error fetching detailed PPP session: $e');
        return null;
      }
    });
  }
}
