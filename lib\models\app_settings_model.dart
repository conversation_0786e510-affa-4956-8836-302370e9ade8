// App Settings Model for Currency and Country Configuration
class AppSettings {
  final String currencyCode;
  final String currencySymbol;
  final String currencyName;
  final String countryCode;
  final String countryName;
  final String countryFlag;
  final String phoneCode;

  AppSettings({
    required this.currencyCode,
    required this.currencySymbol,
    required this.currencyName,
    required this.countryCode,
    required this.countryName,
    required this.countryFlag,
    required this.phoneCode,
  });

  // Default settings (Iraq)
  factory AppSettings.defaultSettings() {
    return AppSettings(
      currencyCode: 'IQD',
      currencySymbol: 'د.ع',
      currencyName: 'Iraqi Dinar',
      countryCode: 'IQ',
      countryName: 'العراق',
      countryFlag: '🇮🇶',
      phoneCode: '+964',
    );
  }

  // Convert to Map for SharedPreferences
  Map<String, dynamic> toMap() {
    return {
      'currencyCode': currencyCode,
      'currencySymbol': currencySymbol,
      'currencyName': currencyName,
      'countryCode': countryCode,
      'countryName': countryName,
      'countryFlag': countryFlag,
      'phoneCode': phoneCode,
    };
  }

  // Create from Map
  factory AppSettings.fromMap(Map<String, dynamic> map) {
    return AppSettings(
      currencyCode: map['currencyCode'] ?? 'IQD',
      currencySymbol: map['currencySymbol'] ?? 'د.ع',
      currencyName: map['currencyName'] ?? 'Iraqi Dinar',
      countryCode: map['countryCode'] ?? 'IQ',
      countryName: map['countryName'] ?? 'العراق',
      countryFlag: map['countryFlag'] ?? '🇮🇶',
      phoneCode: map['phoneCode'] ?? '+964',
    );
  }

  // Copy with method for updates
  AppSettings copyWith({
    String? currencyCode,
    String? currencySymbol,
    String? currencyName,
    String? countryCode,
    String? countryName,
    String? countryFlag,
    String? phoneCode,
  }) {
    return AppSettings(
      currencyCode: currencyCode ?? this.currencyCode,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      currencyName: currencyName ?? this.currencyName,
      countryCode: countryCode ?? this.countryCode,
      countryName: countryName ?? this.countryName,
      countryFlag: countryFlag ?? this.countryFlag,
      phoneCode: phoneCode ?? this.phoneCode,
    );
  }
}
