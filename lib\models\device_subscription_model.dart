import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';

part 'device_subscription_model.g.dart';

@JsonSerializable()
class DeviceSubscription {
  final String id;
  @Json<PERSON>ey(name: 'created_at')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'device_id')
  final String deviceId;
  @<PERSON>son<PERSON>ey(name: 'device_name')
  final String? deviceName;
  @<PERSON>son<PERSON><PERSON>(name: 'device_model')
  final String? deviceModel;
  @<PERSON>son<PERSON>ey(name: 'device_brand')
  final String? deviceBrand;
  @JsonKey(name: 'android_version')
  final String? androidVersion;
  @Json<PERSON>ey(name: 'app_version')
  final String? appVersion;
  @Json<PERSON>ey(name: 'account_number')
  final String accountNumber;
  @<PERSON>son<PERSON>ey(name: 'subscription_start_date')
  final DateTime subscriptionStartDate;
  @<PERSON>sonKey(name: 'subscription_end_date')
  final DateTime subscriptionEndDate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active', fromJson: _boolFromJson)
  final bool isActive;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_access')
  final DateTime lastAccess;
  @<PERSON>son<PERSON><PERSON>(name: 'access_count')
  final int accessCount;
  @JsonKey(name: 'user_email')
  final String? userEmail;
  final String? notes;

  DeviceSubscription({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.deviceId,
    this.deviceName,
    this.deviceModel,
    this.deviceBrand,
    this.androidVersion,
    this.appVersion,
    required this.accountNumber,
    required this.subscriptionStartDate,
    required this.subscriptionEndDate,
    required this.isActive,
    required this.lastAccess,
    required this.accessCount,
    this.userEmail,
    this.notes,
  });

  factory DeviceSubscription.fromJson(Map<String, dynamic> json) =>
      _$DeviceSubscriptionFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceSubscriptionToJson(this);

  DeviceSubscription copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? deviceId,
    String? deviceName,
    String? deviceModel,
    String? deviceBrand,
    String? androidVersion,
    String? appVersion,
    String? accountNumber,
    DateTime? subscriptionStartDate,
    DateTime? subscriptionEndDate,
    bool? isActive,
    DateTime? lastAccess,
    int? accessCount,
    String? userEmail,
    String? notes,
  }) {
    return DeviceSubscription(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deviceId: deviceId ?? this.deviceId,
      deviceName: deviceName ?? this.deviceName,
      deviceModel: deviceModel ?? this.deviceModel,
      deviceBrand: deviceBrand ?? this.deviceBrand,
      androidVersion: androidVersion ?? this.androidVersion,
      appVersion: appVersion ?? this.appVersion,
      accountNumber: accountNumber ?? this.accountNumber,
      subscriptionStartDate:
          subscriptionStartDate ?? this.subscriptionStartDate,
      subscriptionEndDate: subscriptionEndDate ?? this.subscriptionEndDate,
      isActive: isActive ?? this.isActive,
      lastAccess: lastAccess ?? this.lastAccess,
      accessCount: accessCount ?? this.accessCount,
      userEmail: userEmail ?? this.userEmail,
      notes: notes ?? this.notes,
    );
  }

  // Helper methods for UI display
  
  /// Returns the number of days remaining until subscription expires
  int get daysRemaining {
    final now = DateTime.now();
    if (subscriptionEndDate.isBefore(now)) return 0;
    return subscriptionEndDate.difference(now).inDays;
  }

  /// Returns a user-friendly status text
  String get statusText {
    if (!isActive) return 'غير فعال';
    
    final now = DateTime.now();
    if (subscriptionEndDate.isBefore(now)) {
      return 'منتهي الصلاحية';
    }
    
    final days = daysRemaining;
    if (days == 0) {
      return 'ينتهي اليوم';
    } else if (days == 1) {
      return 'ينتهي غداً';
    } else if (days <= 7) {
      return 'ينتهي خلال $days أيام';
    } else if (days <= 30) {
      return 'ينتهي خلال $days يوم';
    } else {
      return 'فعال';
    }
  }

  /// Returns appropriate color for status display
  get statusColor {
    if (!isActive) return Colors.grey;
    
    final days = daysRemaining;
    if (days == 0) return Colors.red;
    if (days <= 3) return Colors.red;
    if (days <= 7) return Colors.orange;
    if (days <= 30) return Colors.yellow;
    return Colors.green;
  }

  /// Checks if subscription is valid (active and not expired)
  bool get isValid {
    return isActive && subscriptionEndDate.isAfter(DateTime.now());
  }

  /// Checks if subscription is expired
  bool get isExpired {
    return !isActive || subscriptionEndDate.isBefore(DateTime.now()) || subscriptionEndDate.isAtSameMomentAs(DateTime.now());
  }

  /// Checks if subscription is expiring soon (within 3 days)
  bool get isExpiringSoon {
    final now = DateTime.now();
    final days = subscriptionEndDate.difference(now).inDays;
    return isActive && days >= 0 && days <= 3;
  }

  /// Returns formatted subscription end date
  String get formattedEndDate {
    return '${subscriptionEndDate.day}/${subscriptionEndDate.month}/${subscriptionEndDate.year}';
  }

  static bool _boolFromJson(dynamic value) {
    if (value is bool) return value;
    if (value is String) return value.toLowerCase() == 'true';
    if (value is int) return value == 1;
    return false;
  }
}
