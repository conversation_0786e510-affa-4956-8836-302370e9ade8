import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/zaincash_service.dart';
import '../config/zaincash_config.dart';

class ZainCashOTPPaymentPage extends StatefulWidget {
  final String packageId;
  final String accountNumber;
  final double amount;
  final String packageName;
  final int durationDays;

  const ZainCashOTPPaymentPage({
    super.key,
    required this.packageId,
    required this.accountNumber,
    required this.amount,
    required this.packageName,
    required this.durationDays,
  });

  @override
  State<ZainCashOTPPaymentPage> createState() => _ZainCashOTPPaymentPageState();
}

class _ZainCashOTPPaymentPageState extends State<ZainCashOTPPaymentPage> {
  final ZainCashService _zainCashService = ZainCashService();
  final _formKey = GlobalKey<FormState>();
  
  // Controllers
  final _phoneController = TextEditingController();
  final _pinController = TextEditingController();
  final _otpController = TextEditingController();
  
  // State variables
  String? _transactionId;
  bool _isLoading = false;
  String _currentStep = 'create'; // create, process, complete, success
  String _statusMessage = '';
  bool _isError = false;

  @override
  void initState() {
    super.initState();
    // تعبئة بيانات الاختبار حسب البيئة
    _phoneController.text = ZainCashConfig.customerMsisdn;
    _pinController.text = ZainCashConfig.customerPin;
    _otpController.text = ZainCashConfig.customerOtp;
    _createPaymentRequest();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _pinController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'دفع عبر زين كاش',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.orange[400]!, Colors.orange[600]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.grey[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // معلومات الدفع
                    _buildPaymentInfo(),
                    const SizedBox(height: 16),

                    // مؤشر التقدم
                    _buildProgressIndicator(),
                    const SizedBox(height: 16),

                    // رسالة الحالة
                    if (_statusMessage.isNotEmpty) ...[
                      _buildStatusMessage(),
                      const SizedBox(height: 12),
                    ],

                    // محتوى الخطوة الحالية
                    _buildCurrentStepContent(),

                    // أزرار التحكم
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[50]!, Colors.blue[100]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(color: Colors.blue[200]!, width: 1.5),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.15),
            blurRadius: 15,
            offset: const Offset(0, 5),
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue[600]!, Colors.blue[700]!],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.receipt_long,
                  color: Colors.white,
                  size: 26,
                ),
              ),
              const SizedBox(width: 14),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تفاصيل الدفع',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[800],
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'مراجعة بيانات العملية',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.blue[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // تفاصيل الدفع في شبكة
          Container(
            padding: const EdgeInsets.all(18),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(14),
              border: Border.all(color: Colors.blue[100]!, width: 1),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.08),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildInfoRow('الباقة', widget.packageName, Icons.inventory_2, Colors.purple),
                const Divider(height: 24, thickness: 1),
                _buildInfoRow('رقم الحساب', widget.accountNumber, Icons.account_circle, Colors.indigo),
                const Divider(height: 24, thickness: 1),
                _buildInfoRow('المبلغ', '${widget.amount.toStringAsFixed(0)} د.ع', Icons.payments, Colors.green),
                const Divider(height: 24, thickness: 1),
                _buildInfoRow('المدة', '${widget.durationDays} يوم', Icons.schedule, Colors.orange),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon, [Color? iconColor]) {
    final color = iconColor ?? Colors.blue[600]!;
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: color.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Icon(
            icon,
            size: 20,
            color: color,
          ),
        ),
        const SizedBox(width: 14),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.2,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  height: 1.2,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator() {
    final steps = [
      {'title': 'إنشاء الطلب', 'icon': Icons.create},
      {'title': 'إدخال البيانات', 'icon': Icons.edit},
      {'title': 'تأكيد OTP', 'icon': Icons.sms},
      {'title': 'مكتمل', 'icon': Icons.check_circle},
    ];
    final currentIndex = _getCurrentStepIndex();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: IntrinsicHeight(
        child: Row(
          children: steps.asMap().entries.map((entry) {
            final index = entry.key;
            final step = entry.value;
            final isActive = index <= currentIndex;
            final isCurrent = index == currentIndex;
            final isCompleted = index < currentIndex;

            return Expanded(
              child: Column(
                children: [
                  // الخط الواصل
                  if (index > 0)
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4.0),
                        height: 2,
                        color: isActive ? Colors.orange[400] : Colors.grey[300],
                      ),
                    ),

                  // الدائرة والأيقونة
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: isActive
                        ? LinearGradient(
                            colors: isCompleted
                              ? [Colors.green[400]!, Colors.green[600]!]
                              : [Colors.orange[400]!, Colors.orange[600]!],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          )
                        : null,
                      color: !isActive ? Colors.grey[300] : null,
                      boxShadow: isActive ? [
                        BoxShadow(
                          color: (isCompleted ? Colors.green : Colors.orange).withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Icon(
                      isCompleted ? Icons.check : step['icon'] as IconData,
                      color: isActive ? Colors.white : Colors.grey[600],
                      size: isCurrent ? 28 : 24,
                    ),
                  ),
                  const SizedBox(height: 8),

                  // النص
                  Text(
                    step['title'] as String,
                    style: TextStyle(
                      fontSize: 12,
                      color: isCurrent
                        ? (isCompleted ? Colors.green[700] : Colors.orange[700])
                        : Colors.grey[600],
                      fontWeight: isCurrent ? FontWeight.bold : FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildStatusMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: _isError
              ? [Colors.red[50]!, Colors.red[100]!]
              : [Colors.green[50]!, Colors.green[100]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isError ? Colors.red[200]! : Colors.green[200]!,
        ),
        boxShadow: [
          BoxShadow(
            color: (_isError ? Colors.red : Colors.green).withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _isError ? Colors.red[600] : Colors.green[600],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _isError ? Icons.error_outline : Icons.info_outline,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _statusMessage,
              style: TextStyle(
                color: _isError ? Colors.red[800] : Colors.green[800],
                fontSize: 15,
                fontWeight: FontWeight.w600,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentStepContent() {
    switch (_currentStep) {
      case 'create':
        return _buildCreateStep();
      case 'process':
        return _buildProcessStep();
      case 'complete':
        return _buildCompleteStep();
      case 'success':
        return _buildSuccessStep();
      default:
        return const SizedBox();
    }
  }

  Widget _buildCreateStep() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري إنشاء طلب الدفع...'),
        ],
      ),
    );
  }

  Widget _buildProcessStep() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Header مبسط
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.orange[400]!, Colors.orange[600]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.95),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.15),
                        blurRadius: 6,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Image.asset(
                    'assets/zain-cash-seeklogo.png',
                    width: 44,
                    height: 44,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.account_balance_wallet,
                        size: 44,
                        color: Colors.orange[600],
                      );
                    },
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'بيانات محفظة زين كاش',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // حقل رقم الهاتف
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextFormField(
              controller: _phoneController,
              decoration: InputDecoration(
                labelText: 'رقم الهاتف',
                hintText: '9647xxxxxxxxx',
                prefixIcon: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.phone, color: Colors.orange[700]),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.orange[400]!, width: 2),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                if (!value.startsWith('964')) {
                  return 'يجب أن يبدأ الرقم بـ 964';
                }
                if (value == ZainCashConfig.msisdn) {
                  return 'لا يمكن استخدام رقم التاجر. يرجى إدخال رقم عميل مختلف.';
                }
                return null;
              },
            ),
          ),
          const SizedBox(height: 20),

          // حقل رقم PIN
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextFormField(
              controller: _pinController,
              decoration: InputDecoration(
                labelText: 'الرقم السري (PIN)',
                hintText: 'أدخل رقم PIN الخاص بمحفظتك',
                prefixIcon: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.lock, color: Colors.orange[700]),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.orange[400]!, width: 2),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
              obscureText: true,
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال الرقم السري';
                }
                return null;
              },
            ),
          ),
          const SizedBox(height: 20),

          // زر الإرسال
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _processTransaction,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'إرسال رمز التحقق',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompleteStep() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Header لخطوة OTP
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green[400]!, Colors.green[600]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Icon(
                  Icons.sms,
                  size: 48,
                  color: Colors.white,
                ),
                const SizedBox(height: 12),
                const Text(
                  'رمز التحقق',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'تم إرسال رمز التحقق إلى هاتفك\nيرجى إدخاله لإكمال العملية',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.9),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // تلميح لحقل OTP
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.sms_outlined, color: Colors.green[600], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'أدخل الرمز المرسل إلى هاتفك (يمكن تعديل الرقم المعبأ)',
                    style: TextStyle(
                      color: Colors.green[700],
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // حقل OTP محسن
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextFormField(
              controller: _otpController,
              decoration: InputDecoration(
                labelText: 'رمز التحقق (OTP)',
                hintText: '1234',
                prefixIcon: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.security, color: Colors.green[700]),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.green[400]!, width: 2),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
              keyboardType: TextInputType.number,
              maxLength: 4,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                letterSpacing: 8,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال رمز التحقق';
                }
                if (value.length != 4) {
                  return 'رمز التحقق يجب أن يكون 4 أرقام';
                }
                return null;
              },
            ),
          ),
          const SizedBox(height: 20),

          // معلومات إضافية
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[700]),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'لم تستلم الرمز؟ تأكد من رقم الهاتف وانتظر قليلاً',
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessStep() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // أنيميشن النجاح
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [Colors.green[400]!, Colors.green[600]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: const Icon(
              Icons.check_circle,
              color: Colors.white,
              size: 60,
            ),
          ),
          const SizedBox(height: 30),

          // رسالة النجاح
          const Text(
            '🎉 تم الدفع بنجاح!',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          Text(
            'تم تفعيل اشتراكك بنجاح\nيمكنك الآن الاستمتاع بجميع الخدمات',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),

          // معلومات الدفع
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(Icons.payment, color: Colors.green[700]),
                    const SizedBox(width: 12),
                    const Text(
                      'تفاصيل العملية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildPaymentDetail('الباقة', widget.packageName),
                _buildPaymentDetail('المبلغ', '${widget.amount.toInt()} د.ع'),
                _buildPaymentDetail('المدة', '${widget.durationDays} يوم'),
                _buildPaymentDetail('الحالة', 'مفعل ✅'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // دالة مساعدة لعرض تفاصيل الدفع
  Widget _buildPaymentDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Row(
        children: [
          if (_currentStep != 'create' && _currentStep != 'success') ...[
            Expanded(
              child: Container(
                height: 56,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: OutlinedButton(
                  onPressed: _isLoading ? null : _cancelPayment,
                  style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    side: BorderSide.none,
                  ),
                  child: const Text(
                    'إلغاء',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
          ],
          Expanded(
            child: Container(
              height: 56,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: _currentStep == 'success'
                      ? [Colors.green[400]!, Colors.green[600]!]
                      : [Colors.orange[400]!, Colors.orange[600]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: (_currentStep == 'success' ? Colors.green : Colors.orange)
                        .withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: _isLoading ? null : _handleNextStep,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            _getButtonText(),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          if (_currentStep != 'success') ...[
                            const SizedBox(width: 8),
                            const Icon(
                              Icons.arrow_forward,
                              color: Colors.white,
                              size: 20,
                            ),
                          ],
                        ],
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  int _getCurrentStepIndex() {
    switch (_currentStep) {
      case 'create':
        return 0;
      case 'process':
        return 1;
      case 'complete':
        return 2;
      case 'success':
        return 3;
      default:
        return 0;
    }
  }

  String _getButtonText() {
    switch (_currentStep) {
      case 'process':
        return 'إرسال OTP';
      case 'complete':
        return 'إكمال الدفع';
      case 'success':
        return 'العودة';
      default:
        return 'التالي';
    }
  }

  void _createPaymentRequest() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري إنشاء طلب الدفع...';
      _isError = false;
    });

    try {
      final result = await _zainCashService.createPaymentRequestWithOTP(
        packageId: widget.packageId,
        accountNumber: widget.accountNumber,
        amount: widget.amount,
        packageName: widget.packageName,
        durationDays: widget.durationDays,
      );

      if (result['success']) {
        setState(() {
          _transactionId = result['transactionId'];
          _currentStep = 'process';
          _statusMessage = 'تم إنشاء طلب الدفع بنجاح. يرجى إدخال بيانات محفظتك.';
          _isError = false;
          _isLoading = false;
        });
      } else {
        setState(() {
          _statusMessage = result['error'] ?? 'فشل في إنشاء طلب الدفع';
          _isError = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في إنشاء طلب الدفع: $e';
        _isError = true;
        _isLoading = false;
      });
    }
  }

  void _handleNextStep() {
    switch (_currentStep) {
      case 'process':
        _processTransaction();
        break;
      case 'complete':
        _completePayment();
        break;
      case 'success':
        Navigator.of(context).pop(true);
        break;
    }
  }

  void _processTransaction() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري معالجة المعاملة...';
      _isError = false;
    });

    try {
      final result = await _zainCashService.processTransaction(
        transactionId: _transactionId!,
        phoneNumber: _phoneController.text.trim(),
        pin: _pinController.text.trim(),
      );

      if (result['success']) {
        setState(() {
          _currentStep = 'complete';
          _statusMessage = result['message'] ?? 'تم إرسال رمز OTP إلى هاتفك';
          _isError = false;
          _isLoading = false;
        });
      } else {
        setState(() {
          _statusMessage = result['error'] ?? 'فشل في معالجة المعاملة';
          _isError = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في معالجة المعاملة: $e';
        _isError = true;
        _isLoading = false;
      });
    }
  }

  void _completePayment() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري إكمال الدفع...';
      _isError = false;
    });

    try {
      final result = await _zainCashService.completePayment(
        transactionId: _transactionId!,
        phoneNumber: _phoneController.text.trim(),
        pin: _pinController.text.trim(),
        otp: _otpController.text.trim(),
      );

      if (result['success']) {
        setState(() {
          _currentStep = 'success';
          _statusMessage = result['message'] ?? 'تم الدفع بنجاح';
          _isError = false;
          _isLoading = false;
        });
      } else {
        setState(() {
          _statusMessage = result['error'] ?? 'فشل في إكمال الدفع';
          _isError = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في إكمال الدفع: $e';
        _isError = true;
        _isLoading = false;
      });
    }
  }

  void _cancelPayment() async {
    if (_transactionId != null) {
      await _zainCashService.cancelTransaction(_transactionId!);
    }
    Navigator.of(context).pop(false);
  }
}
