<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتيجة الدفع - ISP Manager</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .loading {
            color: #007bff;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        .title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .message {
            font-size: 1.1rem;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .token-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            word-break: break-all;
            font-size: 0.9rem;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="loading" class="loading">
            <div class="icon">⏳</div>
            <div class="title">جاري معالجة نتيجة الدفع...</div>
            <div class="message">يرجى الانتظار</div>
        </div>

        <div id="success" class="success" style="display: none;">
            <div class="icon">✅</div>
            <div class="title">تم الدفع بنجاح!</div>
            <div class="message">تم إكمال عملية الدفع بنجاح. سيتم تفعيل اشتراكك خلال دقائق.</div>
            <div id="success-token" class="token-info"></div>
            <a href="#" class="btn btn-success" onclick="returnToApp()">العودة للتطبيق</a>
        </div>

        <div id="error" class="error" style="display: none;">
            <div class="icon">❌</div>
            <div class="title">فشل في الدفع</div>
            <div class="message">حدث خطأ أثناء عملية الدفع. يرجى المحاولة مرة أخرى.</div>
            <div id="error-details" class="token-info"></div>
            <a href="#" class="btn" onclick="returnToApp()">العودة للتطبيق</a>
        </div>

        <div id="no-token" class="error" style="display: none;">
            <div class="icon">⚠️</div>
            <div class="title">لا توجد بيانات دفع</div>
            <div class="message">لم يتم العثور على بيانات الدفع في الرابط.</div>
            <a href="#" class="btn" onclick="returnToApp()">العودة للتطبيق</a>
        </div>
    </div>

    <script>
        // استخراج Token من URL
        function getTokenFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('token');
        }

        // فك تشفير JWT Token
        function decodeJWT(token) {
            try {
                const parts = token.split('.');
                if (parts.length !== 3) return null;
                
                const payload = parts[1];
                const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
                return JSON.parse(decoded);
            } catch (e) {
                console.error('Error decoding JWT:', e);
                return null;
            }
        }

        // العودة للتطبيق
        function returnToApp() {
            // محاولة فتح التطبيق
            window.location.href = 'ispmanager://payment-result';
            
            // إذا لم يعمل، إظهار تعليمات
            setTimeout(() => {
                alert('ارجع للتطبيق يدوياً وتحقق من حالة الاشتراك');
            }, 1000);
        }

        // معالجة النتيجة
        function processPaymentResult() {
            const token = getTokenFromUrl();
            
            if (!token) {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('no-token').style.display = 'block';
                return;
            }

            const decoded = decodeJWT(token);
            
            if (!decoded) {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('error-details').textContent = 'فشل في فك تشفير Token';
                return;
            }

            // إخفاء شاشة التحميل
            document.getElementById('loading').style.display = 'none';

            // فحص حالة الدفع
            if (decoded.status === 'success') {
                document.getElementById('success').style.display = 'block';
                document.getElementById('success-token').innerHTML = 
                    '<strong>رقم المعاملة:</strong> ' + (decoded.orderid || 'غير متوفر') + '<br>' +
                    '<strong>معرف الدفع:</strong> ' + (decoded.id || 'غير متوفر');
            } else {
                document.getElementById('error').style.display = 'block';
                document.getElementById('error-details').innerHTML = 
                    '<strong>السبب:</strong> ' + (decoded.msg || 'غير محدد') + '<br>' +
                    '<strong>الحالة:</strong> ' + (decoded.status || 'غير معروف');
            }

            // حفظ Token في localStorage للتطبيق
            localStorage.setItem('zaincash_payment_token', token);
            localStorage.setItem('zaincash_payment_result', JSON.stringify(decoded));
        }

        // تشغيل المعالجة عند تحميل الصفحة
        window.onload = processPaymentResult;
    </script>
</body>
</html>
