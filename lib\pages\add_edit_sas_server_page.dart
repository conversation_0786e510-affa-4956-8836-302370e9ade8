import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:isp_manager/services/database_service.dart';
import '../models/sas_server_model.dart';

class AddEditSasServerPage extends StatefulWidget {
  final SasServerModel? server;

  const AddEditSasServerPage({super.key, this.server});

  /// Extracts the hostname from a URL string
  /// Handles URLs with protocols (http://, https://) and paths
  static String extractHostname(String url) {
    // Remove leading/trailing whitespace
    url = url.trim();
    
    // If it's already a plain hostname (no protocol), return as is
    if (!url.contains('://') && !url.startsWith('http')) {
      // Remove any trailing paths or query parameters
      if (url.contains('/')) {
        url = url.split('/')[0];
      }
      return url;
    }
    
    // Handle URLs with protocols
    try {
      // Add protocol if missing for proper parsing
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'http://$url';
      }
      
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      // If parsing fails, try manual extraction
      try {
        // Remove protocol
        if (url.startsWith('http://')) {
          url = url.substring(7);
        } else if (url.startsWith('https://')) {
          url = url.substring(8);
        }
        
        // Remove path and query parameters
        if (url.contains('/')) {
          url = url.split('/')[0];
        }
        
        // Remove port if present
        if (url.contains(':')) {
          url = url.split(':')[0];
        }
        
        return url;
      } catch (e) {
        // If all else fails, return the original trimmed string
        return url.trim();
      }
    }
  }

  @override
  State<AddEditSasServerPage> createState() => _AddEditSasServerPageState();
}

class _AddEditSasServerPageState extends State<AddEditSasServerPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _hostController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _depositPasswordController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  ServerType _selectedServerType = ServerType.SAS;

  @override
  void initState() {
    super.initState();
    if (widget.server != null) {
      _nameController.text = widget.server!.name;
      _hostController.text = widget.server!.host;
      _usernameController.text = widget.server!.username;
      _passwordController.text = widget.server!.password;
      _depositPasswordController.text = widget.server!.depositPassword ?? '';
      _selectedServerType = widget.server!.serverType;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _hostController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _depositPasswordController.dispose();
    super.dispose();
  }

  Future<void> _saveServer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // For Earthlink servers, always use the fixed hostname
      // For SAS servers, extract the hostname from the input URL
      final host = _selectedServerType == ServerType.Earthlink
          ? 'rapi.earthlink.iq'
          : AddEditSasServerPage.extractHostname(_hostController.text);

      final newServer = SasServerModel(
        adminId: DatabaseService().adminId,
        id: widget.server?.id,
        name: _nameController.text.trim(),
        host: host,
        username: _usernameController.text.trim(),
        password: _passwordController.text,
        depositPassword: _selectedServerType == ServerType.Earthlink 
            ? _depositPasswordController.text.trim().isNotEmpty 
                ? _depositPasswordController.text.trim() 
                : null
            : null,
        isConnected: widget.server?.isConnected ?? false,
        serverType: _selectedServerType,
      );
      final FirebaseFirestore firestore = FirebaseFirestore.instance;

      if (widget.server == null) {
        try {
          SasServerModel newSas = newServer.copyWith(
            adminId: DatabaseService().adminId,
            id: firestore.collection('sas_servers').doc().id,
          );
          await FirebaseFirestore.instance
              .collection('sas_servers')
              .doc(newSas.id) // لازم الـ id يكون مضبوط
              .set(newSas.toMap());
        } catch (e) {
          // Handle error appropriately
          print('Error adding server: $e');
        }
        // await _sqliteService.insertSasServer(newServer.toMap());
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Server added successfully!')),
          );
        }
      } else {
        SasServerModel newSas = newServer.copyWith(
          adminId: DatabaseService().adminId,
          id: newServer.id, // Fix: use newServer.id instead of newSas.id
        );
        await FirebaseFirestore.instance
            .collection('sas_servers')
            .doc(newSas.id) // لازم الـ id يكون مضبوط
            .set(newSas.toMap());
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Server updated successfully!')),
          );
        }
      }
      if (mounted) {
        Navigator.pop(context, true); // Indicate success
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to save server: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.server == null ? 'Add Server' : 'Edit Server',
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    DropdownButtonFormField<ServerType>(
                      value: _selectedServerType,
                      decoration: const InputDecoration(
                        labelText: 'Server Type',
                        border: OutlineInputBorder(),
                      ),
                      items: ServerType.values.map((ServerType type) {
                        return DropdownMenuItem<ServerType>(
                          value: type,
                          child: Text(type.name),
                        );
                      }).toList(),
                      onChanged: (ServerType? newValue) {
                        setState(() {
                          _selectedServerType = newValue!;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Server Name',
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null || v.isEmpty
                          ? 'Please enter a server name'
                          : null,
                    ),
                    if (_selectedServerType == ServerType.SAS || _selectedServerType == ServerType.MikroTik) ...[
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _hostController,
                        decoration: const InputDecoration(
                          labelText: 'Host (e.g., demo4.sasradius.com)',
                          helperText: 'Can include full URL with http:// or https://',
                          border: OutlineInputBorder(),
                        ),
                        validator: (v) => v == null || v.isEmpty
                            ? 'Please enter the host'
                            : null,
                      ),
                    ],
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _usernameController,
                      decoration: const InputDecoration(
                        labelText: 'Username',
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null || v.isEmpty
                          ? 'Please enter the username'
                          : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _passwordController,
                      decoration: const InputDecoration(
                        labelText: 'Password',
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                      validator: (v) => v == null || v.isEmpty
                          ? 'Please enter the password'
                          : null,
                    ),
                    if (_selectedServerType == ServerType.Earthlink) ...[
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _depositPasswordController,
                        decoration: const InputDecoration(
                          labelText: 'Earthlink Deposit Password',
                          helperText: 'Password used for balance withdrawal and renewals',
                          border: OutlineInputBorder(),
                        ),
                        obscureText: true,
                        validator: (v) {
                          if (_selectedServerType == ServerType.Earthlink) {
                            return v == null || v.isEmpty
                                ? 'Please enter the deposit password'
                                : null;
                          }
                          return null;
                        },
                      ),
                    ],
                    const SizedBox(height: 24),
                    if (_errorMessage != null)
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ElevatedButton(
                      onPressed: _saveServer,
                      child: Text(
                        widget.server == null ? 'Add Server' : 'Update Server',
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}