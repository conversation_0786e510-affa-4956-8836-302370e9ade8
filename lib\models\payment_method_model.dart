import 'package:flutter/foundation.dart';

enum PaymentDisplayType { number, qr }

class PaymentMethodModel {
  final String id;
  final String adminId;
  final String name;
  final String accountInfo; // Could be phone number, IBAN, or QR data
  final PaymentDisplayType displayType;

  PaymentMethodModel({
    required this.id,
    required this.adminId,
    required this.name,
    required this.accountInfo,
    required this.displayType,
  });

  Map<String, dynamic> toMap() => {
        'id': id,
        'adminId': adminId,
        'name': name,
        'accountInfo': accountInfo,
        'displayType': describeEnum(displayType),
      };

  factory PaymentMethodModel.fromMap(Map<String, dynamic> map) => PaymentMethodModel(
        id: map['id'],
        adminId: map['adminId'],
        name: map['name'],
        accountInfo: map['accountInfo'],
        displayType: PaymentDisplayType.values.firstWhere(
          (e) => describeEnum(e) == map['displayType'],
          orElse: () => PaymentDisplayType.number,
        ),
      );
} 