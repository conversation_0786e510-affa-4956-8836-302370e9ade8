# الدليل الشامل لنقل Supabase إلى Firebase - نظام إدارة مزودي الإنترنت

## 📋 نظرة عامة

تم بنجاح نقل النظام من Supabase إلى Firebase مع الحفاظ على جميع الوظائف والميزات. هذا الدليل يغطي كل شيء تحتاج معرفته لإدارة النظام الجديد.

---

## 🎯 ما تم إنجازه

### ✅ 1. نقل البيانات
- **الاشتراكات**: تم نقل جميع اشتراكات الأجهزة
- **الباقات**: تم نقل جميع الباقات والخدمات
- **التحديثات**: تم نقل إعدادات تحديثات التطبيق
- **WhatsApp**: تم نقل إعدادات WhatsApp

### ✅ 2. إنشاء خدمات Firebase جديدة
- `FirebaseSubscriptionService` - إدارة الاشتراكات
- `FirebasePackagesService` - إدارة الباقات
- `FirebaseUpdateService` - إدارة التحديثات
- `FirebaseWhatsAppService` - إدارة إعدادات WhatsApp

### ✅ 3. التحديث التلقائي
- **الترحيل التلقائي**: يتم تشغيله عند بدء التطبيق
- **التحقق الذكي**: يتحقق من وجود البيانات في Firebase أولاً
- **السجلات**: يتم تسجيل عملية الترحيل في Console

---

## 🏗️ هيكل قاعدة البيانات الجديد

### Collections في Firebase

#### 1. `device_subscriptions`
```json
{
  "id": "auto-generated",
  "adminId": "admin_user_id",
  "deviceId": "unique_device_identifier",
  "packageId": "package_reference",
  "packageName": "اسم الباقة",
  "price": 50000,
  "currency": "د.ع",
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-12-31T23:59:59Z",
  "status": "active",
  "autoRenew": true,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

#### 2. `app_subscription_packages`
```json
{
  "id": "auto-generated",
  "adminId": "admin_user_id",
  "name": "باقة الإنترنت الأساسية",
  "description": "وصف الباقة",
  "price": 50000,
  "currency": "د.ع",
  "duration": 30,
  "durationUnit": "days",
  "speed": "10 Mbps",
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

#### 3. `app_updates`
```json
{
  "id": "auto-generated",
  "version": "1.0.0",
  "buildNumber": 1,
  "title": "تحديث جديد",
  "description": "وصف التحديث",
  "downloadUrl": "https://example.com/app.apk",
  "isForceUpdate": false,
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00Z"
}
```

#### 4. `whatsapp_settings`
```json
{
  "id": "auto-generated",
  "adminId": "admin_user_id",
  "apiKey": "your_whatsapp_api_key",
  "phoneNumber": "+964700000000",
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

---

## 🔧 الخدمات والوظائف

### 1. FirebaseSubscriptionService

#### الوظائف المتاحة:
```dart
// إنشاء اشتراك جديد
Future<void> createSubscription(DeviceSubscriptionModel subscription)

// جلب جميع اشتراكات المدير
Future<List<DeviceSubscriptionModel>> getSubscriptionsByAdmin(String adminId)

// جلب اشتراك محدد
Future<DeviceSubscriptionModel?> getSubscriptionById(String id)

// تحديث اشتراك
Future<void> updateSubscription(DeviceSubscriptionModel subscription)

// حذف اشتراك
Future<void> deleteSubscription(String id)

// تجديد اشتراك
Future<void> renewSubscription(String id, int durationDays)

// جلب الاشتراكات المنتهية
Future<List<DeviceSubscriptionModel>> getExpiredSubscriptions(String adminId)

// جلب الاشتراكات النشطة
Future<List<DeviceSubscriptionModel>> getActiveSubscriptions(String adminId)
```

#### مثال على الاستخدام:
```dart
final subscriptionService = FirebaseSubscriptionService();

// إنشاء اشتراك جديد
final subscription = DeviceSubscriptionModel(
  adminId: 'admin123',
  deviceId: 'device456',
  packageId: 'package789',
  packageName: 'باقة الإنترنت الأساسية',
  price: 50000,
  currency: 'د.ع',
  startDate: DateTime.now(),
  endDate: DateTime.now().add(Duration(days: 30)),
  status: 'active',
  autoRenew: true,
);

await subscriptionService.createSubscription(subscription);
```

### 2. FirebasePackagesService

#### الوظائف المتاحة:
```dart
// إنشاء باقة جديدة
Future<void> createPackage(PackageModel package)

// جلب جميع باقات المدير
Future<List<PackageModel>> getPackagesByAdmin(String adminId)

// جلب باقة محددة
Future<PackageModel?> getPackageById(String id)

// تحديث باقة
Future<void> updatePackage(PackageModel package)

// حذف باقة
Future<void> deletePackage(String id)

// تفعيل/إلغاء تفعيل باقة
Future<void> togglePackageStatus(String id, bool isActive)
```

### 3. FirebaseUpdateService

#### الوظائف المتاحة:
```dart
// إنشاء تحديث جديد
Future<void> createUpdate(AppUpdateModel update)

// جلب آخر تحديث
Future<AppUpdateModel?> getLatestUpdate()

// جلب جميع التحديثات
Future<List<AppUpdateModel>> getAllUpdates()

// تحديث معلومات التحديث
Future<void> updateAppUpdate(AppUpdateModel update)

// حذف تحديث
Future<void> deleteUpdate(String id)

// تفعيل/إلغاء تفعيل تحديث
Future<void> toggleUpdateStatus(String id, bool isActive)
```

### 4. FirebaseWhatsAppService

#### الوظائف المتاحة:
```dart
// حفظ إعدادات WhatsApp
Future<void> saveWhatsAppSettings(WhatsAppSettingsModel settings)

// جلب إعدادات WhatsApp للمدير
Future<WhatsAppSettingsModel?> getWhatsAppSettings(String adminId)

// تحديث إعدادات WhatsApp
Future<void> updateWhatsAppSettings(WhatsAppSettingsModel settings)

// تفعيل/إلغاء تفعيل WhatsApp
Future<void> toggleWhatsAppStatus(String adminId, bool isActive)
```

---

## 🚀 الترحيل التلقائي

### كيف يعمل:
1. **عند بدء التطبيق**: يتم التحقق من وجود بيانات الاشتراكات في Firebase
2. **إذا لم توجد**: يتم تشغيل الترحيل التلقائي من Supabase
3. **السجلات**: يتم تسجيل التقدم في Console
4. **الاكتمال**: يتم إعلام المستخدم عند اكتمال الترحيل

### الكود المسؤول:
```dart
// في main.dart
Future<void> _initializeApp() async {
  // ... إعدادات أخرى
  
  // الترحيل التلقائي
  await _performBackgroundMigration();
}

Future<void> _performBackgroundMigration() async {
  try {
    final subscriptionService = FirebaseSubscriptionService();
    final subscriptions = await subscriptionService.getSubscriptionsByAdmin('admin');
    
    if (subscriptions.isEmpty) {
      print('بدء الترحيل التلقائي من Supabase...');
      await _migrateSubscriptionsFromSupabase();
      print('اكتمل الترحيل التلقائي بنجاح');
    }
  } catch (e) {
    print('خطأ في الترحيل التلقائي: $e');
  }
}
```

---

## 🎨 لوحة التحكم

### 1. إدارة البيانات من Firebase Console

#### خطوات الوصول:
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. اختر مشروعك
3. اذهب إلى "Firestore Database"
4. اختر Collection المطلوب

#### إدارة الاشتراكات:
- **عرض**: اختر `device_subscriptions`
- **إضافة**: انقر "إضافة مستند"
- **تعديل**: انقر على المستند المطلوب
- **حذف**: انقر على أيقونة الحذف

#### إدارة الباقات:
- **عرض**: اختر `app_subscription_packages`
- **إضافة**: أدخل البيانات المطلوبة
- **تعديل**: عدل البيانات واحفظ
- **تفعيل/إلغاء**: غير قيمة `isActive`

### 2. قواعد الأمان

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد الاشتراكات
    match /device_subscriptions/{document} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.adminId;
    }
    
    // قواعد الباقات
    match /app_subscription_packages/{document} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.adminId;
    }
    
    // قواعد التحديثات (للقراءة فقط)
    match /app_updates/{document} {
      allow read: if true;
      allow write: if request.auth != null && 
        request.auth.token.admin == true;
    }
    
    // قواعد إعدادات WhatsApp
    match /whatsapp_settings/{document} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.adminId;
    }
  }
}
```

---

## 📱 استخدام التطبيق

### 1. إدارة الاشتراكات

#### تجديد اشتراك:
```dart
// في صفحة تجديد الاشتراك
final subscriptionService = FirebaseSubscriptionService();
await subscriptionService.renewSubscription(subscriptionId, 30);
```

#### عرض الاشتراكات:
```dart
// في صفحة الإعدادات
final subscriptions = await subscriptionService.getSubscriptionsByAdmin(adminId);
```

### 2. إدارة الباقات

#### إنشاء باقة جديدة:
```dart
final packageService = FirebasePackagesService();
final package = PackageModel(
  adminId: adminId,
  name: 'باقة جديدة',
  price: 75000,
  duration: 30,
  speed: '25 Mbps',
);
await packageService.createPackage(package);
```

### 3. إدارة التحديثات

#### فحص التحديثات:
```dart
final updateService = FirebaseUpdateService();
final latestUpdate = await updateService.getLatestUpdate();
```

---

## 🔄 تحديث منطق باقات الاشتراك الافتراضية

- **أسماء الباقات الافتراضية أصبحت:**
  - الباقة الشهرية
  - الباقة نصف السنوية
  - الباقة السنوية

- **كل باقة افتراضية تحتوي الآن على:**
  - `details`: وصف مختصر للباقة
  - `features`: قائمة مميزات (مثال: دعم فني سريع، تحديثات مجانية...)

- **منطق الإنشاء:**
  - عند فتح شاشة التجديد أو عند أول استخدام، يتم التحقق من وجود الباقات الافتراضية الثلاثة.
  - إذا كانت أي باقة ناقصة، يتم إنشاؤها تلقائيًا.
  - **لا يتم حذف أي باقة موجودة** (مخصصة أو قديمة)، ولا تتكرر الباقات الافتراضية.

- **العرض في الواجهة:**
  - يتم عرض اسم الباقة، التفاصيل، والمميزات بشكل منظم وجذاب.
  - الباقة الموصى بها تظهر عليها شارة "الأكثر شعبية".
  - السعر والمدة بارزان وواضحان.

- **تخصيص الباقات:**
  - يمكن للمطور أو المدير إضافة باقات مخصصة من لوحة التحكم أو عبر الكود.
  - الباقات الافتراضية لا تتأثر بالباقات المخصصة.

## مثال بيانات باقة افتراضية (Firestore):

```json
{
  "name": "الباقة الشهرية",
  "duration_days": 30,
  "price": 5000,
  "is_active": true,
  "is_recommended": true,
  "details": "أفضل خيار للتجربة أو الاستخدام القصير.",
  "features": [
    "دعم فني سريع",
    "تحديثات مجانية",
    "إمكانية الترقية في أي وقت"
  ]
}
```

## مثال كود إنشاء الباقات الافتراضية (Dart):

```dart
final List<Map<String, dynamic>> defaultPackages = [
  {
    'name': 'الباقة الشهرية',
    'duration_days': 30,
    'price': 5000,
    'is_active': true,
    'is_recommended': true,
    'details': 'أفضل خيار للتجربة أو الاستخدام القصير.',
    'features': [
      'دعم فني سريع',
      'تحديثات مجانية',
      'إمكانية الترقية في أي وقت',
    ],
  },
  {
    'name': 'الباقة نصف السنوية',
    'duration_days': 180,
    'price': 25000,
    'is_active': true,
    'is_recommended': false,
    'details': 'وفّر أكثر مع اشتراك 6 أشهر.',
    'features': [
      'دعم فني مميز',
      'تحديثات مجانية',
      'خصم خاص',
    ],
  },
  {
    'name': 'الباقة السنوية',
    'duration_days': 365,
    'price': 45000,
    'is_active': true,
    'is_recommended': false,
    'details': 'راحة بال لعام كامل مع أفضل سعر.',
    'features': [
      'دعم فني VIP',
      'تحديثات مجانية طوال السنة',
      'أفضل قيمة مقابل السعر',
    ],
  },
];
```

---

## 🔍 استكشاف الأخطاء

### الأخطاء الشائعة وحلولها:

#### 1. خطأ في الاتصال بـ Firebase
```
الحل: تحقق من إعدادات Firebase في firebase_options.dart
```

#### 2. خطأ في الصلاحيات
```
الحل: تحقق من قواعد الأمان في Firestore
```

#### 3. خطأ في الترحيل
```
الحل: تحقق من اتصال Supabase وإعدادات الترحيل
```

### سجلات التطبيق:
```dart
// في Console ستجد:
'بدء الترحيل التلقائي من Supabase...'
'تم ترحيل X اشتراك'
'اكتمل الترحيل التلقائي بنجاح'
```

---

## 📊 المراقبة والإحصائيات

### 1. إحصائيات Firebase Console
- اذهب إلى "Usage" لمراقبة الاستخدام
- اذهب إلى "Performance" لمراقبة الأداء
- اذهب إلى "Crashlytics" لمراقبة الأخطاء

### 2. سجلات التطبيق
```dart
// إضافة سجلات مخصصة
print('عدد الاشتراكات: ${subscriptions.length}');
print('عدد الباقات: ${packages.length}');
```

---

## 🛡️ الأمان

### 1. حماية البيانات
- جميع البيانات محمية بقواعد الأمان
- كل مدير يرى بياناته فقط
- التحقق من الصلاحيات في كل عملية

### 2. إدارة المفاتيح
- احفظ `firebase_options.dart` في مكان آمن
- لا تشارك المفاتيح في الكود العام
- استخدم متغيرات البيئة للإنتاج

---

## 🚀 النشر والإنتاج

### 1. إعداد Firebase للمنتج
```bash
# إنشاء مشروع Firebase جديد
firebase init

# إعداد Firestore
firebase firestore:rules

# نشر قواعد الأمان
firebase deploy --only firestore:rules
```

### 2. إعداد التطبيق
```dart
// تأكد من إعدادات Firebase الصحيحة
// تحقق من قواعد الأمان
// اختبر جميع الوظائف
```

---

## 📚 المراجع والروابط

### وثائق Firebase:
- [Firebase Console](https://console.firebase.google.com/)
- [Firestore Documentation](https://firebase.google.com/docs/firestore)
- [Firebase Security Rules](https://firebase.google.com/docs/firestore/security/get-started)

### وثائق Flutter:
- [Firebase Flutter Plugin](https://firebase.flutter.dev/)
- [Cloud Firestore Plugin](https://firebase.flutter.dev/docs/firestore/overview/)

---

## 🎯 الخطوات التالية

### 1. الاختبار الشامل
- [ ] اختبار جميع الوظائف
- [ ] اختبار الترحيل التلقائي
- [ ] اختبار الأمان والصلاحيات

### 2. التحسينات
- [ ] إضافة إشعارات للتحديثات
- [ ] تحسين الأداء
- [ ] إضافة تقارير متقدمة

### 3. النشر
- [ ] إعداد Firebase للمنتج
- [ ] نشر التطبيق
- [ ] مراقبة الأداء

---

## 📞 الدعم والمساعدة

### في حالة الحاجة للمساعدة:
1. راجع سجلات Console للأخطاء
2. تحقق من إعدادات Firebase
3. راجع قواعد الأمان
4. تأكد من صحة البيانات

### روابط مفيدة:
- [Firebase Support](https://firebase.google.com/support)
- [Flutter Documentation](https://docs.flutter.dev/)
- [Firebase Community](https://firebase.google.com/community)

---

## ✨ الخلاصة

تم بنجاح نقل النظام من Supabase إلى Firebase مع:
- ✅ الحفاظ على جميع الوظائف
- ✅ الترحيل التلقائي للبيانات
- ✅ لوحة تحكم شاملة
- ✅ أمان عالي
- ✅ أداء محسن

النظام الآن جاهز للاستخدام والإنتاج! 🚀 