class PrinterSettingsModel {
  final String? connectedDeviceAddress;
  final String? connectedDeviceName;
  final String paperSize;
  final bool showSubscriberName;
  final bool showSubscriptionNumber;
  final bool showDateTime;
  final bool showPaymentAmount;
  final bool showOperationType;
  final bool showEmployeeName;
  final bool showCompanyInfo;
  final bool autoRenewalPrint;
  final bool autoPaymentPrint;
  final String? companyName;
  final String? companyAddress;
  final List<String> elementOrder;

  PrinterSettingsModel({
    this.connectedDeviceAddress,
    this.connectedDeviceName,
    this.paperSize = '80mm',
    this.showSubscriberName = true,
    this.showSubscriptionNumber = true,
    this.showDateTime = true,
    this.showPaymentAmount = true,
    this.showOperationType = true,
    this.showEmployeeName = true,
    this.showCompanyInfo = true,
    this.autoRenewalPrint = false,
    this.autoPaymentPrint = false,
    this.companyName = 'شركة المنصة للخدمات الذكية',
    this.companyAddress = '',
    List<String>? elementOrder,
  }) : elementOrder = elementOrder ?? [
    'companyInfo',
    'subscriberName',
    'subscriptionNumber',
    'dateTime',
    'paymentAmount',
    'operationType',
    'employeeName',
  ];

  PrinterSettingsModel copyWith({
    String? connectedDeviceAddress,
    String? connectedDeviceName,
    String? paperSize,
    bool? showSubscriberName,
    bool? showSubscriptionNumber,
    bool? showDateTime,
    bool? showPaymentAmount,
    bool? showOperationType,
    bool? showEmployeeName,
    bool? showCompanyInfo,
    bool? autoRenewalPrint,
    bool? autoPaymentPrint,
    String? companyName,
    String? companyAddress,
    List<String>? elementOrder,
  }) {
    return PrinterSettingsModel(
      connectedDeviceAddress: connectedDeviceAddress ?? this.connectedDeviceAddress,
      connectedDeviceName: connectedDeviceName ?? this.connectedDeviceName,
      paperSize: paperSize ?? this.paperSize,
      showSubscriberName: showSubscriberName ?? this.showSubscriberName,
      showSubscriptionNumber: showSubscriptionNumber ?? this.showSubscriptionNumber,
      showDateTime: showDateTime ?? this.showDateTime,
      showPaymentAmount: showPaymentAmount ?? this.showPaymentAmount,
      showOperationType: showOperationType ?? this.showOperationType,
      showEmployeeName: showEmployeeName ?? this.showEmployeeName,
      showCompanyInfo: showCompanyInfo ?? this.showCompanyInfo,
      autoRenewalPrint: autoRenewalPrint ?? this.autoRenewalPrint,
      autoPaymentPrint: autoPaymentPrint ?? this.autoPaymentPrint,
      companyName: companyName ?? this.companyName,
      companyAddress: companyAddress ?? this.companyAddress,
      elementOrder: elementOrder ?? List<String>.from(this.elementOrder),
    );
  }

  Map<String, dynamic> toJson() => {
    'connectedDeviceAddress': connectedDeviceAddress,
    'connectedDeviceName': connectedDeviceName,
    'paperSize': paperSize,
    'showSubscriberName': showSubscriberName,
    'showSubscriptionNumber': showSubscriptionNumber,
    'showDateTime': showDateTime,
    'showPaymentAmount': showPaymentAmount,
    'showOperationType': showOperationType,
    'showEmployeeName': showEmployeeName,
    'showCompanyInfo': showCompanyInfo,
    'autoRenewalPrint': autoRenewalPrint,
    'autoPaymentPrint': autoPaymentPrint,
    'companyName': companyName,
    'companyAddress': companyAddress,
    'elementOrder': elementOrder,
  };

  static PrinterSettingsModel fromJson(Map<String, dynamic> json) {
    return PrinterSettingsModel(
      connectedDeviceAddress: json['connectedDeviceAddress'],
      connectedDeviceName: json['connectedDeviceName'],
      paperSize: json['paperSize'] ?? '80mm',
      showSubscriberName: json['showSubscriberName'] ?? true,
      showSubscriptionNumber: json['showSubscriptionNumber'] ?? true,
      showDateTime: json['showDateTime'] ?? true,
      showPaymentAmount: json['showPaymentAmount'] ?? true,
      showOperationType: json['showOperationType'] ?? true,
      showEmployeeName: json['showEmployeeName'] ?? true,
      showCompanyInfo: json['showCompanyInfo'] ?? true,
      autoRenewalPrint: json['autoRenewalPrint'] ?? false,
      autoPaymentPrint: json['autoPaymentPrint'] ?? false,
      companyName: json['companyName'] ?? 'شركة المنصة للخدمات الذكية',
      companyAddress: json['companyAddress'] ?? '',
      elementOrder: (json['elementOrder'] as List?)?.map((e) => e.toString()).toList(),
    );
  }
}
