// مثال شامل لاستخدام نظام العملة والدولة الجديد
import 'package:flutter/material.dart';
import '../widgets/currency_country_widgets.dart';
import '../services/app_settings_service.dart';
import '../services/whatsapp_service.dart';

class ExampleUsagePage extends StatefulWidget {
  const ExampleUsagePage({super.key});

  @override
  State<ExampleUsagePage> createState() => _ExampleUsagePageState();
}

class _ExampleUsagePageState extends State<ExampleUsagePage> {
  final _phoneController = TextEditingController();
  final _amountController = TextEditingController();

  @override
  void dispose() {
    _phoneController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مثال استخدام النظام الجديد'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عرض الإعدادات الحالية
            const SettingsSummaryCard(),
            const SizedBox(height: 24),

            // أمثلة على عرض العملة
            Text(
              'أمثلة على عرض العملة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text('مبلغ عادي: '),
                        const CurrencyText(amount: 50000),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Text('مبلغ كبير: '),
                        CurrencyText(
                          amount: 1250000,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Text('مبلغ بدون رمز: '),
                        const CurrencyText(
                          amount: 75000,
                          showSymbol: false,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // أمثلة على عرض أرقام الهواتف
            Text(
              'أمثلة على عرض أرقام الهواتف',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text('رقم عادي: '),
                        const PhoneText(phoneNumber: '7901234567'),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Text('رقم مع تنسيق: '),
                        PhoneText(
                          phoneNumber: '7709876543',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Text('رقم بدون مفتاح: '),
                        const PhoneText(
                          phoneNumber: '7501112233',
                          showCountryCode: false,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // أمثلة على حقول الإدخال
            Text(
              'أمثلة على حقول الإدخال',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    PhoneInputField(
                      controller: _phoneController,
                      labelText: 'رقم هاتف المشترك',
                      isRequired: true,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال رقم الهاتف';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    CurrencyInputField(
                      controller: _amountController,
                      labelText: 'مبلغ الدفع',
                      hintText: 'أدخل المبلغ المطلوب',
                      isRequired: true,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال المبلغ';
                        }
                        final amount = double.tryParse(value);
                        if (amount == null || amount <= 0) {
                          return 'يرجى إدخال مبلغ صحيح';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // مثال على استخدام البرمجة المباشرة
            Text(
              'مثال على الاستخدام البرمجي',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'استخدام الخدمات مباشرة:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton(
                      onPressed: () async {
                        // مثال على الاستخدام المباشر
                        final currencySymbol = await AppSettingsService.getCurrencySymbol();
                        final formattedAmount = await AppSettingsService.formatCurrency(100000);
                        final formattedPhone = await AppSettingsService.formatPhoneNumber('7901234567');
                        
                        if (!mounted) return;
                        
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('نتائج التنسيق'),
                            content: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('رمز العملة: $currencySymbol'),
                                Text('المبلغ المنسق: $formattedAmount'),
                                Text('الهاتف المنسق: $formattedPhone'),
                              ],
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('موافق'),
                              ),
                            ],
                          ),
                        );
                      },
                      child: const Text('اختبار التنسيق'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // مثال على استخدام WhatsApp
            Text(
              'مثال على استخدام WhatsApp',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'خدمة WhatsApp الجديدة:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton(
                      onPressed: () async {
                        try {
                          // مثال على إرسال رسالة
                          await WhatsAppService().sendMessage(
                            '7901234567', 
                            'مرحباً! هذه رسالة تجريبية من التطبيق الجديد.',
                          );
                        } catch (e) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('خطأ: $e')),
                          );
                        }
                      },
                      child: const Text('إرسال رسالة تجريبية'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () async {
                        try {
                          // مثال على فتح محادثة
                          await WhatsAppService().openChat('7901234567');
                        } catch (e) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('خطأ: $e')),
                          );
                        }
                      },
                      child: const Text('فتح محادثة'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // معلومات إضافية
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: Theme.of(context).colorScheme.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'نصائح للمطورين',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '• استخدم CurrencyText بدلاً من Text العادي للمبالغ المالية',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '• استخدم PhoneText لعرض أرقام الهواتف مع مفتاح الدولة',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '• استخدم PhoneInputField و CurrencyInputField في النماذج',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '• استخدم PackageDisplayText لعرض معلومات الحزم مع الأسعار',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
