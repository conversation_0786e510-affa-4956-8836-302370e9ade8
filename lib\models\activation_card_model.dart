import 'package:flutter/foundation.dart';

class ActivationCardModel {
  final String id; // كود البطاقة
  final String adminId;
  final String value; // عدد الأيام أو اسم الباقة
  final bool isUsed;
  final String? usedBy; // subscriberId أو null
  final DateTime createdAt;
  final DateTime? usedAt;

  ActivationCardModel({
    required this.id,
    required this.adminId,
    required this.value,
    required this.isUsed,
    this.usedBy,
    required this.createdAt,
    this.usedAt,
  });

  Map<String, dynamic> toMap() => {
        'id': id,
        'adminId': adminId,
        'value': value,
        'isUsed': isUsed,
        'usedBy': usedBy,
        'createdAt': createdAt.toIso8601String(),
        'usedAt': usedAt?.toIso8601String(),
      };

  factory ActivationCardModel.fromMap(Map<String, dynamic> map) => ActivationCardModel(
        id: map['id'],
        adminId: map['adminId'],
        value: map['value'],
        isUsed: map['isUsed'] ?? false,
        usedBy: map['usedBy'],
        createdAt: map['createdAt'] is DateTime
            ? map['createdAt']
            : DateTime.parse(map['createdAt']),
        usedAt: map['usedAt'] == null
            ? null
            : (map['usedAt'] is DateTime
                ? map['usedAt']
                : DateTime.parse(map['usedAt'])),
      );
} 