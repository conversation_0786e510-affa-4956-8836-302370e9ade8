import 'package:isp_manager/models/mikrotik_device_model.dart';

class AIAssistantConfig {
  final String id;
  final String geminiApiKey;
  final DateTime createdAt;
  final String? lastUsedDeviceId;

  AIAssistantConfig({
    required this.id,
    required this.geminiApiKey,
    DateTime? createdAt,
    this.lastUsedDeviceId,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'geminiApiKey': geminiApiKey,
      'createdAt': createdAt.toIso8601String(),
      'lastUsedDeviceId': lastUsedDeviceId,
    };
  }

  factory AIAssistantConfig.fromMap(Map<String, dynamic> map) {
    return AIAssistantConfig(
      id: map['id'],
      geminiApiKey: map['geminiApiKey'],
      createdAt: DateTime.parse(map['createdAt']),
      lastUsedDeviceId: map['lastUsedDeviceId'],
    );
  }
}

class AIChatMessage {
  final String id;
  final String role; // 'user' or 'assistant'
  final String content;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;
  final MikrotikDeviceModel? device; // Device reference for context

  AIChatMessage({
    required this.id,
    required this.role,
    required this.content,
    DateTime? timestamp,
    this.metadata,
    this.device,
  }) : timestamp = timestamp ?? DateTime.now();
  
  // Helper constructor for user messages
  factory AIChatMessage.user({
    required String content,
    MikrotikDeviceModel? device,
  }) {
    return AIChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: 'user',
      content: content,
      device: device,
    );
  }
  
  // Helper constructor for assistant messages
  factory AIChatMessage.assistant({
    required String content,
    MikrotikDeviceModel? device,
  }) {
    return AIChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: 'assistant',
      content: content,
      device: device,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'role': role,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
      'device': device?.toMap(),
    };
  }

  factory AIChatMessage.fromMap(Map<String, dynamic> map) {
    return AIChatMessage(
      id: map['id'],
      role: map['role'],
      content: map['content'],
      timestamp: DateTime.parse(map['timestamp']),
      metadata: map['metadata'],
      device: map['device'] != null
          ? MikrotikDeviceModel.fromMap(map['device'])
          : null,
    );
  }
}
