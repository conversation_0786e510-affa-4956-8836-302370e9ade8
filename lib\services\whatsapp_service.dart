// WhatsApp Helper Service for handling phone numbers and messaging
import 'package:android_intent_plus/android_intent.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'app_settings_service.dart';

class WhatsAppService {
  static final WhatsAppService _instance = WhatsAppService._internal();
  factory WhatsAppService() => _instance;
  WhatsAppService._internal();

  /// تنسيق رقم الهاتف لـ WhatsApp باستخدام مفتاح الدولة من الإعدادات
  Future<String> formatPhoneForWhatsApp(String phone) async {
    // الحصول على مفتاح الدولة من الإعدادات
    final phoneCode = await AppSettingsService.getPhoneCode();
    final cleanPhoneCode = phoneCode.replaceAll('+', ''); // إزالة علامة +
    
    // تنظيف رقم الهاتف
    String phoneNumber = phone.replaceAll(RegExp(r'[^\d]'), ''); // إزالة جميع الرموز غير الرقمية
    
    // إضافة مفتاح الدولة من الإعدادات إذا لم يكن موجودًا
    if (phoneNumber.startsWith('0')) {
      phoneNumber = '$cleanPhoneCode${phoneNumber.substring(1)}';
    } else if (!phoneNumber.startsWith(cleanPhoneCode)) {
      phoneNumber = '$cleanPhoneCode$phoneNumber';
    }
    
    return phoneNumber;
  }

  /// إرسال رسالة عبر WhatsApp
  Future<void> sendMessage(String phone, String message) async {
    try {
      // تنسيق رقم الهاتف
      final phoneNumber = await formatPhoneForWhatsApp(phone);
      print('WhatsAppService: Formatted phone number: $phoneNumber');
      
      // تشفير الرسالة
      final encodedMessage = Uri.encodeComponent(message);
      print('WhatsAppService: Encoded message: $encodedMessage');
      
      // محاولة فتح واتساب مع الرقم والرسالة
      final whatsappUrl = 'whatsapp://send?phone=$phoneNumber&text=$encodedMessage';
      print('WhatsAppService: WhatsApp URL: $whatsappUrl');
      
      final intent = AndroidIntent(
        action: 'android.intent.action.VIEW',
        data: whatsappUrl,
      );
      
      try {
        await intent.launch();
        print('WhatsAppService: WhatsApp launched successfully via intent.');
      } catch (e) {
        print('WhatsAppService: Failed to launch WhatsApp via intent: $e. Trying fallback URL.');
        // في حالة فشل فتح واتساب، جرب الرابط البديل
        final fallbackIntent = AndroidIntent(
          action: 'android.intent.action.VIEW',
          data: 'https://wa.me/$phoneNumber?text=$encodedMessage',
        );
        await fallbackIntent.launch();
        print('WhatsAppService: WhatsApp launched successfully via fallback URL.');
      }
    } catch (e) {
      print('WhatsAppService: Error in sendMessage: $e');
      Fluttertoast.showToast(
        msg: 'لا يمكن فتح واتساب. تأكد من تثبيت التطبيق',
        toastLength: Toast.LENGTH_LONG,
      );
      throw Exception('Cannot launch WhatsApp: $e');
    }
  }

  /// إرسال رسالة فقط لرقم محدد (بدون تنسيق إضافي)
  Future<void> sendMessageToFormattedNumber(String formattedPhone, String message) async {
    try {
      // تشفير الرسالة
      final encodedMessage = Uri.encodeComponent(message);
      
      // محاولة فتح واتساب مع الرقم والرسالة
      final whatsappUrl = 'whatsapp://send?phone=$formattedPhone&text=$encodedMessage';
      
      final intent = AndroidIntent(
        action: 'android.intent.action.VIEW',
        data: whatsappUrl,
      );
      
      try {
        await intent.launch();
      } catch (e) {
        // في حالة فشل فتح واتساب، جرب الرابط البديل
        final fallbackIntent = AndroidIntent(
          action: 'android.intent.action.VIEW',
          data: 'https://wa.me/$formattedPhone?text=$encodedMessage',
        );
        await fallbackIntent.launch();
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'لا يمكن فتح واتساب. تأكد من تثبيت التطبيق',
        toastLength: Toast.LENGTH_LONG,
      );
      throw Exception('Cannot launch WhatsApp: $e');
    }
  }

  /// فتح محادثة WhatsApp بدون رسالة محددة مسبقاً
  Future<void> openChat(String phone) async {
    try {
      // تنسيق رقم الهاتف
      final phoneNumber = await formatPhoneForWhatsApp(phone);
      
      // محاولة فتح واتساب للمحادثة
      final whatsappUrl = 'whatsapp://send?phone=$phoneNumber';
      
      final intent = AndroidIntent(
        action: 'android.intent.action.VIEW',
        data: whatsappUrl,
      );
      
      try {
        await intent.launch();
      } catch (e) {
        // في حالة فشل فتح واتساب، جرب الرابط البديل
        final fallbackIntent = AndroidIntent(
          action: 'android.intent.action.VIEW',
          data: 'https://wa.me/$phoneNumber',
        );
        await fallbackIntent.launch();
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'لا يمكن فتح واتساب. تأكد من تثبيت التطبيق',
        toastLength: Toast.LENGTH_LONG,
      );
      throw Exception('Cannot launch WhatsApp: $e');
    }
  }
}
