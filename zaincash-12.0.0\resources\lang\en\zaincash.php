<?php

return [
    "amount_required" => "You must specify the amount.",
    "amount_numeric" => "The amount must be a number.",
    'amount_min' => 'Amount must be at least :min IQD.',
    "msisdn_regex" => "The Msisdn phone number is invalid. must be 13 digits. Example: 9647813881805.",
    "serviceType_required" => "You must specify the Service Type (e.g., Book, Travel, Gaming, etc).",
    "serviceType_max" => "The Service Type must not be greater than 254 characters.",
    "orderId_required" => "You must specify the Order ID, which acts as a recipe ID (e.g., 1515616313).",
    "orderId_max" => "The Order ID must not be greater than 512 characters.",
    "id_required" => "You must specify the Transaction ID.",
    "id_hexadecimal" => "The :attribute must be a valid hexadecimal string.",
    "phonenumber_required" => "You must specify the phone number.",
    "phonenumber_regex" => "The phonenumber is invalid. must be 13 digits. Example: 9647813881805.",
    "pin_required" => "You must specify the PIN.",
    "pin_max" => "The PIN must not be greater than 254 characters.",
    "opt_required" => "You must specify the OTP.",
    "opt_max" => "The OTP must not be greater than 10 characters.",
];
