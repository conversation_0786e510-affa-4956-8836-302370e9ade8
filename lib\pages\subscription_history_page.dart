import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'package:isp_manager/services/firebase_app_subscription_packages_service.dart';

class SubscriptionHistoryPage extends StatefulWidget {
  final String accountNumber;

  const SubscriptionHistoryPage({Key? key, required this.accountNumber}) : super(key: key);

  @override
  _SubscriptionHistoryPageState createState() => _SubscriptionHistoryPageState();
}

class _SubscriptionHistoryPageState extends State<SubscriptionHistoryPage> {
  late Future<List<Map<String, dynamic>>> _subscriptionHistoryFuture;

  @override
  void initState() {
    super.initState();
    _subscriptionHistoryFuture = FirebaseAppSubscriptionPackagesService().getSubscriptionHistory(widget.accountNumber);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('سجل الدفعات'),
      ),
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: _subscriptionHistoryFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('حدث خطأ في جلب البيانات'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return Center(child: Text('لا يوجد سجل دفعات لعرضه'));
          }

          final history = snapshot.data!;

          return ListView.builder(
            itemCount: history.length,
            itemBuilder: (context, index) {
              final payment = history[index];
              DateTime paymentDate;
              if (payment['payment_date'] is Timestamp) {
                paymentDate = (payment['payment_date'] as Timestamp).toDate();
              } else if (payment['payment_date'] is String) {
                paymentDate = DateTime.parse(payment['payment_date']);
              } else {
                paymentDate = DateTime.now();
              }
              final formattedDate = DateFormat('yyyy-MM-dd – hh:mm a').format(paymentDate);

              return Card(
                margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ListTile(
                  title: Text(payment['package_name'] ?? 'باقة غير مسماة'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('تاريخ الدفع: $formattedDate'),
                      Text('طريقة الدفع: ${payment['payment_method'] ?? 'غير محدد'}'),
                      Text('لمدة: ${payment['duration_days']} يومًا'),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
