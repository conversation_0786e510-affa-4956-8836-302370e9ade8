import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';

class MapService {
  static final MapService _instance = MapService._internal();
  factory MapService() => _instance;
  MapService._internal();

  // طبقات الخرائط المختلفة
  static final TileLayer cartoDBLayer = TileLayer(
    urlTemplate: 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png',
    subdomains: ['a', 'b', 'c', 'd'],
    userAgentPackageName: 'com.example.isp_manager',
  );

  static final TileLayer stamenLayer = TileLayer(
    urlTemplate: 'https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}.png',
    subdomains: ['a', 'b', 'c', 'd'],
    userAgentPackageName: 'com.example.isp_manager',
  );

  static final TileLayer openStreetMapLayer = TileLayer(
    urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
    userAgentPackageName: 'com.example.isp_manager',
  );

  // الحصول على الموقع الحالي مع معالجة محسنة للأخطاء
  Future<Map<String, dynamic>> getCurrentLocation() async {
    try {
      // التحقق من تفعيل خدمة الموقع
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return {
          'success': false,
          'error': 'خدمة الموقع غير مفعلة',
          'errorType': 'service_disabled',
        };
      }

      // التحقق من الأذونات
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return {
            'success': false,
            'error': 'تم رفض إذن الموقع',
            'errorType': 'permission_denied',
          };
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return {
          'success': false,
          'error': 'إذن الموقع مرفوض نهائياً. يرجى تفعيله من إعدادات الجهاز',
          'errorType': 'permission_denied_forever',
        };
      }

      // الحصول على الموقع
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      return {
        'success': true,
        'position': position,
        'location': LatLng(position.latitude, position.longitude),
        'accuracy': position.accuracy,
        'timestamp': position.timestamp,
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في الحصول على الموقع: $e',
        'errorType': 'location_error',
      };
    }
  }

  // الحصول على الموقع مع محاولات متعددة
  Future<Map<String, dynamic>> getCurrentLocationWithRetry({int maxRetries = 3}) async {
    for (int i = 0; i < maxRetries; i++) {
      final result = await getCurrentLocation();
      if (result['success']) {
        return result;
      }
      
      // انتظار قبل المحاولة التالية
      if (i < maxRetries - 1) {
        await Future.delayed(Duration(seconds: 2 * (i + 1)));
      }
    }
    
    return {
      'success': false,
      'error': 'فشل في الحصول على الموقع بعد $maxRetries محاولات',
      'errorType': 'max_retries_exceeded',
    };
  }

  // التحقق من صحة الإحداثيات
  bool isValidCoordinates(double latitude, double longitude) {
    return latitude >= -90 && latitude <= 90 && 
           longitude >= -180 && longitude <= 180;
  }

  // حساب المسافة بين نقطتين
  double calculateDistance(LatLng point1, LatLng point2) {
    return Geolocator.distanceBetween(
      point1.latitude, point1.longitude,
      point2.latitude, point2.longitude,
    ) / 1000; // تحويل إلى كيلومترات
  }

  // عرض رسالة خطأ مناسبة
  void showLocationError(BuildContext context, Map<String, dynamic> result) {
    String message = result['error'] ?? 'خطأ غير معروف في تحديد الموقع';
    
    if (result['errorType'] == 'service_disabled') {
      message = 'يرجى تفعيل خدمة الموقع من إعدادات الجهاز';
    } else if (result['errorType'] == 'permission_denied') {
      message = 'يرجى السماح للتطبيق بالوصول إلى موقعك';
    } else if (result['errorType'] == 'permission_denied_forever') {
      message = 'إذن الموقع مرفوض نهائياً. يرجى تفعيله من إعدادات الجهاز';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'إعدادات',
          textColor: Colors.white,
          onPressed: () {
            Geolocator.openAppSettings();
          },
        ),
      ),
    );
  }

  // عرض رسالة نجاح تحديد الموقع
  void showLocationSuccess(BuildContext context, LatLng location) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تحديد موقعك بنجاح: ${location.latitude.toStringAsFixed(4)}, ${location.longitude.toStringAsFixed(4)}'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }
} 