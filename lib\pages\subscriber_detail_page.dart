import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:isp_manager/services/firebase_auth_service.dart';
import 'package:isp_manager/services/firebase_service.dart';
import 'dart:convert';
import '../services/database_service.dart';
import '../services/ai_service.dart';
import '../services/message_service.dart';
import '../services/printer_service.dart';
import '../services/app_settings_service.dart';
import '../services/whatsapp_service.dart';
import '../models/subscriber_model.dart';
import '../models/package_model.dart';
import '../models/user_model.dart';
import '../models/printer_settings_model.dart';
import '../models/activity_log_model.dart';
import '../models/payment_record_model.dart';
import '../widgets/currency_country_widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/sqlite_service.dart'; // Import SQLiteService
import '../services/sas_api_service.dart'; // Import SasApiService
import '../models/sas_user_model.dart'; // Import SasUser
import '../models/sas_server_model.dart'; // Import SasServerModel
import '../models/network_device.dart'; // Import NetworkDevice
import '../services/network_device_service.dart'; // Import NetworkDeviceService
import '../helpers/database_helper.dart'; // Import DatabaseHelper
import 'message_templates_page.dart'; // Import MessageTemplatesPage
import '../models/message_template_model.dart'; // Import MessageTemplateModel
import '../services/earthlink_service.dart'; // Import EarthlinkService
import '../models/earthlink_account_model.dart'; // Import EarthlinkAccount
import '../models/earthlink_user_model.dart'; // Import EarthlinkUser
import '../services/mikrotik_sync_service.dart'; // Import MikroTik Service

class SubscriberDetailPage extends StatefulWidget {
  final String subscriberId;

  const SubscriberDetailPage({super.key, required this.subscriberId});

  @override
  State<SubscriberDetailPage> createState() => _SubscriberDetailPageState();
}

class _SubscriberDetailPageState extends State<SubscriberDetailPage>
    with TickerProviderStateMixin {
  SubscriberModel? _subscriber;
  PackageModel? _package;
  List<ActivityLogModel> _activityLogs = [];
  List<PaymentRecordModel> _paymentRecords = [];
  UserModel? _currentUser;
  SasUser? _sasUser; // New state variable for SAS Radius user
  bool _isLoading = true;
  late TabController _tabController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  Map<String, dynamic>? _onlineInfo;
  bool _loadingOnline = false;
  List<PackageModel> _packages = [];

  // متغيرات جديدة لإدارة الأجهزة والإشارة
  NetworkDevice? _networkDevice;
  Map<String, String>? _ubntSignalInfo;
  bool _loadingSignal = false;
  String _tempUsername = '';
  String _tempPassword = '';

  // متغير لحفظ السيرفر الذي تم استخدامه لجلب بيانات المشترك

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _loadData();
    //  WidgetsBinding.instance
    //     .addPostFrameCallback((_) => serverUserData());
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  // دالة مساعدة لإظهار الرسائل بشكل آمن
  void _showSafeSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : null,
        ),
      );
    } catch (scaffoldError) {
      print(message);
      // إظهار Toast كبديل
      Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: isError ? Colors.red : Colors.green,
        textColor: Colors.white,
      );
    }
  }

  Future<void> _loadData() async {
    if (!mounted) return;
    setState(() => _isLoading = true);
    try {
      var subscribers = await DatabaseService().getSubscribersFire();
      if (subscribers.isEmpty) {
        subscribers = await DatabaseService().getSubscribers();
      }
      var packages = await DatabaseService().getPackagesFire();
      if (packages.isEmpty) {
        packages = await DatabaseService().getPackages();
      }
      var activityLogs = await DatabaseService().getActivityLogsFire();
      if (activityLogs.isEmpty) {
        activityLogs = await DatabaseService().getActivityLogs();
      }
      var paymentRecords = await DatabaseService().getPaymentRecordsFire();
      if (paymentRecords.isEmpty) {
        paymentRecords = await DatabaseService().getPaymentRecords();
      }
      final currentUser = await FirebaseAuthService().getUserData(
        FirebaseAuth.instance.currentUser!.uid,
      );
      DatabaseService().syncPaymentsToFirebase();
      DatabaseService().syncActivityToFirebase();
      final subscriber =
          subscribers.where((s) => s.id == widget.subscriberId).isNotEmpty
          ? subscribers.where((s) => s.id == widget.subscriberId).first
          : null;

      if (subscriber != null) {
        final package =
            packages.where((p) => p.id == subscriber.packageId).isNotEmpty
            ? packages.where((p) => p.id == subscriber.packageId).first
            : null;

        final subscriberLogs =
            activityLogs
                .where((log) => log.subscriberId == subscriber.id)
                .toList()
              ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

        final subscriberPayments =
            paymentRecords
                .where((record) => record.subscriberId == subscriber.id)
                .toList()
              ..sort(
                (a, b) => b.paymentDate.compareTo(a.paymentDate),
              );
        try {
          if (!mounted) return;
          setState(() {
            print('Setting state with subscriber: ${subscriber.fullName}');
            _subscriber = subscriber;
            _package = package;
            _activityLogs = subscriberLogs;
            _paymentRecords = subscriberPayments;
            _currentUser = currentUser;
            _packages = packages;
            _isLoading = false;
            print('State set successfully');
          });
          serverUserData();
          // جلب معلومات الجلسة تلقائياً بعد تحميل البيانات
          _fetchOnlineInfo();
          
          // If this is an Earthlink subscriber, try to fetch more detailed information
          if (subscriber.earthlinkUserIndex != null && subscriber.earthlinkUserIndex!.isNotEmpty) {
            _fetchEarthlinkDetailedInfo();
          }
        } catch (e) {
          print('Error in setState: $e');
          if (mounted) {
            setState(() => _isLoading = false);
          }
        }

        try {
          _fadeController.forward();
        } catch (e) {
          print('Error in fadeController.forward: $e');
        }
      } else {
        print('Subscriber not found, navigating back');
        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      print('Error in _loadData: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        Navigator.of(context).pop();
      }
    }
  }

    Future<void> serverUserData() async {
    if (_subscriber == null || _subscriber!.username.isEmpty) {
      // No username, so nothing to do on any server.
      return;
    }

    // Get the currently active connected server.
    final SasServerModel? activeServer =
        await DatabaseService().getConnectedServer();

    // If no server is active, we cannot fetch server-side data.
    if (activeServer == null) {
      print('ℹ️ No active server connected. Skipping server-side user data fetch.');
      return;
    }

    print(
        'ℹ️ Active server found: ${activeServer.name} (Type: ${activeServer.serverType}). Fetching user data...');

    // Execute logic based on the active server's type.
    switch (activeServer.serverType) {
      case ServerType.SAS:
        await _fetchSasUserData(activeServer);
        break;
      case ServerType.Earthlink:
        await _fetchEarthlinkUserData(activeServer);
        break;
      case ServerType.MikroTik:
        print(
            'ℹ️ Active server is MikroTik. No specific user data to fetch in serverUserData. Online status will be fetched separately.');
        // Let's clear any potentially stale SAS user data.
        if (mounted && _sasUser != null) {
          setState(() {
            _sasUser = null;
          });
        }
        break;
      default:
        print(
            'ℹ️ Unhandled server type in serverUserData: ${activeServer.serverType}');
        break;
    }
  }

  /// Helper function to fetch user data from a SAS server.
  Future<void> _fetchSasUserData(SasServerModel sasServer) async {
    print(
        '🔐 Attempting to log in to SAS server: ${sasServer.name} (${sasServer.host})');
    final sasApiService = SasApiService();
    final loggedIn = await sasApiService.loginWithCredentials(
      host: cleanHost(sasServer.host),
      username: sasServer.username,
      password: sasServer.password,
    );

    SasUser? sasUser;
    if (loggedIn) {
      print('✅ Successfully logged in to SAS: ${sasServer.host}');
      sasUser = await sasApiService.getSasUSerDetails(_subscriber!.username);
    } else {
      print('❌ Failed to log in to SAS server: ${sasServer.name}');
    }

    if (mounted) {
      setState(() {
        _sasUser = sasUser;
      });
    }
  }

  /// Helper function to fetch user data from an Earthlink server.
  Future<void> _fetchEarthlinkUserData(SasServerModel earthlinkServer) async {
    final earthlinkService = EarthlinkService();
    final loginResult = await earthlinkService.login(
      username: earthlinkServer.username,
      password: earthlinkServer.password,
    );

    if (loginResult['success']) {
      print('✅ Successfully logged in to Earthlink: ${earthlinkServer.name}');
      if (_subscriber!.earthlinkUserIndex != null) {
        final userIndex = int.tryParse(_subscriber!.earthlinkUserIndex!);
        if (userIndex != null) {
          final userDetailsResult =
              await earthlinkService.getUserDetails(userIndex: userIndex);
          if (userDetailsResult['success']) {
            final detailedUser =
                EarthlinkUser.fromJson(userDetailsResult['data']);
            final updatedSubscriber = _subscriber!.copyWith(
              adminId: _subscriber!.adminId,
              subscriptionEnd:
                  detailedUser.subscriptionEnd ?? _subscriber!.subscriptionEnd,
              isActive: detailedUser.isActive ?? _subscriber!.isActive,
              macAddress:
                  detailedUser.earthMaxMAC ?? _subscriber!.macAddress,
            );
            if (mounted) {
              setState(() {
                _subscriber = updatedSubscriber;
                _sasUser = null;
              });
            }
          }
        }
      }
    } else {
      print('❌ Failed to log in to Earthlink server.');
    }
  }

  Future<void> _fetchOnlineInfo() async {
    if (_subscriber == null || _subscriber!.username.isEmpty) return;

    setState(() => _loadingOnline = true);

    try {
      final SasServerModel? server = await DatabaseService().getConnectedServer();

      if (server == null) {
        if (mounted) {
          setState(() {
            _loadingOnline = false;
            _onlineInfo = {'error': 'لا يوجد خادم متصل'};
          });
        }
        return;
      }

      Map<String, dynamic>? onlineInfo;
      switch (server.serverType) {
        case ServerType.MikroTik:
          final mikrotikApi = MikrotikSyncService(server);
          // استخدام الدالة الجديدة للحصول على تفاصيل جلسة PPP كاملة
          final detailedSession =
              await mikrotikApi.getDetailedPppSession(_subscriber!.username);
          if (detailedSession != null) {
            onlineInfo = {'data': [detailedSession], 'type': 'mikrotik_detailed'};
          } else {
            onlineInfo = {'error': 'لم يتم العثور على معلومات المشترك في MikroTik'};
          }
          break;
        case ServerType.SAS:
          final sasApiService = SasApiService();
          final loggedIn = await sasApiService.loginWithCredentials(
            host: cleanHost(server.host),
            username: server.username,
            password: server.password,
          );
          if (loggedIn) {
            onlineInfo = await sasApiService.fetchOnlineUserInfo(
              token: sasApiService.token ?? '',
              username: _subscriber!.username,
              host: server.host,
            );
          } else {
            onlineInfo = {'error': 'فشل تسجيل الدخول إلى خادم SAS'};
          }
          break;
        case ServerType.Earthlink:
          final earthlinkService = EarthlinkService();
          final loginResult = await earthlinkService.login(
            username: server.username,
            password: server.password,
          );
          if (loginResult['success']) {
            final dashboardResult = await earthlinkService.getDashboardStats();
            if (dashboardResult['success']) {
              onlineInfo = {
                'isOnline': null,
                'stats': dashboardResult['data'],
                'source': 'Earthlink Dashboard',
              };
            }
          }
          onlineInfo ??= {'error': 'تعذر جلب معلومات الاتصال من Earthlink'};
          break;
      }

      if (mounted) {
        setState(() {
          _loadingOnline = false;
          _onlineInfo = onlineInfo ?? {'error': 'لا توجد معلومات اتصال'};
        });
      }
    } catch (e) {
      print('Error fetching online info: $e');
      if (mounted) {
        setState(() {
          _loadingOnline = false;
          _onlineInfo = {'error': 'حدث خطأ أثناء جلب معلومات الاتصال'};
        });
      }
    }
  }

  Future<void> _renewSubscription() async {
    if (_subscriber == null) return;

    // احصل على جميع الباقات المتوفرة
    final packages = await DatabaseService().getPackagesFire();
    print(packages);
    // افتح Dialog التجديد مباشرة، ودع المستخدم يختار الباقة من داخله
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => _RenewalDialog(
        subscriber: _subscriber!,
        package: _package, // قد تكون null إذا لم يكن للمشترك باقة
        allPackages: packages,
      ),
    );

    if (result == true) {
      await _loadData();
      // عرض رسالة نجاح التجديد
      if (mounted) {
        _showSafeSnackBar('تم تجديد اشتراك ${_subscriber?.fullName} بنجاح.');
      }
    }
  }

  Future<void> _recordPayment() async {
    if (_subscriber == null) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) =>
          _PaymentDialog(subscriber: _subscriber!, currentUser: _currentUser!),
    );

    if (result == true) {
      await _loadData();
    }
  }

  Future<void> _generatePaymentReminder() async {
    if (_subscriber == null) return;

    // جلب الباقة إذا لم تكن موجودة
    PackageModel? package = _package;
    if (package == null && _subscriber!.packageId.isNotEmpty) {
      try {
        package = await DatabaseService().getPackageById(_subscriber!.packageId);
      } catch (e) {
        print('Error fetching package: $e');
      }
    }

    // إذا لم نتمكن من جلب الباقة، استخدم باقة افتراضية
    if (package == null) {
      package = PackageModel(
        adminId: _subscriber!.adminId,
        id: 'default',
        name: 'باقة افتراضية',
        price: _subscriber!.debtAmount,
        durationInDays: 30,
        speed: 'غير محدد',
        deviceCount: 1,
        createdAt: DateTime.now(),
        serverId: '',
      );
    }

    if (!mounted) return;
    showDialog(
      context: context,
      builder: (context) =>
          _PaymentReminderDialog(subscriber: _subscriber!, package: package!),
    );
  }

  Future<void> _editSubscriber() async {
    if (_subscriber == null) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => _EditSubscriberDialog(subscriber: _subscriber!),
    );

    if (result == true) {
      await _loadData();
    }
  }

  Future<void> _addPreviousDebt() async {
    if (_subscriber == null || _currentUser == null) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => _PreviousDebtDialog(
        subscriber: _subscriber!,
        currentUser: _currentUser!,
      ),
    );

    if (result == true) {
      await _loadData();
    }
  }

  Future<void> _showEditDeviceCredentials() async {
    if (_networkDevice == null) return;

    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => _EditDeviceCredentialsDialog(
        device: _networkDevice!,
        currentUsername: _tempUsername,
        currentPassword: _tempPassword,
      ),
    );

    if (result != null) {
      setState(() {
        _tempUsername = result['username']!;
        _tempPassword = result['password']!;
      });

      // تحديث بيانات الجهاز في Firebase
      await _updateDeviceCredentials(result['username']!, result['password']!);

      // إعادة جلب معلومات الإشارة
      await _fetchSignalInfo();

      if (mounted) {
        _showSafeSnackBar('تم تحديث بيانات الدخول وإعادة جلب معلومات الإشارة');
      }
    }
  }

  Future<void> _updateDeviceCredentials(String username, String password) async {
    if (_networkDevice == null) return;

    try {
      final dbHelper = DatabaseHelper();

      // إنشاء جهاز محدث
      final updatedDevice = NetworkDevice(
        id: _networkDevice!.id,
        name: _networkDevice!.name,
        adminId: _networkDevice!.adminId,
        ipAddress: _networkDevice!.ipAddress,
        type: _networkDevice!.type,
        username: _networkDevice!.username,
        password: _networkDevice!.password,
        ubntUsername: username,
        ubntPassword: password,
      );

      await dbHelper.updateNetworkDevice(updatedDevice);

      setState(() {
        _networkDevice = updatedDevice;
      });
    } catch (e) {
      print('Error updating device credentials: $e');
      if (mounted) {
        _showSafeSnackBar('فشل في تحديث بيانات الدخول: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('تفاصيل المشترك')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_subscriber == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('خطأ')),
        body: const Center(child: Text('لم يتم العثور على المشترك')),
      );
    }

    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) => [
            _buildSliverAppBar(),
          ],
          body: Column(
            children: [
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildInfoTab(),
                    _buildPaymentsTab(),
                    _buildActivityTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildActionButtons(),
    );
  }

  Widget _buildSliverAppBar() {
    Color statusColor;
    String statusText = _subscriber!.subscriptionStatusText;
    IconData statusIcon;

    if (_subscriber!.isExpired) {
      statusColor = Theme.of(context).colorScheme.error;
      statusIcon = Icons.error_outline;
    } else if (_subscriber!.isExpiringSoon) {
      statusColor = Colors.orange;
      statusIcon = Icons.warning_amber;
    } else {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle_outline;
    }
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      actions: [
        // زر إرسال تنبيه انتهاء الاشتراك (يظهر فقط للمشتركين الذين ينتهي اشتراكهم قريباً)
        if (_subscriber!.isExpiringSoon)
          IconButton(
            icon: const Icon(Icons.notification_important),
            tooltip: 'إرسال تنبيه انتهاء الاشتراك',
            onPressed: _sendExpiryNotification,
          ),
        // زر تعديل بيانات الدخول للجهاز (أيقونة ربط صغيرة)
        IconButton(
          icon: Icon(
            _networkDevice != null ? Icons.link : Icons.link_off,
            size: 20,
            color: _networkDevice != null ? null : Colors.grey,
          ),
          tooltip: _getDeviceLinkTooltip(),
          onPressed: _networkDevice != null
            ? _showEditDeviceCredentials
            : _showNoDeviceMessage,
        ),
        // زر حذف المشترك
        IconButton(
          icon: const Icon(Icons.delete_outline),
          tooltip: 'حذف المشترك',
          onPressed: _showDeleteConfirmation,
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                statusColor.withOpacity(0.1),
                Theme.of(context).colorScheme.surface,
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(statusIcon, color: statusColor, size: 24),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _subscriber!.fullName,
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _subscriber!.phoneNumber,
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onSurface.withOpacity(0.7),
                                  ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          statusText,
                          style: Theme.of(context).textTheme.labelMedium
                              ?.copyWith(
                                color: statusColor,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Theme.of(context).colorScheme.primary,
        unselectedLabelColor: Theme.of(
          context,
        ).colorScheme.onSurface.withOpacity(0.6),
        indicatorColor: Theme.of(context).colorScheme.primary,
        tabs: const [
          Tab(text: 'المعلومات'),
          Tab(text: 'المدفوعات'),
          Tab(text: 'السجل'),
        ],
      ),
    );
  }

  Widget _buildInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض قسم الدين في أعلى الصفحة
          _buildDebtSection(),
          const SizedBox(height: 20),
          _buildInfoSection(
            title: 'معلومات الاشتراك',
            icon: Icons.inventory,
            children: [
              _buildInfoRow(
                'الباقة',
                _subscriber!.packageName,
              ), // Use the new packageName field
              _buildInfoRowWithWidget(
                'السعر',
                _package != null
                    ? CurrencyText(
                        amount: _package!.sellingPrice ?? _package!.price,
                      )
                    : const Text('غير محدد'),
              ),
              _buildInfoRow('المدة', _package?.durationDisplayText ?? 'غير متوفر'),
              _buildInfoRow('السرعة', _package?.speed ?? 'غير متوفر'),
              _buildInfoRow('عدد الأجهزة', _package != null ? '${_package!.deviceCount}' : 'غير متوفر'),
              _buildInfoRow(
                'تاريخ البداية',
                _formatDateTime(_subscriber!.subscriptionStart),
              ),
              _buildInfoRow(
                'تاريخ الانتهاء',
                _formatDateTime(_subscriber!.subscriptionEnd),
              ),
              _buildInfoRow(
                'الأيام المتبقية',
                _subscriber!.daysRemaining != null
                    ? '${_subscriber!.daysRemaining}'
                    : 'غير محدد',
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildInfoSection(
            title: 'معلومات الاتصال',
            icon: Icons.contact_phone,
            children: [
              _buildInfoRow('الاسم الكامل', _subscriber!.fullName),
              _buildInfoRowWithWidget(
                'رقم الهاتف',
                PhoneText(phoneNumber: _subscriber!.phoneNumber),
              ),
              _buildInfoRow('العنوان', _subscriber!.address),
            ],
          ),
          const SizedBox(height: 20),
          _buildInfoSection(
            title: 'المعلومات الفنية',
            icon: Icons.router,
            children: [
              _buildInfoRow(
                'نوع الاشتراك',
                _subscriber!.subscriptionTypeDisplayText,
              ),
              _buildInfoRow(
                'اسم المستخدم',
                _subscriber!.username.isEmpty
                    ? 'غير محدد'
                    : _subscriber!.username,
              ),
              _buildInfoRow(
                'كلمة المرور',
                _subscriber!.password.isEmpty
                    ? 'غير محدد'
                    : _subscriber!.password,
              ),
              _buildInfoRow(
                'عنوان MAC',
                _subscriber!.macAddress.isEmpty
                    ? 'غير محدد'
                    : _subscriber!.macAddress,
              ),
              _buildInfoRow(
                'اسم الراوتر',
                _subscriber!.routerName.isEmpty
                    ? 'غير محدد'
                    : _subscriber!.routerName,
              ),
              _buildInfoRow(
                'الملاحظات الفنية',
                _subscriber!.technicalNotes.isEmpty
                    ? 'لا توجد ملاحظات'
                    : _subscriber!.technicalNotes,
              ),
            ],
          ),
          const SizedBox(height: 20),
          // New section for SAS Radius Information
          if (_sasUser != null)
            _buildInfoSection(
              title: 'معلومات SAS Radius',
              icon: Icons.cloud,
              children: [
                _buildInfoRow('اسم المستخدم (SAS)', _sasUser!.username),
                _buildInfoRow("اسم الباقة", _package?.name ?? 'غير متوفر'),
                _buildInfoRowWithWidget(
                  'الرصيد (SAS)',
                  _sasUser!.balance != null
                      ? CurrencyText(amount: _sasUser!.balance!)
                      : const Text('غير متوفر'),
                ),
                _buildInfoRow('الحالة (SAS)', _sasUser!.status ?? 'غير متوفر'),
                _buildInfoRow('الاسم الكامل (SAS)', _sasUser!.fullName),
                _buildInfoRow(
                  'تاريخ الانتهاء',
                  _sasUser!.expiration.toString(),
                ),
                _buildInfoRow(
                  'البريد الالكترونى',
                  (_sasUser!.email != null)
                      ? _sasUser!.email.toString()
                      : 'غير متوفر',
                ),
                _buildInfoRow(
                  "العنوان",
                  (_sasUser!.address!.isNotEmpty)
                      ? _sasUser!.address.toString()
                      : 'غير متوفر',
                ),
                _buildInfoRow(
                  "المدينة",
                  (_sasUser!.city!.isNotEmpty)
                      ? _sasUser!.city.toString()
                      : 'غير متوفر',
                ),

                _buildInfoRowWithWidget(
                  'رقم الهاتف (SAS)',
                  _sasUser!.phone != null
                      ? PhoneText(phoneNumber: _sasUser!.phone!)
                      : const Text('غير متوفر'),
                ),
              ],
            ),
          const SizedBox(height: 16),
          const Text('معلومات الجلسة الحالية:', style: TextStyle(fontWeight: FontWeight.bold)),
          Row(
            children: [
              ElevatedButton(
                onPressed: _fetchOnlineInfo,
                child: const Text('تحديث حالة الاتصال'),
              ),
              const SizedBox(width: 8),
              if (_networkDevice != null)
                ElevatedButton.icon(
                  onPressed: _loadingSignal ? null : _fetchSignalInfo,
                  icon: _loadingSignal
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2)
                      )
                    : const Icon(Icons.refresh),
                  label: const Text('تحديث الإشارة'),
                ),
            ],
          ),
          buildOnlineInfo(),
          const SizedBox(height: 16),
          _buildSignalInfoSection(),
        ],
      ),
    );
  }

  Widget _buildPaymentsTab() {
    return Column(
      children: [
        // عرض حالة الدين (دائماً مرئي مع الألوان الجديدة)
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _subscriber!.debtStatusColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _subscriber!.debtStatusColor.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                _subscriber!.debtStatusIcon,
                color: _subscriber!.debtStatusColor,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _subscriber!.hasOutstandingDebt
                          ? 'إجمالي الدين المستحق'
                          : _subscriber!.hasAdvancePayment
                          ? 'رصيد مدفوع مقدماً'
                          : 'حالة الدين',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _subscriber!.debtStatusColor,
                      ),
                    ),
                    FutureBuilder<String>(
                      future: _subscriber!.hasOutstandingDebt
                          ? AppSettingsService.formatCurrency(
                              _subscriber!.debtAmount,
                            )
                          : _subscriber!.hasAdvancePayment
                          ? AppSettingsService.formatCurrency(
                              _subscriber!.advancePaymentAmount,
                            )
                          : Future.value('لا توجد ديون مستحقة'),
                      builder: (context, snapshot) {
                        return Text(
                          snapshot.data ?? '...',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: _subscriber!.debtStatusColor,
                              ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              // زر الدفع (متاح دائماً)
              ElevatedButton.icon(
                onPressed: _recordPayment,
                icon: const Icon(Icons.payment),
                label: Text(
                  _subscriber!.hasOutstandingDebt ? 'دفع' : 'إضافة دفعة',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _subscriber!.debtStatusColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: _paymentRecords.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.payment,
                        size: 64,
                        color: Theme.of(
                          context,
                        ).colorScheme.primary.withOpacity(0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد مدفوعات مسجلة',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: Theme.of(
                                context,
                              ).colorScheme.onSurface.withOpacity(0.6),
                            ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _paymentRecords.length,
                  itemBuilder: (context, index) {
                    final record = _paymentRecords[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(Icons.check_circle, color: Colors.green),
                        ),
                        title: FutureBuilder<String>(
                          future: AppSettingsService.formatCurrency(
                            record.amount,
                          ),
                          builder: (context, snapshot) {
                            return Text(
                              snapshot.data ?? '...',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          },
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('طريقة الدفع: ${record.paymentMethod}'),
                            Text(_formatDate(record.paymentDate)),
                            if (record.notes != null &&
                                record.notes!.isNotEmpty)
                              Text('ملاحظات: ${record.notes}'),
                          ],
                        ),
                        isThreeLine: true,
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildActivityTab() {
    return _activityLogs.isEmpty
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.history,
                  size: 64,
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'لا توجد أنشطة مسجلة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          )
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _activityLogs.length,
            itemBuilder: (context, index) {
              final log = _activityLogs[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getActivityIcon(log.action),
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  title: Text(
                    log.action,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(log.description),
                      Text(_formatDateTime(log.timestamp)),
                      if (log.amount > 0)
                        FutureBuilder<String>(
                          future: AppSettingsService.formatCurrency(log.amount),
                          builder: (context, snapshot) {
                            return Text(
                              'المبلغ: ${snapshot.data ?? '...'}',
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                  isThreeLine: true,
                ),
              );
            },
          );
  }

  Widget _buildInfoSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
    Color? iconColor,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(icon, color: iconColor ?? Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isCopyable = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            flex: 3,
            child: isCopyable && value != '-'
                ? GestureDetector(
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: value));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('تم نسخ $label: $value')),
                      );
                    },
                    child: Text(
                      value,
                      style: const TextStyle(
                        color: Colors.blue,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  )
                : Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRowWithWidget(String label, Widget valueWidget) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ),
          Expanded(child: valueWidget),
        ],
      ),
    );
  }

  Widget _buildDebtSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _subscriber!.debtStatusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _subscriber!.debtStatusColor.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _subscriber!.debtStatusIcon,
                color: _subscriber!.debtStatusColor,
              ),
              const SizedBox(width: 8),
              Text(
                _subscriber!.hasOutstandingDebt
                    ? 'دين مستحق'
                    : _subscriber!.hasAdvancePayment
                    ? 'رصيد مقدم'
                    : 'حالة الدين',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: _subscriber!.debtStatusColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _subscriber!.debtStatusText,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _subscriber!.debtStatusColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              if (_subscriber!.hasOutstandingDebt) ...[
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _generatePaymentReminder,
                    icon: const Icon(Icons.message),
                    label: const Text('رسالة تذكير'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: _subscriber!.debtStatusColor,
                      side: BorderSide(color: _subscriber!.debtStatusColor),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
              ],
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _recordPayment,
                  icon: const Icon(Icons.payment),
                  label: Text(
                    _subscriber!.hasOutstandingDebt
                        ? 'تسجيل دفعة'
                        : _subscriber!.hasAdvancePayment
                        ? 'إضافة رصيد'
                        : 'تسجيل دفعة',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _subscriber!.debtStatusColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // زر إضافة دين سابق
        FloatingActionButton(
          onPressed: _addPreviousDebt,
          backgroundColor: Theme.of(context).colorScheme.error,
          foregroundColor: Theme.of(context).colorScheme.onError,
          heroTag: 'add-debt',
          tooltip: 'إضافة دين سابق',
          child: const Icon(Icons.add_card),
        ),
        const SizedBox(height: 8),
        // زر تجديد الاشتراك
        FloatingActionButton(
          onPressed: _renewSubscription,
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          heroTag: 'renew',
          tooltip: 'تجديد الاشتراك',
          child: const Icon(Icons.refresh),
        ),
        const SizedBox(height: 8),
        // زر تعديل المشترك
        FloatingActionButton(
          onPressed: _editSubscriber,
          backgroundColor: Theme.of(context).colorScheme.secondary,
          foregroundColor: Theme.of(context).colorScheme.onSecondary,
          heroTag: 'edit',
          tooltip: 'تعديل المشترك',
          child: const Icon(Icons.edit),
        ),
      ],
    );
  }

  IconData _getActivityIcon(String action) {
    switch (action) {
      case 'إضافة مشترك':
        return Icons.person_add;
      case 'تجديد اشتراك':
        return Icons.refresh;
      case 'تسجيل دفعة':
        return Icons.payment;
      case 'تعديل بيانات':
        return Icons.edit;
      case 'إضافة دين سابق':
        return Icons.add_card;
      default:
        return Icons.history;
    }
  }

  static String _formatDate(DateTime? date) {
    if (date == null) return 'غير محدد';
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return 'غير محدد';
    return DateFormat('yyyy/MM/dd hh:mm a').format(dateTime);
  }

  // دالة إرسال تنبيه انتهاء الاشتراك
  void _sendExpiryNotification() async {
    try {
      // جلب الباقة إذا لم تكن موجودة
      PackageModel? package = _package;
      if (package == null && _subscriber!.packageId.isNotEmpty) {
        try {
          package = await DatabaseService().getPackageById(_subscriber!.packageId);
          if (package == null) {
            print('DEBUG: _sendExpiryNotification in subscriber_detail_page.dart: Package not found for _subscriber!.packageId: ${_subscriber!.packageId}. Using default package.');
          } else {
            print('DEBUG: _sendExpiryNotification in subscriber_detail_page.dart: Package found: ${package.name} for _subscriber!.packageId: ${_subscriber!.packageId}.');
          }
        } catch (e) {
          print('Error fetching package for expiry notification: $e');
        }
      }

      // إذا لم نتمكن من جلب الباقة، استخدم باقة افتراضية
      if (package == null) {
        package = PackageModel(
          adminId: _subscriber!.adminId,
          id: 'default',
          name: 'باقة افتراضية',
          price: 0.0,
          durationInDays: 30,
          speed: 'غير محدد',
          deviceCount: 1,
          createdAt: DateTime.now(),
          serverId: '',
        );
      }

      final messageService = MessageService();

      // إرسال رسالة انتهاء الاشتراك باستخدام القالب
      await messageService.sendExpiryReminder(_subscriber!);
      if (mounted) {
        _showSafeSnackBar('تم إرسال تنبيه انتهاء الاشتراك بنجاح');
      }
    } catch (e) {
      if (mounted) {
        _showSafeSnackBar('فشل في إرسال التنبيه: $e', isError: true);
      }
    }
  }

  // دالة إظهار تأكيد حذف المشترك
  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('هل أنت متأكد من حذف المشترك "${_subscriber!.fullName}"؟'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.warning, color: Colors.red),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'تحذير: لا يمكن التراجع عن هذا الإجراء. سيتم حذف جميع البيانات المرتبطة بالمشترك.',
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteSubscriber();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  // دالة حذف المشترك
  void _deleteSubscriber() async {
    // showDialog(
    //   context: context,
    //   barrierDismissible: false,
    //   builder: (BuildContext context) {
    //     return const AlertDialog(
    //       content: Row(
    //         children: [
    //           CircularProgressIndicator(),
    //           SizedBox(width: 16),
    //           Text('جاري حذف المشترك...'),
    //         ],
    //       ),
    //     );
    //   },
    // );

    try {
      await DatabaseService().deleteSubscriber(_subscriber!.id);

      if (mounted) {
        Navigator.of(context).pop(true); // العودة مع إشارة أن المشترك تم حذفه

        _showSafeSnackBar('تم حذف المشترك بنجاح');
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // إغلاق dialog التحميل

        _showSafeSnackBar('فشل في حذف المشترك: $e', isError: true);
      }
    }
  }

  

  /// Fetch detailed Earthlink information for the subscriber
  Future<void> _fetchEarthlinkDetailedInfo() async {
    if (_subscriber == null || _subscriber!.earthlinkUserIndex == null) return;
    
    try {
      print('📱 جلب معلومات مفصلة لمستخدم Earthlink: ${_subscriber!.earthlinkUserIndex}');
      
      // Try to get Earthlink server for this user
      final earthlinkServers = await FirebaseService().getSasServers();
      
      for (final serverMap in earthlinkServers) {
        final server = SasServerModel.fromMap(serverMap);
        if (server.serverType == ServerType.Earthlink) {
          final earthlinkService = EarthlinkService();
          final loginResult = await earthlinkService.login(
            username: server.username,
            password: server.password,
          );
          
          if (loginResult['success']) {
            print('✅ تم تسجيل الدخول بنجاح إلى Earthlink: ${server.name}');
            
            // Try to get more detailed user information
            if (_subscriber!.earthlinkUserIndex != null) {
              final userIndex = int.tryParse(_subscriber!.earthlinkUserIndex!);
              if (userIndex != null) {
                // Use the new detailed subscription info method
                if (_subscriber!.username.isNotEmpty) {
                  final detailedSubscriptionResult = await earthlinkService.getDetailedUserSubscriptionInfo(
                    userId: _subscriber!.username,
                  );
                  
                  if (detailedSubscriptionResult['success']) {
                    final subscriptionData = detailedSubscriptionResult['data'];
                    print('📊 معلومات الاشتراك المفصلة: $subscriptionData');
                    
                    // Extract and update subscriber information
                    DateTime? newSubscriptionEnd;
                    String? newPackageName;
                    double? newPackageCost;
                    int? newAccountIndex;
                    
                    if (subscriptionData is Map) {
                      // Enhanced field extraction with more comprehensive field names
                      List<String> expiryFields = [
                        'expiryDate', 'expireDate', 'expirationDate', 'endDate', 'subscriptionEnd',
                        'Expiration', 'ExpireDate', 'EndDate', 'ValidityEnd', 'validTo', 'expiry',
                        'ExpirationDate', 'expire_date', 'end_date', 'manualExpirationDate'
                      ];
                      for (String field in expiryFields) {
                        if (subscriptionData.containsKey(field) && subscriptionData[field] != null) {
                          try {
                            final expireDateStr = subscriptionData[field].toString();
                            newSubscriptionEnd = _parseDateString(expireDateStr);
                            if (newSubscriptionEnd != null) {
                              print('📅 تاريخ انتهاء جديد: $newSubscriptionEnd');
                              break;
                            }
                          } catch (e) {
                            print('❌ خطأ في تحليل تاريخ الانتهاء من $field: $e');
                          }
                        }
                        
                        // Also check nested objects for expiration dates
                        subscriptionData.forEach((key, value) {
                          if (value is Map && value.containsKey(field) && value[field] != null) {
                            try {
                              final expireDateStr = value[field].toString();
                              newSubscriptionEnd = _parseDateString(expireDateStr);
                              if (newSubscriptionEnd != null) {
                                print('📅 تاريخ انتهاء جديد من كائن متداخل $key.$field: $newSubscriptionEnd');
                                return; // Use return instead of break in forEach
                              }
                            } catch (e) {
                              print('❌ خطأ في تحليل تاريخ الانتهاء من كائن متداخل $key.$field: $e');
                            }
                          }
                        });
                        
                        if (newSubscriptionEnd != null) break;
                      }
                      
                      // Look for package name in various possible fields
                      List<String> packageFields = [
                        'accountName', 'packageName', 'planName', 'subscriptionType', 'AccountName',
                        'PlanName', 'ServiceName', 'serviceName', 'package', 'plan', 'accountType',
                        'AccountType', 'subscription_plan', 'service_plan'
                      ];
                      for (String field in packageFields) {
                        if (subscriptionData.containsKey(field) && subscriptionData[field] != null) {
                          newPackageName = subscriptionData[field].toString();
                          print('📦 اسم الباقة الجديد: $newPackageName');
                          break;
                        }
                        
                        // Also check nested objects for package names
                        bool foundPackage = false;
                        subscriptionData.forEach((key, value) {
                          if (value is Map && value.containsKey(field) && value[field] != null) {
                            newPackageName = value[field].toString();
                            print('📦 اسم الباقة الجديد من كائن متداخل $key.$field: $newPackageName');
                            foundPackage = true;
                            return; // Use return instead of break in forEach
                          }
                        });
                        
                        if (foundPackage) break;
                        if (newPackageName != null) break;
                      }
                      
                      // Look for package cost in various possible fields
                      List<String> costFields = [
                        'accountCost', 'cost', 'price', 'amount', 'salePrice', 'retailPrice',
                        'AccountCost', 'Price', 'Amount', 'monthlyFee', 'subscriptionFee',
                        'totalAmount', 'paymentAmount'
                      ];
                      for (String field in costFields) {
                        if (subscriptionData.containsKey(field) && subscriptionData[field] != null) {
                          try {
                            newPackageCost = double.parse(subscriptionData[field].toString());
                            print('💰 تكلفة الباقة الجديدة: $newPackageCost');
                            break;
                          } catch (e) {
                            print('❌ خطأ في تحليل تكلفة الباقة من $field: $e');
                          }
                        }
                        
                        // Also check nested objects for costs
                        bool foundCost = false;
                        subscriptionData.forEach((key, value) {
                          if (value is Map && value.containsKey(field) && value[field] != null) {
                            try {
                              newPackageCost = double.parse(value[field].toString());
                              print('💰 تكلفة الباقة الجديدة من كائن متداخل $key.$field: $newPackageCost');
                              foundCost = true;
                              return; // Use return instead of break in forEach
                            } catch (e) {
                              print('❌ خطأ في تحليل تكلفة الباقة من كائن متداخل $key.$field: $e');
                            }
                          }
                        });
                        
                        if (foundCost) break;
                        if (newPackageCost != null) break;
                      }
                      
                      // Look for account index in various possible fields
                      List<String> accountIndexFields = [
                        'accountIndex', 'AccountIndex', 'accountId', 'AccountId', 'profileId'
                      ];
                      for (String field in accountIndexFields) {
                        if (subscriptionData.containsKey(field) && subscriptionData[field] != null) {
                          try {
                            newAccountIndex = int.parse(subscriptionData[field].toString());
                            print('🔢 مؤشر الحساب الجديد: $newAccountIndex');
                            break;
                          } catch (e) {
                            print('❌ خطأ في تحليل مؤشر الحساب من $field: $e');
                          }
                        }
                        
                        // Also check nested objects for account indices
                        bool foundIndex = false;
                        subscriptionData.forEach((key, value) {
                          if (value is Map && value.containsKey(field) && value[field] != null) {
                            try {
                              newAccountIndex = int.parse(value[field].toString());
                              print('🔢 مؤشر الحساب الجديد من كائن متداخل $key.$field: $newAccountIndex');
                              foundIndex = true;
                              return; // Use return instead of break in forEach
                            } catch (e) {
                              print('❌ خطأ في تحليل مؤشر الحساب من كائن متداخل $key.$field: $e');
                            }
                          }
                        });
                        
                        if (foundIndex) break;
                        if (newAccountIndex != null) break;
                      }
                    }
                    
                    // Update the subscriber with new information
                    bool hasNewInfo = newSubscriptionEnd != null || newPackageName != null || newPackageCost != null || newAccountIndex != null;
                    if (hasNewInfo) {
                      if (!mounted) return;
                      setState(() {
                        if (newSubscriptionEnd != null) {
                          _subscriber = _subscriber!.copyWith(
                            adminId: _subscriber!.adminId,
                            subscriptionEnd: newSubscriptionEnd,
                          );
                        }

                        // Update package if we found package information
                        if (newPackageName != null || newPackageCost != null || newAccountIndex != null) {
                          // Try to find the matching package
                          PackageModel? matchingPackage;
                          
                          // First try to find by account index
                          if (newAccountIndex != null) {
                            try {
                              matchingPackage = _packages.firstWhere(
                                (p) => p.earthlinkAccountId == newAccountIndex.toString(),
                              );
                            } catch (e) {
                              matchingPackage = null;
                            }
                          }
                          
                          // If not found by index, try by name
                          if (matchingPackage == null && newPackageName != null) {
                            try {
                              matchingPackage = _packages.firstWhere(
                                (p) => p.name == newPackageName,
                              );
                            } catch (e) {
                              matchingPackage = null;
                            }
                          }

                          if (matchingPackage != null) {
                            // If we found a matching package, update both packageId and packageName
                            _package = matchingPackage;
                            _subscriber = _subscriber!.copyWith(
                              adminId: _subscriber!.adminId,
                              packageId: matchingPackage.id,
                              packageName: matchingPackage.name,
                            );
                          } else if (newPackageName != null) {
                            // If we couldn't find the exact package, still update the packageName
                            // but keep the existing packageId to maintain the relationship
                            _subscriber = _subscriber!.copyWith(
                              adminId: _subscriber!.adminId,
                              packageName: newPackageName,
                            );
                          }
                        }
                        
                        print('✅ تم تحديث معلومات المستخدم بنجاح');
                      });
                    } else {
                      print('ℹ️ لم يتم العثور على معلومات إضافية للاشتراك');
                    }
                  } else {
                    print('❌ فشل في جلب معلومات الاشتراك المفصلة: ${detailedSubscriptionResult['error']}');
                  }
                }
                
                // Even if we couldn't get detailed subscription info, try to get basic user details
                final userDetailsResult = await earthlinkService.getUserDetails(
                  userIndex: userIndex,
                );
                
                if (userDetailsResult['success']) {
                  final userDetails = userDetailsResult['data'];
                  print('👤 معلومات المستخدم الأساسية: $userDetails');
                  
                  // Extract MAC address if available
                  if (userDetails is Map && userDetails.containsKey('earthMaxMAC') && userDetails['earthMaxMAC'] != null) {
                    final macAddress = userDetails['earthMaxMAC'].toString();
                    if (!mounted) return;
                    setState(() {
                      _subscriber = _subscriber!.copyWith(
                        adminId: _subscriber!.adminId,
                        macAddress: macAddress,
                      );
                      print('✅ تم تحديث عنوان MAC: $macAddress');
                    });
                  }
                }
                
                // Try to get user profile information as another fallback
                if (_subscriber!.username.isNotEmpty) {
                  final userProfileResult = await earthlinkService.getUserProfile(
                    userId: _subscriber!.username,
                  );
                  
                  if (userProfileResult['success']) {
                    final profileData = userProfileResult['data'];
                    print('👤 معلومات ملف المستخدم: $profileData');
                    
                    // Extract additional information if available
                    if (profileData is Map) {
                      // Extract MAC address if available
                      if (profileData.containsKey('earthMaxMAC') && profileData['earthMaxMAC'] != null) {
                        final macAddress = profileData['earthMaxMAC'].toString();
                        if (!mounted) return;
                        setState(() {
                          _subscriber = _subscriber!.copyWith(
                            adminId: _subscriber!.adminId,
                            macAddress: macAddress,
                          );
                          print('✅ تم تحديث عنوان MAC من ملف المستخدم: $macAddress');
                        });
                      }
                      
                      // Extract other information
                      List<String> nameFields = ['firstName', 'lastName', 'displayName', 'fullName'];
                      bool nameUpdated = false;
                      for (String field in nameFields) {
                        if (profileData.containsKey(field) && profileData[field] != null) {
                          final fullName = profileData[field].toString();
                          if (fullName.isNotEmpty && fullName != 'null') {
                            if (!mounted) return;
                            setState(() {
                              _subscriber = _subscriber!.copyWith(
                                adminId: _subscriber!.adminId,
                                fullName: fullName,
                              );
                              print('✅ تم تحديث الاسم الكامل من ملف المستخدم: $fullName');
                            });
                            nameUpdated = true;
                            break;
                          }
                        }
                      }
                      
                      List<String> phoneFields = ['mobileNumber', 'phoneNumber', 'phone', 'mobilePhone'];
                      bool phoneUpdated = false;
                      for (String field in phoneFields) {
                        if (profileData.containsKey(field) && profileData[field] != null) {
                          final phoneNumber = profileData[field].toString();
                          if (phoneNumber.isNotEmpty && phoneNumber != 'null') {
                            if (!mounted) return;
                            setState(() {
                              _subscriber = _subscriber!.copyWith(
                                adminId: _subscriber!.adminId,
                                phoneNumber: phoneNumber,
                              );
                              print('✅ تم تحديث رقم الهاتف من ملف المستخدم: $phoneNumber');
                            });
                            phoneUpdated = true;
                            break;
                          }
                        }
                      }
                      
                      List<String> addressFields = ['address', 'street', 'city', 'state', 'country'];
                      List<String> addressValues = [];
                      for (String field in addressFields) {
                        if (profileData.containsKey(field) && profileData[field] != null) {
                          final addressPart = profileData[field].toString();
                          if (addressPart.isNotEmpty && addressPart != 'null') {
                            addressValues.add(addressPart);
                          }
                        }
                      }
                      if (addressValues.isNotEmpty) {
                        final address = addressValues.join(', ');
                        if (!mounted) return;
                        setState(() {
                          _subscriber = _subscriber!.copyWith(
                            adminId: _subscriber!.adminId,
                            address: address,
                          );
                          print('✅ تم تحديث العنوان من ملف المستخدم: $address');
                        });
                      }
                    }
                  }
                }
                
                // Try to get invoices information which might contain package details
                if (_subscriber!.username.isNotEmpty) {
                  final invoicesResult = await earthlinkService.getUsersInvoices(
                    query: _subscriber!.username,
                  );
                  
                  if (invoicesResult['success']) {
                    final invoicesData = invoicesResult['data'];
                    print('🧾 معلومات فواتير المستخدم: $invoicesData');
                    
                    if (invoicesData is Map && 
                        invoicesData.containsKey('value') && 
                        invoicesData['value'] is Map &&
                        invoicesData['value'].containsKey('itemsList') && 
                        invoicesData['value']['itemsList'] is List) {
                      
                      final itemsList = invoicesData['value']['itemsList'] as List;
                      if (itemsList.isNotEmpty) {
                        // Get the most recent invoice
                        final latestInvoice = itemsList.first;
                        if (latestInvoice is Map) {
                          // Extract package information from invoice
                          if (latestInvoice.containsKey('accountName') && latestInvoice['accountName'] != null) {
                            final packageName = latestInvoice['accountName'].toString();
                            if (!mounted) return;
                            setState(() {
                              _subscriber = _subscriber!.copyWith(
                                adminId: _subscriber!.adminId,
                                packageName: packageName,
                              );
                              print('✅ تم تحديث اسم الباقة من الفاتورة: $packageName');
                              
                              // Try to find matching package
                              try {
                                final matchingPackage = _packages.firstWhere(
                                  (p) => p.name == packageName,
                                );
                                _package = matchingPackage;
                                _subscriber = _subscriber!.copyWith(
                                  adminId: _subscriber!.adminId,
                                  packageId: matchingPackage.id,
                                );
                                print('✅ تم ربط الباقة من الفاتورة: ${matchingPackage.name}');
                              } catch (e) {
                                print('ℹ️ لم يتم العثور على باقة مطابقة للاسم: $packageName');
                              }
                            });
                          }
                          
                          if (latestInvoice.containsKey('salePrice') && latestInvoice['salePrice'] != null) {
                            try {
                              final packageCost = double.parse(latestInvoice['salePrice'].toString());
                              if (!mounted) return;
                              setState(() {
                                // Update technical notes with package cost
                                final updatedNotes = _subscriber!.technicalNotes.contains('تكلفة الحساب')
                                    ? _subscriber!.technicalNotes.replaceAll(RegExp(r'تكلفة الحساب: \$[\d.]+'), 'تكلفة الحساب: \$$packageCost')
                                    : '${_subscriber!.technicalNotes}\nتكلفة الحساب: \$$packageCost';
                                
                                _subscriber = _subscriber!.copyWith(
                                  adminId: _subscriber!.adminId,
                                  technicalNotes: updatedNotes,
                                );
                                print('✅ تم تحديث تكلفة الباقة من الفاتورة: $packageCost');
                              });
                            } catch (e) {
                              print('❌ خطأ في تحليل تكلفة الباقة من الفاتورة: $e');
                            }
                          }
                        }
                      }
                    }
                  }
                }
                
                break; // Exit the server loop once we've successfully processed
              }
            }
          } else {
            print('❌ فشل تسجيل الدخول إلى Earthlink: ${loginResult['error']}');
          }
        }
      }
    } catch (e) {
      print('Error fetching Earthlink detailed info: $e');
    }
  }
  
  /// Parse date string in various formats
  DateTime? _parseDateString(String dateStr) {
    try {
      // Handle different date formats
      if (dateStr.contains('T')) {
        // ISO format like "2024-11-29T17:21:00"
        return DateTime.parse(dateStr);
      } else if (dateStr.contains('/')) {
        // Handle format like "29/11/2024 05:21 PM" or "11/29/2024"
        final parts = dateStr.split(' ');
        if (parts.length >= 1) {
          final datePart = parts[0];
          final dateParts = datePart.split('/');
          if (dateParts.length == 3) {
            // Try to determine if it's DD/MM/YYYY or MM/DD/YYYY
            // We'll assume DD/MM/YYYY format based on common Middle Eastern usage
            final day = int.parse(dateParts[0]);
            final month = int.parse(dateParts[1]);
            final year = int.parse(dateParts[2]);
            
            // Validate the date parts
            if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year > 2000) {
              return DateTime(year, month, day);
            }
          }
        }
      } else if (dateStr.contains('-')) {
        // Handle format like "2024-11-29"
        final dateParts = dateStr.split('-');
        if (dateParts.length == 3) {
          final year = int.parse(dateParts[0]);
          final month = int.parse(dateParts[1]);
          final day = int.parse(dateParts[2]);
          return DateTime(year, month, day);
        }
      } else {
        // Try parsing as YYYY-MM-DD format
        return DateTime.parse(dateStr.split(' ')[0]);
      }
    } catch (e) {
      print('Error parsing date string "$dateStr": $e');
    }
    return null;
  }

  Future<void> _fetchNetworkDevice() async {
    if (_onlineInfo == null ||
        _onlineInfo!['data'] == null ||
        _onlineInfo!['data'].isEmpty) return;

    try {
      final session = _onlineInfo!['data'][0];
      String? ipAddress;

      // استخراج عنوان IP حسب نوع البيانات
      if (_onlineInfo!['type'] == 'mikrotik_detailed') {
        final active = session['active'] as Map<String, dynamic>?;
        ipAddress = active?['address']?.toString();
      } else {
        ipAddress = session['framedipaddress']?.toString();
      }

      if (ipAddress != null && ipAddress.isNotEmpty) {
        final dbHelper = DatabaseHelper();
        final device = await dbHelper.getNetworkDeviceByIpFire(ipAddress);

        if (device != null) {
          // إذا كان IP الجهاز في قاعدة البيانات مختلف عن IP الجلسة، حدثه
          if (device.ipAddress != ipAddress) {
            final updatedDevice = device.copyWith(ipAddress: ipAddress);
            await dbHelper.updateNetworkDevice(updatedDevice);
          }
          if (!mounted) return;
          setState(() {
            _networkDevice = device.copyWith(ipAddress: ipAddress);
            // تعيين بيانات الدخول المؤقتة
            _tempUsername = device.ubntUsername ?? device.username;
            _tempPassword = device.ubntPassword ?? device.password;
          });

          // جلب معلومات الإشارة تلقائياً
          await _fetchSignalInfo();
        }
      }
    } catch (e) {
      print('Error fetching network device: $e');
    }
  }

  Future<void> _fetchSignalInfo() async {
    if (_networkDevice == null) return;

    if (!mounted) return;
    setState(() => _loadingSignal = true);
    try {
      final deviceService = NetworkDeviceService();

      // إنشاء جهاز مؤقت ببيانات الدخول المحدثة
      final tempDevice = NetworkDevice(
        id: _networkDevice!.id,
        name: _networkDevice!.name,
        adminId: _networkDevice!.adminId,
        ipAddress: _networkDevice!.ipAddress,
        type: _networkDevice!.type,
        username: _tempUsername,
        password: _tempPassword,
      );

      final signalInfo = await deviceService.getDeviceInfo(tempDevice);

      if (!mounted) return;
      setState(() {
        _ubntSignalInfo = signalInfo;
      });
    } catch (e) {
      print('Error fetching signal info: $e');
      if (!mounted) return;
      setState(() {
        _ubntSignalInfo = null;
      });
    } finally {
      if (!mounted) return;
    setState(() => _loadingSignal = false);
    }
  }

  Widget buildOnlineInfo() {
    print('🖥️ عرض معلومات الجلسة - Loading: $_loadingOnline, OnlineInfo: $_onlineInfo');

    if (_loadingOnline) {
      return Center(child: CircularProgressIndicator());
    }

    if (_onlineInfo != null && _onlineInfo!['data'] != null && _onlineInfo!['data'].isNotEmpty) {
      print('✅ يوجد معلومات جلسة - عدد الجلسات: ${_onlineInfo!['data'].length}');
      final session = _onlineInfo!['data'][0];
      print('📱 بيانات الجلسة: $session');

      // التحقق من نوع البيانات (MikroTik مفصل أم SAS)
      final bool isMikrotikDetailed = _onlineInfo!['type'] == 'mikrotik_detailed';

      if (isMikrotikDetailed) {
        return _buildMikrotikDetailedInfo(session);
      } else {
        // عرض SAS أو MikroTik العادي
        return _buildStandardSessionInfo(session);
      }
    } else {
      print('❌ لا توجد معلومات جلسة - OnlineInfo: $_onlineInfo');
      return Card(
        margin: EdgeInsets.all(8),
        child: Padding(
          padding: EdgeInsets.all(12),
          child: Column(
            children: [
              Icon(Icons.wifi_off, size: 48, color: Colors.grey),
              SizedBox(height: 8),
              Text(
                _onlineInfo?['error'] ?? 'لا يوجد جلسة حالية للمشترك',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _fetchOnlineInfo,
                icon: Icon(Icons.refresh),
                label: Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildMikrotikDetailedInfo(Map<String, dynamic> sessionData) {
    final secret = sessionData['secret'] as Map<String, dynamic>?;
    final active = sessionData['active'] as Map<String, dynamic>?;
    final profile = sessionData['profile'] as Map<String, dynamic>?;
    final isOnline = sessionData['isOnline'] as bool? ?? false;

    return Column(
      children: [
        // معلومات PPP Secret
        if (secret != null) _buildMikrotikSecretCard(secret),

        // معلومات الجلسة النشطة
        if (active != null) _buildMikrotikActiveCard(active),

        // معلومات البروفايل
        if (profile != null) _buildMikrotikProfileCard(profile),

        // حالة الاتصال
        _buildConnectionStatusCard(isOnline),
      ],
    );
  }

  Widget _buildMikrotikSecretCard(Map<String, dynamic> secret) {
    return Card(
      margin: EdgeInsets.all(8),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.vpn_key, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'معلومات PPP Secret',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            Divider(),
            _buildInfoRow('اسم المستخدم', secret['name'] ?? 'غير محدد'),
            _buildInfoRow('البروفايل', secret['profile'] ?? 'غير محدد'),
            _buildInfoRow('الخدمة', secret['service'] ?? 'غير محدد'),
            _buildInfoRow('الحالة', secret['disabled'] == 'true' ? 'معطل' : 'مفعل'),
            _buildInfoRow('التعليق', secret['comment'] ?? 'لا توجد ملاحظات'),
          ],
        ),
      ),
    );
  }

  Widget _buildMikrotikActiveCard(Map<String, dynamic> active) {
    return Card(
      margin: EdgeInsets.all(8),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.wifi, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  'الجلسة النشطة',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            Divider(),
            _buildInfoRow('عنوان IP', active['address'] ?? 'غير محدد', isCopyable: true),
            _buildInfoRow('مدة الاتصال', active['uptime'] ?? 'غير محدد'),
            _buildInfoRow('معرف الجلسة', active['session-id'] ?? 'غير محدد'),
            _buildInfoRow('Caller ID', active['caller-id'] ?? 'غير محدد'),
            _buildInfoRow('التشفير', active['encoding'] ?? 'غير محدد'),
            _buildInfoRow('البيانات الواردة', _formatBytes(active['bytes-in'])),
            _buildInfoRow('البيانات الصادرة', _formatBytes(active['bytes-out'])),
            _buildInfoRow('الحزم الواردة', active['packets-in'] ?? '0'),
            _buildInfoRow('الحزم الصادرة', active['packets-out'] ?? '0'),
          ],
        ),
      ),
    );
  }

  Widget _buildMikrotikProfileCard(Map<String, dynamic> profile) {
    return Card(
      margin: EdgeInsets.all(8),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'إعدادات البروفايل',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            Divider(),
            _buildInfoRow('اسم البروفايل', profile['name'] ?? 'غير محدد'),
            _buildInfoRow('حد السرعة', profile['rate-limit'] ?? 'غير محدود'),
            _buildInfoRow('مهلة الجلسة', profile['session-timeout'] ?? 'غير محدود'),
            _buildInfoRow('مهلة الخمول', profile['idle-timeout'] ?? 'غير محدود'),
            _buildInfoRow('خادم DNS', profile['dns-server'] ?? 'غير محدد'),
            _buildInfoRow('قائمة العناوين', profile['address-list'] ?? 'غير محدد'),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionStatusCard(bool isOnline) {
    return Card(
      margin: EdgeInsets.all(8),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Row(
          children: [
            Icon(
              isOnline ? Icons.check_circle : Icons.cancel,
              color: isOnline ? Colors.green : Colors.red,
              size: 24,
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                isOnline ? 'المشترك متصل حالياً' : 'المشترك غير متصل',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isOnline ? Colors.green : Colors.red,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStandardSessionInfo(Map<String, dynamic> session) {
    String? packageName;
    final profileId = session['profile_id']?.toString() ?? session['sasProfileId']?.toString();
    if (profileId != null && profileId.isNotEmpty && _packages.isNotEmpty) {
      final found = _packages.firstWhere(
        (p) => p.sasProfileId == profileId || p.id == profileId,
        orElse: () => PackageModel(
          id: 'unknown',
          name: 'غير متوفر',
          price: 0,
          durationInDays: 0,
          speed: '',
          deviceCount: 0,
          adminId: '',
          serverId: '',
          createdAt: DateTime.now(),
        ),
      );
      packageName = found.name;
    }

    // تنسيق مدة الجلسة
    String sessionDuration = '-';
    if (session['acctsessiontime'] != null) {
      final seconds = int.tryParse(session['acctsessiontime'].toString()) ?? 0;
      sessionDuration = formatDuration(seconds);
    }

    return Card(
      margin: EdgeInsets.all(8),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow(
              'IP',
              session['framedipaddress'] ?? '-',
              isCopyable: true,
            ),
            _buildInfoRow('مدة الجلسة', sessionDuration),
            _buildInfoRow('وقت البدء', session['acctstarttime']?.toString() ?? '-'),
            _buildInfoRow('آخر ظهور', session['last_online']?.toString() ?? '-'),
            _buildInfoRow('اسم الباقة (الجلسة)', packageName ?? 'غير متوفر'),
            _buildInfoRow('البيانات المستلمة (RX)', formatBytes(session['acctinputoctets'])),
            _buildInfoRow('البيانات المرسلة (TX)', formatBytes(session['acctoutputoctets']))
          ],
        ),
      ),
    );
  }

  String _formatBytes(dynamic bytes) {
    if (bytes == null) return '0 B';
    final int bytesInt = int.tryParse(bytes.toString()) ?? 0;
    return formatBytes(bytesInt);
  }

  Widget _buildSignalInfoSection() {
    if (_networkDevice == null) {
      return Card(
        margin: const EdgeInsets.all(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                Icons.device_unknown,
                size: 48,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 8),
              Text(
                'لم يتم العثور على جهاز مرتبط بهذا المشترك',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'تأكد من وجود جهاز بنفس IP الجلسة في قائمة الأجهزة',
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    if (_loadingSignal) {
      return Card(
        margin: const EdgeInsets.all(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'جاري جلب معلومات الإشارة...', 
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_ubntSignalInfo == null) {
      return Card(
        margin: const EdgeInsets.all(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                Icons.signal_wifi_off,
                size: 48,
                color: Colors.red.shade400,
              ),
              const SizedBox(height: 8),
              Text(
                'فشل في جلب معلومات الإشارة',
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'تحقق من بيانات الدخول للجهاز',
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _showEditDeviceCredentials,
                icon: const Icon(Icons.settings),
                label: const Text('تعديل بيانات الدخول'),
              ),
            ],
          ),
        ),
      );
    }

    return buildUbntSignalInfo(_ubntSignalInfo!);
  }

  String _getDeviceLinkTooltip() {
    if (_networkDevice != null) {
      return 'تعديل بيانات دخول الجهاز';
    } else if (_onlineInfo == null || _onlineInfo!['data'] == null || _onlineInfo!['data'].isEmpty) {
      return 'المشترك غير متصل حالياً';
    } else {
      final session = _onlineInfo!['data'][0];
      String? ipAddress;

      // استخراج عنوان IP حسب نوع البيانات
      if (_onlineInfo!['type'] == 'mikrotik_detailed') {
        final active = session['active'] as Map<String, dynamic>?;
        ipAddress = active?['address']?.toString();
      } else {
        ipAddress = session['framedipaddress']?.toString();
      }

      return 'لا يوجد جهاز مطابق للـ IP: ${ipAddress ?? 'غير محدد'}';
    }
  }

  void _showNoDeviceMessage() {
    String message;
    String title;
    IconData icon;
    Color color;

    if (_onlineInfo == null || _onlineInfo!['data'] == null || _onlineInfo!['data'].isEmpty) {
      title = 'المشترك غير متصل';
      message = 'المشترك غير متصل حالياً. لا يمكن تحديد الجهاز الشبكي.';
      icon = Icons.wifi_off;
      color = Colors.orange;
    } else {
      final session = _onlineInfo!['data'][0];
      String? ipAddress;

      // استخراج عنوان IP حسب نوع البيانات
      if (_onlineInfo!['type'] == 'mikrotik_detailed') {
        final active = session['active'] as Map<String, dynamic>?;
        ipAddress = active?['address']?.toString();
      } else {
        ipAddress = session['framedipaddress']?.toString();
      }

      title = 'لا يوجد جهاز مطابق';
      message = 'لا يوجد جهاز شبكي مطابق للـ IP: ${ipAddress ?? 'غير محدد'}\n\nيمكنك إضافة الجهاز مباشرة.';
      icon = Icons.router;
      color = Colors.red;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(icon, color: color),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
          if (_onlineInfo != null && _onlineInfo!['data'] != null && _onlineInfo!['data'].isNotEmpty)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                final session = _onlineInfo!['data'][0];
                String? ipAddress;

                // استخراج عنوان IP حسب نوع البيانات
                if (_onlineInfo!['type'] == 'mikrotik_detailed') {
                  final active = session['active'] as Map<String, dynamic>?;
                  ipAddress = active?['address']?.toString();
                } else {
                  ipAddress = session['framedipaddress']?.toString();
                }

                final subscriberName = _subscriber?.fullName ?? 'جهاز مشترك';
                _showQuickAddDeviceDialog(ipAddress ?? '', subscriberName);
              },
              icon: const Icon(Icons.add, size: 18),
              label: const Text('إضافة جهاز'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
        ],
      ),
    );
  }

  Widget buildUbntSignalInfo(Map<String, String> ubntInfo) {
    Color getSignalColor(String? value) {
      if (value == null) return Colors.grey;
      final match = RegExp(r'-?\d+').firstMatch(value);
      if (match == null) return Colors.grey;
      final dbm = int.tryParse(match.group(0)!);
      if (dbm == null) return Colors.grey;
      if (dbm >= -60) return Colors.green;
      if (dbm >= -75) return Colors.orange;
      return Colors.red;
    }

    // استخدام نفس تصميم _buildInfoRow للتوافق مع باقي الأقسام
    return _buildInfoSection(
      title: 'معلومات الإشارة',
      icon: Icons.network_check,
      iconColor: getSignalColor(ubntInfo['signalStrength']),
      children: [
        // عرض قوة الإشارة مع الألوان
        _buildInfoRowWithWidget(
          'قوة الإشارة',
          Row(
            children: [
              Icon(
                Icons.signal_cellular_alt,
                color: getSignalColor(ubntInfo['signalStrength']),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                ubntInfo['signalStrength'] ?? '-',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: getSignalColor(ubntInfo['signalStrength']),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                getSignalColor(ubntInfo['signalStrength']) == Colors.green
                  ? '(جيدة)'
                  : getSignalColor(ubntInfo['signalStrength']) == Colors.orange
                    ? '(متوسطة)'
                    : '(ضعيفة)',
                style: TextStyle(
                  fontSize: 12,
                  color: getSignalColor(ubntInfo['signalStrength']),
                ),
              ),
            ],
          ),
        ),
        _buildInfoRow('SNR', ubntInfo['snr'] ?? '-'),
        _buildInfoRow('Noise', ubntInfo['noise'] ?? '-'),
        _buildInfoRow('SSID', ubntInfo['ssid'] ?? '-'),
        _buildInfoRow('سرعة الإرسال', ubntInfo['txRate'] ?? '-'),
        _buildInfoRow('سرعة الاستقبال', ubntInfo['rxRate'] ?? '-'),
        _buildInfoRow('مدة التشغيل', ubntInfo['uptime'] ?? '-'),
        _buildInfoRow('اسم الجهاز', ubntInfo['deviceName'] ?? '-'),
        _buildInfoRow('الموديل', ubntInfo['deviceModel'] ?? '-'),
        _buildInfoRow('MAC', ubntInfo['macAddress'] ?? '-'),
        _buildInfoRow('إصدار السوفتوير', ubntInfo['firmwareVersion'] ?? '-'),
        _buildInfoRow('وضع الشبكة', ubntInfo['networkMode'] ?? '-'),
        _buildInfoRow('وضع الواي فاي', ubntInfo['wirelessMode'] ?? '-'),
        _buildInfoRow('سرعة LAN', ubntInfo['lanSpeed'] ?? '-'),
      ],
    );
  }





  void _showQuickAddDeviceDialog(String ip, String subscriberName) async {
    String username = '';
    String password = '';
    String type = 'UBNT';
    final dbHelper = DatabaseHelper();

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة جهاز جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('سيتم إضافة الجهاز باسم المشترك: $subscriberName'),
            Text('IP: $ip'),
            TextField(
              decoration: InputDecoration(labelText: 'اسم المستخدم'),
              onChanged: (v) => username = v,
            ),
            TextField(
              decoration: InputDecoration(labelText: 'كلمة المرور'),
              obscureText: true,
              onChanged: (v) => password = v,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final newDevice = NetworkDevice(
                name: subscriberName,
                adminId: DatabaseService().adminId,
                ipAddress: ip,
                type: type,
                username: username,
                password: password,
              );
              await dbHelper.insertNetworkDevice(newDevice);
              Navigator.pop(context);
              await _fetchNetworkDevice();
            },
            child: Text('إضافة'),
          ),
        ],
      ),
    );
  }
}

class _RenewalDialog extends StatefulWidget {
  final SubscriberModel subscriber;
  final PackageModel? package;
  final List<PackageModel> allPackages;

  const _RenewalDialog({
    required this.subscriber,
    required this.package,
    required this.allPackages,
  });

  @override
  State<_RenewalDialog> createState() => _RenewalDialogState();
}

class _RenewalDialogState extends State<_RenewalDialog> {
  bool _isLoading = false;
  bool? _isPaid; // Changed to nullable bool
  bool? _sendMessage; // Changed to nullable bool
  PrinterSettingsModel? _printerSettings;
  bool _printerEnabled = false;
  bool _printerLoading = true;
  late String _selectedPackageId;
  late PackageModel _selectedPackage;
  final _notesController = TextEditingController();
  final _amountController = TextEditingController();
  late String _paymentMethod; // Changed to late
  final List<String> _paymentMethods = [
    'نقداً',
    'كاش آسيا',
    'زين كاش',
    'آسيا حوالة',
    'حوالة بنكية',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    // Initialize nullable booleans
    _isPaid = false;
    _sendMessage = false;
    _paymentMethod = _paymentMethods.first; // Initialize payment method

    // Filter packages to avoid duplicates and empty IDs
    final validPackages = widget.allPackages
        .where((package) => package.id.isNotEmpty)
        .toSet()
        .toList();

    // إذا كانت الباقة غير محددة أو غير صالحة، اختر أول باقة صالحة من القائمة
    if (widget.package != null &&
        validPackages.any((p) => p.id == widget.package!.id)) {
      _selectedPackage = widget.package!;
    } else {
      _selectedPackage = validPackages.isNotEmpty
          ? validPackages.first
          : widget.allPackages.first;
    }
    _selectedPackageId = _selectedPackage.id;
    _amountController.text =
        (_selectedPackage.sellingPrice ?? _selectedPackage.price).toString();
    _loadPrinterSettings();
  }

  // دالة مساعدة لإظهار الرسائل بشكل آمن
  void _showSafeSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : null,
        ),
      );
    } catch (scaffoldError) {
      print(message);
      // إظهار Toast كبديل
      Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: isError ? Colors.red : Colors.green,
        textColor: Colors.white,
      );
    }
  }

  Future<void> _loadPrinterSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final json = prefs.getString('printer_settings');
    if (json != null) {
      final settings = PrinterSettingsModel.fromJson(jsonDecode(json));
      setState(() {
        _printerSettings = settings;
        _printerEnabled =
            settings.connectedDeviceAddress != null &&
            settings.connectedDeviceAddress!.isNotEmpty;
        _printerLoading = false;
      });
    } else {
      setState(() {
        _printerSettings = null;
        _printerEnabled = false;
        _printerLoading = false;
      });
    }
  }

  Future<void> _printReceipt() async {
    if (_printerSettings == null) return;
    try {
      // إظهار رسالة تحميل فقط إذا كان الwidget مازال موجوداً
      if (mounted) {
        try {
          _showSafeSnackBar('جاري الطباعة...');
        } catch (scaffoldError) {
          print('جاري الطباعة...');
        }
      }

      final now = DateTime.now();
      final data = {
        'subscriberName': widget.subscriber.fullName,
        'subscriptionNumber': widget.subscriber.id,
        'dateTime':
            '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} ${now.hour}:${now.minute.toString().padLeft(2, '0')}',
        'paymentAmount':
            _selectedPackage.sellingPrice ?? _selectedPackage.price,
        'operationType': 'تجديد اشتراك',
        'employeeName': '',
        'companyInfo': _printerSettings?.companyName ?? '',
      };

      // طباعة الإيصال بالصورة (حل مشاكل التشفير العربي)
      await PrinterService.printReceiptUnified(
        settings: _printerSettings!,
        data: data,
        operationType: 'تجديد',
        asImage: true, // استخدام الطباعة بالصورة دائماً
      );
      // رسالة نجاح فقط إذا كان الwidget مازال موجوداً
      if (mounted) {
        try {
          _showSafeSnackBar('تم طباعة إيصال التجديد بنجاح');
        } catch (scaffoldError) {
          print('تم طباعة إيصال التجديد بنجاح');
        }
      }
    } catch (e) {
      // رسالة خطأ فقط إذا كان الwidget مازال موجوداً
      if (mounted) {
        try {
          _showSafeSnackBar('خطأ في الطباعة: $e', isError: true);
        } catch (scaffoldError) {
          print('خطأ في الطباعة: $e');
        }
      }
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تجديد الاشتراك'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تجديد اشتراك: ${widget.subscriber.fullName}'),
            const SizedBox(height: 12),
            // Package selection dropdown
            DropdownButtonFormField<String>(
              value:
                  (widget.allPackages
                          .where((package) => package.id == _selectedPackageId)
                          .length ==
                      1)
                  ? _selectedPackageId
                  : null,
              decoration: const InputDecoration(
                labelText: 'الباقة',
                border: OutlineInputBorder(),
              ),
              isExpanded: true,
              items: widget.allPackages
                  .where((package) => package.id.isNotEmpty)
                  .toSet()
                  .map((package) {
                    return DropdownMenuItem(
                      value: package.id,
                      child: PackageDisplayText(
                        package: package,
                        style: const TextStyle(overflow: TextOverflow.ellipsis),
                      ),
                    );
                  })
                  .toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedPackageId = value;
                    _selectedPackage = widget.allPackages.firstWhere(
                      (p) => p.id == value,
                    );
                  });
                }
              },
            ),
            const SizedBox(height: 12),
            Text('المدة: ${_selectedPackage.durationDisplayText}'),
            Row(
              children: [
                const Text('السعر: '),
                CurrencyText(
                  amount:
                      _selectedPackage.sellingPrice ?? _selectedPackage.price,
                ),
              ],
            ),
            const Divider(height: 24),

            // Payment status switch
            SwitchListTile(
              title: const Text('تم الدفع'),
              subtitle: const Text('حدد إذا تم دفع قيمة التجديد'),
              value: _isPaid ?? false, // Use null-aware operator
              onChanged: (value) {
                setState(() {
                  _isPaid = value;
                });
              },
              activeColor: Theme.of(context).colorScheme.primary,
            ),

            // إضافة خيار إرسال رسالة
            SwitchListTile(
              title: const Text('إرسال رسالة تجديد'),
              subtitle: const Text('إرسال رسالة للمشترك بعد تجديد الاشتراك'),
              value: _sendMessage ?? false, // Use null-aware operator
              onChanged: (value) {
                setState(() {
                  _sendMessage = value;
                });
              },
              activeColor: Theme.of(context).colorScheme.secondary,
              secondary: Icon(
                Icons.message,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),

            // Payment details (only shown if paid)
            if (_isPaid ?? false) ...[
              // Use null-aware operator
              const SizedBox(height: 12),
              DropdownButtonFormField<String>(
                value: _paymentMethod,
                decoration: const InputDecoration(
                  labelText: 'طريقة الدفع',
                  border: OutlineInputBorder(),
                ),
                items: _paymentMethods.map((method) {
                  return DropdownMenuItem(value: method, child: Text(method));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _paymentMethod = value!;
                  });
                },
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        if (!_printerLoading && _printerEnabled)
          OutlinedButton.icon(
            onPressed: _isLoading ? null : _printReceipt,
            icon: const Icon(Icons.print),
            label: const Text('طباعة الشريط'),
          ),
        ElevatedButton(
          onPressed: _isLoading ? null : _processRenewal,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('تجديد'),
        ),
      ],
    );
  }

    // =========================================================================
  // UNIFIED RENEWAL LOGIC
  // =========================================================================

  /// The main, unified renewal processing function.
  /// This function acts as a dispatcher based on the subscriber's server type.
  Future<void> _processRenewal() async {
    setState(() => _isLoading = true);

    try {
      // Step 1: Perform server-side renewal logic by dispatching to the correct helper.
      await _executeRenewalOnServer();

      // Step 2: Update the local database. This is common for all renewal types.
      final updatedSubscriber = await _updateLocalDatabaseAfterRenewal();

      // Step 3: Perform post-renewal actions (logging, messaging, printing).
      await _performPostRenewalActions(updatedSubscriber);

      if (mounted) {
        _showSafeSnackBar(
          'تم تجديد اشتراك ${widget.subscriber.fullName} بنجاح.',
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        // Extract a cleaner error message.
        final errorMessage = e.toString().startsWith('Exception: ')
            ? e.toString().substring('Exception: '.length)
            : e.toString();
        _showSafeSnackBar('خطأ في تجديد الاشتراك: $errorMessage', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Helper to determine the server and execute the correct renewal logic.
  Future<void> _executeRenewalOnServer() async {
    final subscriber = widget.subscriber;

    // For local users without a username, only local renewal is performed.
    if (subscriber.username.isEmpty) {
      print('🏠 المستخدم محلي (بدون اسم مستخدم)، سيتم التجديد محلياً فقط.');
      return; // Skip server-side processing.
    }

    // Get the currently active connected server.
    final SasServerModel? server = await DatabaseService().getConnectedServer();

    // If there is no active connected server, we can only perform a local renewal.
    if (server == null) {
      print('⚠️ لا يوجد خادم متصل فعال، سيتم التجديد محلياً فقط.');
      // Inform the user that renewal will be local-only.
      _showSafeSnackBar('لا يوجد خادم متصل. سيتم التجديد محلياً فقط.');
      return; // Skip server-side processing.
    }

    print(
        '🔄 بدء عملية التجديد على الخادم النشط المتصل: ${server.name} (النوع: ${server.serverType})');

    // Dispatch to the appropriate renewal method based on server type.
    switch (server.serverType) {
      case ServerType.MikroTik:
        await _renewOnMikroTik(server);
        break;
      case ServerType.SAS:
        await _renewOnSas(server);
        break;
      case ServerType.Earthlink:
        await _renewOnEarthlink(server);
        break;
      default:
        print('⚠️ نوع الخادم غير مدعوم للتجديد التلقائي: ${server.serverType}');
        // Proceed with local renewal only.
        break;
    }
  }

  /// Handles the renewal process for a MikroTik server.
  Future<void> _renewOnMikroTik(SasServerModel server) async {
    final newProfile = _selectedPackage.mikrotikProfileName;
    if (newProfile == null || newProfile.isEmpty) {
      throw Exception('الباقة المحددة لا تحتوي على بروفايل مايكروتك.');
    }

    final newEndDate =
        DateTime.now().add(Duration(days: _selectedPackage.durationInDays));
    // A more informative comment for the MikroTik user with full date and time.
    final newComment =
        '${DateFormat('yyyy-MM-dd HH:mm:ss').format(newEndDate)} | ${widget.subscriber.fullName} | ${widget.subscriber.phoneNumber}';

    final api = MikrotikSyncService(server);
    await api.renewPppSecret(
      username: widget.subscriber.username,
      newProfile: newProfile,
      newComment: newComment,
    );
    print('✅ تم تجديد مشترك المايكروتك بنجاح.');
  }

  /// Handles the renewal process for a SAS Radius server.
  Future<void> _renewOnSas(SasServerModel server) async {
    final sasProfileId = _selectedPackage.sasProfileId;
    if (sasProfileId == null || sasProfileId.isEmpty) {
      throw Exception('الباقة المحددة لا تحتوي على معرف بروفايل SAS.');
    }

    final sasApi = SasApiService();
    final loggedIn = await sasApi.loginWithCredentials(
      host: server.host,
      username: server.username,
      password: server.password,
    );
    if (!loggedIn) {
      throw Exception('فشل تسجيل الدخول إلى خادم SAS.');
    }

    await sasApi.activateUserInSas(
      username: widget.subscriber.username,
      newProfileId: int.parse(sasProfileId),
      comments: 'Renewed from ISP Manager App',
    );
    print('✅ تم تجديد مشترك SAS بنجاح.');
  }

  /// Handles the renewal process for an Earthlink server.
  Future<void> _renewOnEarthlink(SasServerModel server) async {
    final accountIndex = int.tryParse(_selectedPackage.earthlinkAccountId ?? '');
    if (accountIndex == null) {
      throw Exception(
          'الباقة "${_selectedPackage.name}" لا تحتوي على معرف حساب Earthlink صالح.');
    }

    if (server.depositPassword == null || server.depositPassword!.isEmpty) {
      throw Exception('كلمة مرور الإيداع غير محددة في إعدادات سيرفر Earthlink.');
    }

    final earthlinkService = EarthlinkService();
    final loginResult = await earthlinkService.login(
      username: server.username,
      password: server.password,
    );

    if (!loginResult['success']) {
      throw Exception(
          'فشل تسجيل الدخول إلى Earthlink: ${loginResult['error']}');
    }

    // Check admin balance against the cost of the renewal package.
    final costResult =
        await earthlinkService.getAccountCost(accountIndex: accountIndex);
    if (!costResult['success']) {
      throw Exception('فشل جلب تكلفة الحساب: ${costResult['error']}');
    }
    final accountCost = (costResult['cost'] as num).toDouble();

    final balanceResult = await earthlinkService.getAdminBalance();
    if (!balanceResult['success']) {
      throw Exception('فشل جلب رصيد المدير: ${balanceResult['error']}');
    }
    final adminBalance = (balanceResult['balance'] as num).toDouble();

    if (adminBalance < accountCost) {
      final formattedBalance =
          await AppSettingsService.formatCurrency(adminBalance);
      final formattedCost = await AppSettingsService.formatCurrency(accountCost);
      throw Exception(
          'الرصيد غير كافي ($formattedBalance) للتجديد الذي يتطلب $formattedCost.');
    }

    // Execute the renewal using the deposit password.
    final renewalResult = await earthlinkService.renewUserWithDeposit(
      userId: widget.subscriber.earthlinkUserIndex!,
      depositPassword: server.depositPassword!,
      accountIndex: accountIndex,
      agentIndex: '1',
      affiliateIndex: '1',
      firstName: widget.subscriber.fullName.split(' ').first,
      lastName: widget.subscriber.fullName.split(' ').length > 1
          ? widget.subscriber.fullName.split(' ').skip(1).join(' ')
          : '',
      displayName: widget.subscriber.fullName,
      phoneNumber: widget.subscriber.phoneNumber,
      address: widget.subscriber.address,
      earthMaxMAC: widget.subscriber.macAddress,
    );

    if (!renewalResult['success']) {
      throw Exception(
          'فشل تجديد المستخدم في Earthlink: ${renewalResult['error']}');
    }
    print('✅ تم تجديد مشترك Earthlink بنجاح.');
  }

  /// Updates the subscriber's record in the local database after a successful renewal.
  Future<SubscriberModel> _updateLocalDatabaseAfterRenewal() async {
    final now = DateTime.now();
    final newEndDate =
        now.add(Duration(days: _selectedPackage.durationInDays));
    double newDebtAmount = widget.subscriber.debtAmount;
    final renewalAmount =
        _selectedPackage.sellingPrice ?? _selectedPackage.price;

    // If the renewal was not marked as paid, add the renewal amount to the debt.
    if (!(_isPaid ?? false)) {
      newDebtAmount += renewalAmount;
    }

    final updatedSubscriber = widget.subscriber.copyWith(
      adminId: FirebaseAuth.instance.currentUser!.uid,
      subscriptionStart: now,
      subscriptionEnd: newEndDate,
      packageId: _selectedPackageId,
      packageName: _selectedPackage.name,
      debtAmount: newDebtAmount,
      paymentStatus:
          (_isPaid ?? false) ? PaymentStatus.paid : PaymentStatus.pending,
      isActive: true, // Renewal always activates the subscriber.
    );

    await DatabaseService()
        .updateSubscriber(updatedSubscriber, isSyncUpdate: false);
    print('✅ تم تحديث قاعدة البيانات المحلية للمشترك.');
    return updatedSubscriber;
  }

  /// Performs post-renewal actions like logging, messaging, and printing receipts.
  Future<void> _performPostRenewalActions(
      SubscriberModel updatedSubscriber) async {
    final now = DateTime.now();
    final renewalAmount =
        _selectedPackage.sellingPrice ?? _selectedPackage.price;
    final user = await FirebaseAuthService()
        .getUserData(FirebaseAuth.instance.currentUser!.uid);

    if (user != null) {
      // Create an activity log for the renewal.
      String description;
      if (widget.package == null || _selectedPackageId != widget.package!.id) {
        description = (_isPaid ?? false)
            ? 'تم تجديد وتغيير الباقة إلى ${_selectedPackage.name} (مدفوع)'
            : 'تم تجديد وتغيير الباقة إلى ${_selectedPackage.name} (غير مدفوع)';
      } else {
        description = (_isPaid ?? false)
            ? 'تم تجديد الاشتراك لمدة ${_selectedPackage.durationInDays} يوم (مدفوع)'
            : 'تم تجديد الاشتراك لمدة ${_selectedPackage.durationInDays} يوم (غير مدفوع)';
      }

      final log = ActivityLogModel(
        adminId: widget.subscriber.adminId,
        id: DatabaseService().generateId(),
        subscriberId: widget.subscriber.id,
        userId: user.id,
        action: 'تجديد اشتراك',
        description: description,
        amount: (_isPaid ?? false) ? renewalAmount : 0.0,
        timestamp: now,
      );
      await DatabaseService().addActivityLog(log);

      // Create a payment record if the renewal was paid.
      if (_isPaid ?? false) {
        final paymentRecord = PaymentRecordModel(
          adminId: widget.subscriber.adminId,
          id: DatabaseService().generateId(),
          subscriberId: widget.subscriber.id,
          amount: renewalAmount,
          paymentMethod: _paymentMethod,
          notes: _notesController.text.trim(),
          paymentDate: now,
          recordedBy: user.id,
        );
        await DatabaseService().addPaymentRecord(paymentRecord);
      }
    }

    // Send a WhatsApp notification if requested.
    if (_sendMessage ?? false) {
      try {
        final message = await MessageService()
            .sendRenewalMessage(updatedSubscriber, _selectedPackage);
        WhatsAppService().sendMessage(updatedSubscriber.phoneNumber, message);
      } catch (e) {
        print('⚠️ فشل إرسال رسالة التجديد: $e');
        // Non-critical error, so we don't rethrow.
      }
    }

    // Print a receipt if auto-printing is enabled.
    if (_printerSettings?.autoRenewalPrint == true && _printerEnabled) {
      try {
        await _printReceipt();
      } catch (e) {
        print('⚠️ فشل طباعة إيصال التجديد: $e');
        // Non-critical error.
      }
    }
    print('✅ اكتملت إجراءات ما بعد التجديد.');
  }

  /// معالج تجديد Earthlink - يستخدم رصيد المدير مثل SAS
  Future<void> _processEarthlinkRenewal(SubscriberModel subscriber, PackageModel selectedPackage) async {
    print('🌐 بدء تجديد Earthlink للمستخدم: ${subscriber.username}');
    
    // Check if this is actually a local subscriber (no server needed)
    if (subscriber.username.isEmpty || subscriber.earthlinkUserIndex == null || subscriber.earthlinkUserIndex!.isEmpty) {
      print('🏠 المستخدم محلي (بدون خادم) - تخطي اتصال Earthlink');
      if (mounted) {
        Navigator.of(context).pop(true);
      }
      return;
    }
    
    try {
      final earthlinkService = EarthlinkService();
      
      // البحث عن سيرفر Earthlink متصل
      SasServerModel? earthlinkServer;
      
      print('🔍 بدء البحث عن سيرفر Earthlink...');
      print('🆔 معرف سيرفر المشترك: ${subscriber.sasServerId}');
      
      // Try to find an Earthlink server in this order:
      // 1. Server linked to this subscriber (if Earthlink type)
      // 2. Currently connected Earthlink server
      // 3. Any available Earthlink server
      if (subscriber.sasServerId != null) {
        print('🔗 محاولة استخدام السيرفر المرتبط بالمشترك...');
        final serverMap = await FirebaseService().getSasServerById(
          subscriber.sasServerId!,
        );
        if (serverMap != null) {
          final server = SasServerModel.fromMap(serverMap);
          print('📡 نوع السيرفر المرتبط: ${server.serverType}');
          if (server.serverType == ServerType.Earthlink) {
            earthlinkServer = server;
            print('✅ استخدام سيرفر Earthlink المرتبط بالمشترك: ${earthlinkServer.name}');
          } else {
            print('⚠️ السيرفر المرتبط ليس من نوع Earthlink، سيتم البحث عن سيرفر آخر');
          }
        } else {
          print('⚠️ لم يتم العثور على السيرفر المرتبط بالمشترك');
        }
      } else {
        print('ℹ️ لا يوجد سيرفر مرتبط بالمشترك، البحث عن سيرفر Earthlink متاح');
      }
      
      if (earthlinkServer == null) {
        // Try to use the currently connected Earthlink server
        final connectedServerMap = await FirebaseService().getConnectedSasServer();
        if (connectedServerMap != null) {
          final server = SasServerModel.fromMap(connectedServerMap);
          if (server.serverType == ServerType.Earthlink) {
            earthlinkServer = server;
            print('استخدام سيرفر Earthlink المتصل: ${earthlinkServer.name}');
            
            // Link this subscriber to the connected server for future use
            final updatedSubscriber = subscriber.copyWith(
              adminId: FirebaseAuth.instance.currentUser!.uid,
              sasServerId: earthlinkServer.id,
            );
            await DatabaseService().updateSubscriber(updatedSubscriber, isSyncUpdate: false);
          }
        }
      }
      
      if (earthlinkServer == null) {
        // Try to find any available Earthlink server (not necessarily connected)
        print('🔍 البحث عن أي سيرفر Earthlink متوفر...');
        final allServers = await FirebaseService().getSasServers();
        for (final serverMap in allServers) {
          final server = SasServerModel.fromMap(serverMap);
          if (server.serverType == ServerType.Earthlink) {
            earthlinkServer = server;
            print('✅ تم العثور على سيرفر Earthlink متوفر: ${earthlinkServer.name}');
            
            // Link this subscriber to the found server for future use
            final updatedSubscriber = subscriber.copyWith(
              adminId: FirebaseAuth.instance.currentUser!.uid,
              sasServerId: earthlinkServer.id,
            );
            await DatabaseService().updateSubscriber(updatedSubscriber, isSyncUpdate: false);
            break; // Use the first Earthlink server found
          }
        }
      }
      
      if (earthlinkServer == null) {
        print('❌ لم يتم العثور على أي سيرفر Earthlink. يرجى إضافة سيرفر Earthlink في إعدادات الخوادم.');
        if (mounted) {
          _showSafeSnackBar('لم يتم العثور على سيرفر Earthlink. يرجى إضافة سيرفر Earthlink في إعدادات الخوادم وتعيين نوعه إلى Earthlink.', isError: true);
        }
        return;
      }
      
      // التحقق من وجود deposit password في إعدادات السيرفر
      if (earthlinkServer.depositPassword == null || earthlinkServer.depositPassword!.isEmpty) {
        print('⚠️ كلمة مرور الإيداع غير محددة في إعدادات سيرفر Earthlink.');
        if (mounted) {
          _showSafeSnackBar('كلمة مرور الإيداع غير محددة في إعدادات سيرفر Earthlink. يرجى تعديل إعدادات السيرفر.', isError: true);
        }
        return;
      }
      
      // تسجيل الدخول إلى Earthlink
      final loginResult = await earthlinkService.login(
        username: earthlinkServer.username,
        password: earthlinkServer.password,
      );
      
      if (!loginResult['success']) {
        print('❌ فشل تسجيل الدخول إلى Earthlink: ${loginResult['error']}');
        if (mounted) {
          _showSafeSnackBar('فشل تسجيل الدخول إلى Earthlink: ${loginResult['error']}', isError: true);
        }
        return;
      }
      
      print('✅ تم تسجيل الدخول إلى Earthlink بنجاح');
      
      // الحصول على تكلفة الحساب
      print('📦 فحص معرف حساب Earthlink في الباقة المختارة...');
      print('📦 اسم الباقة: ${selectedPackage.name}');
      print('📦 earthlinkAccountId: "${selectedPackage.earthlinkAccountId}"');
      
      final accountIndex = int.tryParse(selectedPackage.earthlinkAccountId ?? '');
      if (accountIndex == null) {
        print('❌ معرف حساب Earthlink غير صالح في الباقة المختارة');
        print('💡 يرجى تعديل الباقة "${selectedPackage.name}" وإضافة معرف حساب Earthlink صالح');
        if (mounted) {
          _showSafeSnackBar('الباقة "${selectedPackage.name}" لا تحتوي على معرف حساب Earthlink صالح. يرجى تعديل الباقة وإضافة معرف الحساب.', isError: true);
        }
        return;
      }
      
      print('✅ معرف حساب Earthlink صالح: $accountIndex');
      
      // Check admin balance before attempting renewal
      final balanceResult = await earthlinkService.getAdminBalance();
      double adminBalance = 0.0;
      if (balanceResult['success']) {
        adminBalance = balanceResult['balance'] is int 
            ? (balanceResult['balance'] as int).toDouble() 
            : balanceResult['balance'] as double;
        print('💰 رصيد المدير الحالي: $adminBalance');
      } else {
        print('⚠️ فشل جلب رصيد المدير: ${balanceResult['error']}');
        if (mounted) {
          _showSafeSnackBar('فشل جلب رصيد المدير: ${balanceResult['error']}', isError: true);
        }
        return;
      }
      
      // Get account cost
      final costResult = await earthlinkService.getAccountCost(
        accountIndex: accountIndex,
      );
      
      double accountCost = 0.0;
      if (costResult['success']) {
        accountCost = costResult['cost'] is int 
            ? (costResult['cost'] as int).toDouble() 
            : costResult['cost'] as double;
        print('💵 تكلفة التجديد: $accountCost');
        
        // Check if balance is sufficient
        if (adminBalance < accountCost) {
          final formattedBalance = await AppSettingsService.formatCurrency(adminBalance);
          final formattedCost = await AppSettingsService.formatCurrency(accountCost);
          print('❌ الرصيد غير كافي للتجديد - الرصيد: $formattedBalance، التكلفة: $formattedCost');
          if (mounted) {
            _showSafeSnackBar('الرصيد غير كافي للتجديد - الرصيد: $formattedBalance، التكلفة: $formattedCost', isError: true);
          }
          return;
        }
      } else {
        print('❌ فشل جلب تكلفة الحساب: ${costResult['error']}');
        if (mounted) {
          _showSafeSnackBar('فشل جلب تكلفة الحساب: ${costResult['error']}', isError: true);
        }
        return;
      }
      
      // تنفيذ التجديد عبر رصيد المدير باستخدام deposit password من إعدادات السيرفر
      final renewalResult = await earthlinkService.renewUserWithDeposit(
        userId: subscriber.earthlinkUserIndex!, // مطلوب للتجديد
        depositPassword: earthlinkServer.depositPassword!, // من إعدادات السيرفر
        accountIndex: accountIndex,
        agentIndex: '1', // قيمة افتراضية
        affiliateIndex: '1', // قيمة افتراضية
        firstName: subscriber.fullName.split(' ').first,
        lastName: subscriber.fullName.split(' ').length > 1 
            ? subscriber.fullName.split(' ').skip(1).join(' ') 
            : '',
        displayName: subscriber.fullName,
        phoneNumber: subscriber.phoneNumber,
        address: subscriber.address,
        earthMaxMAC: subscriber.macAddress,
      );
      
      if (renewalResult['success']) {
        print('✅ تم تجديد المستخدم في Earthlink بنجاح: ${renewalResult['message']}');
        if (mounted) {
          _showSafeSnackBar('تم تجديد المستخدم في Earthlink بنجاح');
          
          // Close dialog with success result
          if (mounted) {
            Navigator.of(context).pop(true);
          }
        }
      } else {
        print('❌ فشل تجديد المستخدم في Earthlink: ${renewalResult['error']}');
        
        // Handle validation errors specifically
        if (renewalResult.containsKey('validationErrors') && 
            renewalResult['validationErrors'] is List && 
            (renewalResult['validationErrors'] as List).isNotEmpty) {
          final validationErrors = renewalResult['validationErrors'] as List;
          String errorMessage = 'فشل تجديد المستخدم في Earthlink:\n';
          for (var error in validationErrors) {
            errorMessage += '• $error\n';
          }
          print(errorMessage);
          if (mounted) {
            _showSafeSnackBar(errorMessage, isError: true);
          }
        } else {
          if (mounted) {
            _showSafeSnackBar('فشل تجديد المستخدم في Earthlink: ${renewalResult['error']}', isError: true);
          }
        }
      }
      
    } catch (earthlinkError) {
      print('⚠️ خطأ في تجديد Earthlink: ${earthlinkError.toString()}');
      if (mounted) {
        _showSafeSnackBar('خطأ في تجديد Earthlink: ${earthlinkError.toString()}', isError: true);
      }
    }
  }
  
  /// معالج تجديد SAS - المنطق الأصلي
  Future<void> _processSasRenewal(SubscriberModel subscriber, PackageModel selectedPackage) async {
    print('🔧 بدء تجديد SAS للمستخدم: ${subscriber.username}');
    
    // Check if this is actually a local subscriber (no server needed)
    if (subscriber.username.isEmpty || subscriber.sasServerId == null) {
      print('🏠 المستخدم محلي (بدون خادم) - تخطي اتصال SAS');
      if (mounted) {
        Navigator.of(context).pop(true);
      }
      return;
    }
    
    SasServerModel? sasServer;

    // Try to find a SAS server in this order:
    // 1. Server linked to this subscriber
    // 2. Currently connected server
    // 3. Any available active server
    if (subscriber.sasServerId != null) {
      final serverMap = await FirebaseService().getSasServerById(
        subscriber.sasServerId!,
      );
      if (serverMap != null) {
        sasServer = SasServerModel.fromMap(serverMap);
        print('Using subscriber-linked SAS server: ${sasServer.name}');
      }
    }

    if (sasServer == null) {
      // Try to use the currently connected server
      final connectedServerMap = await FirebaseService()
          .getConnectedSasServer();
      if (connectedServerMap != null) {
        sasServer = SasServerModel.fromMap(connectedServerMap);
        print('Using connected SAS server: ${sasServer.name}');

        // Link this subscriber to the connected server for future use
        final updatedSubscriber = subscriber.copyWith(
          adminId: FirebaseAuth.instance.currentUser!.uid,
          sasServerId: sasServer.id,
        );
        await DatabaseService().updateSubscriber(updatedSubscriber, isSyncUpdate: false);
      }
    }

    if (selectedPackage.sasProfileId != null) {
      try {
        final sasApiService = SasApiService(); // No parameters needed now
        final loggedIn = await sasApiService.login();
        if (loggedIn) {
          final profileId = int.tryParse(selectedPackage.sasProfileId!);
          if (profileId != null) {
            // التحقق من تغيير الباقة
            bool packageChanged = false;
            if (widget.package != null &&
                widget.package!.sasProfileId != null) {
              final currentProfileId = int.tryParse(
                widget.package!.sasProfileId!,
              );
              packageChanged = currentProfileId != profileId;
            }
            Map<String, dynamic>? sasResponse;
            if (packageChanged) {
              // إذا تغيرت الباقة، استخدم API تغيير الباقة أولاً ثم التفعيل
              print(
                'Package changed, using changeProfile API then activation API',
              );

              // الخطوة الأولى: تغيير الباقة
              final profileChangeResponse = await sasApiService
                  .changeUserProfile(
                    username: subscriber.username,
                    newProfileId: profileId,
                    method: (_isPaid ?? false)
                        ? 'credit'
                        : 'reward_points', // Use null-aware operator
                    comments:
                        'تغيير باقة من تطبيق ISP Manager - ${subscriber.fullName} من ${widget.package?.name ?? 'غير محدد'} إلى ${selectedPackage.name}',
                  );

              // الخطوة الثانية: تفعيل الاشتراك بالباقة الجديدة
              if (profileChangeResponse != null &&
                  profileChangeResponse['status'] == 'success') {
                print(
                  'Profile change successful, now activating subscription',
                );
                sasResponse = await sasApiService.activateUserInSas(
                  username: subscriber.username,
                  newProfileId: profileId,
                  comments:
                      'تفعيل اشتراك بعد تغيير الباقة من تطبيق ISP Manager - ${subscriber.fullName}',
                  moneyCollected: true,
                  issueInvoice: true,
                );
                print(
                  'SAS Activation Response after package change: $sasResponse',
                );

                // دمج معلومات تغيير الباقة مع نتيجة التفعيل
                if (sasResponse != null &&
                    sasResponse['status'] == 'success') {
                  sasResponse['package_changed'] = true;
                  sasResponse['old_profile_id'] =
                      profileChangeResponse['old_profile_id'];
                  sasResponse['new_profile_id'] =
                      profileChangeResponse['new_profile_id'];
                }
              } else {
                // إذا فشل تغيير الباقة، استخدم رد تغيير الباقة
                sasResponse = profileChangeResponse;
              }
            } else {
              // إذا لم تتغير الباقة، استخدم API التفعيل العادي

              sasResponse = await sasApiService.activateUserInSas(
                username: subscriber.username,
                newProfileId: profileId,
                comments:
                    'تجديد اشتراك من تطبيق ISP Manager - ${subscriber.fullName}',
                moneyCollected: true,
                issueInvoice: true,
              );
            }
            if (sasResponse != null &&
                sasResponse['status'] == 'success') {
              if (sasResponse.containsKey('package_changed') &&
                  sasResponse['package_changed'] == true) {
                if (sasResponse.containsKey('old_profile_id') &&
                    sasResponse.containsKey('new_profile_id')) {}
              } else {}
            } else if (sasResponse != null &&
                sasResponse['status'] == 'info') {
              // حالة المعلومات (مثل نفس الباقة)
            } else {
              if (sasResponse != null &&
                  sasResponse.containsKey('package_changed')) {
                // حدث خطأ في التفعيل بعد تغيير الباقة بنجاح
              } else {
                // حدث خطأ في العملية الأساسية
              }

              if (sasResponse != null) {
                if (sasResponse.containsKey('status_code')) {}
              }
            }
          } else {
            print('⚠️ معرف البروفايل غير صالح في الباقة المختارة.');
          }
        } else {
          print(
            '⚠️ فشل تسجيل الدخول إلى SAS Radius. يرجى التحقق من الإعدادات.',
          );
        }
      } catch (sasError) {
        print('⚠️ خطأ في الاتصال بـ SAS Radius: ${sasError.toString()}');
      }
    } else {
      print(
        '⚠️ الباقة المختارة لا تحتوي على معرف بروفايل SAS Radius. تم التجديد محلياً فقط.',
      );
    }
    
    // Close dialog for local subscribers or when SAS process is complete
    if (mounted) {
      Navigator.of(context).pop(true);
    }
  }
}

class _PaymentDialog extends StatefulWidget {
  final SubscriberModel subscriber;
  final UserModel currentUser;

  const _PaymentDialog({required this.subscriber, required this.currentUser});

  @override
  State<_PaymentDialog> createState() => _PaymentDialogState();
}

class _PaymentDialogState extends State<_PaymentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  final _amountController = TextEditingController();
  late String _paymentMethod; // Changed to late
  bool _isLoading = false;
  bool _isPartialPayment = false;
  bool? _sendMessage; // Changed to nullable bool
  PrinterSettingsModel? _printerSettings;
  bool _printerEnabled = false;
  bool _printerLoading = true;

  final List<String> _paymentMethods = [
    'نقداً',
    'كاش آسيا',
    'زين كاش',
    'آسيا حوالة',
    'حوالة بنكية',
    'أخرى',
  ];
  @override
  void initState() {
    super.initState();
    _paymentMethod = _paymentMethods.first; // Initialize payment method
    _sendMessage = false; // Initialize nullable boolean

    // إذا كان هناك دين مستحق، استخدم قيمة الدين، وإلا استخدم صفر
    _amountController.text = widget.subscriber.debtAmount > 0
        ? widget.subscriber.debtAmount.toString()
        : '';
    _loadPrinterSettings();
  }

  // دالة مساعدة لإظهار الرسائل بشكل آمن
  void _showSafeSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : null,
        ),
      );
    } catch (scaffoldError) {
      print(message);
      // إظهار Toast كبديل
      Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: isError ? Colors.red : Colors.green,
        textColor: Colors.white,
      );
    }
  }

  Future<void> _loadPrinterSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final json = prefs.getString('printer_settings');
    if (json != null) {
      final settings = PrinterSettingsModel.fromJson(jsonDecode(json));
      setState(() {
        _printerSettings = settings;
        _printerEnabled =
            settings.connectedDeviceAddress != null &&
            settings.connectedDeviceAddress!.isNotEmpty;
        _printerLoading = false;
      });
    } else {
      setState(() {
        _printerSettings = null;
        _printerEnabled = false;
        _printerLoading = false;
      });
    }
  }

  Future<void> _printReceipt() async {
    if (_printerSettings == null) return;

    try {
      // إظهار رسالة تحميل فقط إذا كان الwidget مازال موجوداً
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('جاري الطباعة...')));
      }

      final now = DateTime.now();
      final paymentAmount = double.tryParse(_amountController.text) ?? 0.0;
      final data = {
        'subscriberName': widget.subscriber.fullName,
        'subscriptionNumber': widget.subscriber.id,
        'dateTime':
            '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} ${now.hour}:${now.minute.toString().padLeft(2, '0')}',
        'paymentAmount': paymentAmount.toInt(),
        'operationType': 'تسديد دين',
        'employeeName': '',
        'companyInfo': _printerSettings?.companyName ?? '',
      };

      // طباعة الإيصال بالصورة (حل مشاكل التشفير العربي)
      await PrinterService.printReceiptUnified(
        settings: _printerSettings!,
        data: data,
        operationType: 'تسديد',
        asImage: true, // استخدام الطباعة بالصورة دائماً
      );
      // رسالة نجاح فقط إذا كان الwidget مازال موجوداً
      if (mounted) {
        _showSafeSnackBar('تم طباعة إيصال الدفع بنجاح');
      }
    } catch (e) {
      // رسالة خطأ فقط إذا كان الwidget مازال موجوداً
      if (mounted) {
        _showSafeSnackBar('خطأ في الطباعة: $e', isError: true);
      }
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  Future<void> _processPayment() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      // الحصول على المبلغ المدفوع من حقل الإدخال
      final paymentAmount = double.tryParse(_amountController.text) ?? 0.0;

      // التحقق من أن المبلغ المدفوع أكبر من صفر
      if (paymentAmount <= 0) {
        setState(() => _isLoading = false);
        _showSafeSnackBar(
          'يجب أن يكون المبلغ المدفوع أكبر من صفر',
          isError: true,
        );
        return;
      }

      // التحقق من أن المبلغ المدفوع لا يتجاوز الدين المستحق فقط إذا كان هناك دين
      if (widget.subscriber.debtAmount > 0 &&
          paymentAmount > widget.subscriber.debtAmount) {
        setState(() => _isLoading = false);
        _showSafeSnackBar(
          'المبلغ المدفوع أكبر من الدين المستحق',
          isError: true,
        );
        return;
      }

      final now = DateTime.now();

      // حساب الدين المتبقي بعد الدفع
      final remainingDebt = widget.subscriber.debtAmount - paymentAmount;

      // تحديد حالة الدفع بناءً على الدين المتبقي
      final newPaymentStatus = remainingDebt <= 0
          ? PaymentStatus.paid
          : PaymentStatus.pending;

      // إنشاء سجل الدفعة
      final paymentRecord = PaymentRecordModel(
        adminId: widget.subscriber.adminId,
        id: _firestore.collection('paymentRecord').doc().id,
        subscriberId: widget.subscriber.id,
        amount: paymentAmount,
        paymentMethod: _paymentMethod,
        notes: _notesController.text.trim(),
        paymentDate: now,
        recordedBy: widget.currentUser.id,
      );

      // إنشاء سجل النشاط
      final activityLog = ActivityLogModel(
        adminId: widget.subscriber.adminId,
        id: _firestore.collection('activityLogs').doc().id,
        subscriberId: widget.subscriber.id,
        userId: widget.currentUser.id,
        action: 'تسجيل دفعة',
        description: remainingDebt > 0
            ? 'تم تسجيل دفعة جزئية بمبلغ ${await AppSettingsService.formatCurrency(paymentAmount)} عبر $_paymentMethod (متبقي ${await AppSettingsService.formatCurrency(remainingDebt)})'
            : 'تم تسديد كامل الدين بمبلغ ${await AppSettingsService.formatCurrency(paymentAmount)} عبر $_paymentMethod',
        amount: paymentAmount,
        timestamp: now,
      );

      // تحديث المشترك
      final updatedSubscriber = widget.subscriber.copyWith(
        adminId: FirebaseAuth.instance.currentUser!.uid,
        debtAmount: remainingDebt,
        paymentStatus: newPaymentStatus,
      );
      // حفظ جميع التحديثات
      await DatabaseService().updateSubscriberWithPayment(
        updatedSubscriber,
        paymentAmount,
        _paymentMethod,
        _notesController.text.trim(),
      );
      await DatabaseService().addPaymentRecord(paymentRecord);
      await DatabaseService().addActivityLog(activityLog);

      // إرسال رسالة إذا تم تفعيل الخيار
      if (_sendMessage ?? false) {
        // Use null-aware operator
        try {
          PackageModel? package = await DatabaseService().getPackageById(
            updatedSubscriber.packageId,
          );
          // إذا لم يتم العثور على الباقة، استخدم باقة افتراضية
          package ??= PackageModel(
            adminId: updatedSubscriber.adminId,
            id: 'default',
            name: 'باقة افتراضية',
            price: updatedSubscriber.debtAmount,
            durationInDays: 30,
            speed: 'غير محدد',
            deviceCount: 1,
            createdAt: DateTime.now(),
            serverId: '',
          );

          final message = await MessageService().sendPaymentMessage(
            updatedSubscriber,
            package,
            paymentAmount,
          );
          // فتح واتساب مع الرسالة
          WhatsAppService().sendMessage(updatedSubscriber.phoneNumber, message);
        } catch (msgError) {
          // إذا فشل إرسال الرسالة، نعرض خطأ ولكن نستمر في العملية
          _showSafeSnackBar(
            'تم تسجيل الدفعة ولكن فشل إرسال الرسالة: ${msgError.toString()}',
            isError: true,
          );
        }
      }

      // طباعة الإيصال تلقائياً إذا تم تفعيل الخيار
      if (_printerSettings?.autoPaymentPrint == true && _printerEnabled) {
        try {
          await _printReceipt();
        } catch (printError) {
          _showSafeSnackBar(
            'تم تسجيل الدفعة ولكن فشلت الطباعة التلقائية: ${printError.toString()}',
            isError: true,
          );
        }
      }

      Navigator.of(context).pop(true);
    } catch (e) {
      setState(() => _isLoading = false);
      _showSafeSnackBar('خطأ في تسجيل الدفعة: ${e.toString()}', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تسجيل دفعة'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('المشترك: ${widget.subscriber.fullName}'),
                const SizedBox(height: 8),
                FutureBuilder<String>(
                  future: widget.subscriber.debtAmount > 0
                      ? AppSettingsService.formatCurrency(
                          widget.subscriber.debtAmount,
                        ).then((amount) => 'المبلغ المستحق: $amount')
                      : widget.subscriber.debtAmount < 0
                      ? AppSettingsService.formatCurrency(
                          widget.subscriber.advancePaymentAmount,
                        ).then((amount) => 'الرصيد المقدم: $amount')
                      : Future.value(
                          'لا توجد ديون مستحقة - يمكنك إضافة رصيد مقدم',
                        ),
                  builder: (context, snapshot) {
                    return Text(snapshot.data ?? '...');
                  },
                ),
                const SizedBox(height: 16),
                // Switch for partial payment - إظهاره فقط إذا كان هناك دين مستحق
                if (widget.subscriber.debtAmount > 0) ...[
                  SwitchListTile(
                    title: const Text('دفع جزئي'),
                    subtitle: const Text('تمكين الدفع الجزئي للديون المستحقة'),
                    value: _isPartialPayment,
                    onChanged: (value) {
                      setState(() {
                        _isPartialPayment = value;
                        if (!value) {
                          // إذا تم إلغاء الدفع الجزئي، أعد تعيين المبلغ إلى إجمالي الدين
                          _amountController.text = widget.subscriber.debtAmount
                              .toString();
                        }
                      });
                    },
                    activeColor: Theme.of(context).colorScheme.primary,
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
                // Payment amount field
                TextFormField(
                  controller: _amountController,
                  decoration: const InputDecoration(
                    labelText: 'المبلغ المدفوع',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  enabled:
                      widget.subscriber.debtAmount <= 0 ||
                      _isPartialPayment, // تمكين التعديل إذا لم يكن هناك دين أو في حالة الدفع الجزئي
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال المبلغ المدفوع';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null) {
                      return 'يرجى إدخال رقم صحيح';
                    }
                    if (amount <= 0) {
                      return 'يجب أن يكون المبلغ أكبر من صفر';
                    }
                    // التحقق من الحد الأقصى فقط إذا كان هناك دين مستحق
                    if (widget.subscriber.debtAmount > 0 &&
                        amount > widget.subscriber.debtAmount) {
                      return 'المبلغ أكبر من الدين المستحق';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Payment method dropdown
                DropdownButtonFormField<String>(
                  value: _paymentMethod,
                  decoration: const InputDecoration(
                    labelText: 'طريقة الدفع',
                    border: OutlineInputBorder(),
                  ),
                  items: _paymentMethods.map((method) {
                    return DropdownMenuItem(value: method, child: Text(method));
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _paymentMethod = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Notes field
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات (اختياري)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),

                // إضافة خيار إرسال رسالة
                const SizedBox(height: 12),
                SwitchListTile(
                  title: const Text('إرسال رسالة تأكيد الدفع'),
                  subtitle: const Text('إرسال رسالة للمشترك بعد تسجيل الدفعة'),
                  value: _sendMessage ?? false, // Use null-aware operator
                  onChanged: (value) {
                    setState(() {
                      _sendMessage = value;
                    });
                  },
                  activeColor: Theme.of(context).colorScheme.secondary,
                  secondary: Icon(
                    Icons.message,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        if (!_printerLoading && _printerEnabled)
          OutlinedButton.icon(
            onPressed: _isLoading ? null : _printReceipt,
            icon: const Icon(Icons.print),
            label: const Text('طباعة الشريط'),
          ),
        ElevatedButton(
          onPressed: _isLoading ? null : _processPayment,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('تسجيل'),
        ),
      ],
    );
  }
}

class _PaymentReminderDialog extends StatefulWidget {
  final SubscriberModel subscriber;
  final PackageModel package;

  const _PaymentReminderDialog({
    required this.subscriber,
    required this.package,
  });

  @override
  State<_PaymentReminderDialog> createState() => _PaymentReminderDialogState();
}

class _PaymentReminderDialogState extends State<_PaymentReminderDialog> {
  List<String> _reminderMessages = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadReminderMessages();
  }

  Future<void> _loadReminderMessages() async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      // استخدام قوالب الرسائل المخصصة من شاشة إدارة قوالب الرسائل
      // يمكنك تخصيص هذه القوالب من خلال: إعدادات التطبيق > إدارة قوالب الرسائل
      final templates = await DatabaseService().getMessageTemplatesFire();
      
      // البحث عن قوالب التذكير بالدفع
      final reminderTemplates = templates.where((t) => t.type == MessageTemplateType.reminder).toList();
      
      if (reminderTemplates.isNotEmpty) {
        // استخدام القوالب المخصصة
        _reminderMessages = [];
        for (final template in reminderTemplates) {
          final message = await MessageService().previewMessage(
            template.content,
            widget.subscriber,
            widget.package,
          );
          _reminderMessages.add(message);
        }
      } else {
        // إذا لم توجد قوالب مخصصة، استخدم رسائل افتراضية
        final currencyAmount = await AppSettingsService.formatCurrency(
          widget.subscriber.debtAmount,
        );
        _reminderMessages = [
          'عزيزي ${widget.subscriber.fullName}، نذكركم بضرورة تسديد رسوم الاشتراك البالغة $currencyAmount.',
          'السيد/ة ${widget.subscriber.fullName}، يرجى تسديد المبلغ المستحق قدره $currencyAmount لتجنب قطع الخدمة.',
          'تحذير نهائي: سيتم إيقاف خدمة الإنترنت في حال عدم تسديد المبلغ المستحق خلال 24 ساعة.',
        ];
      }
    } catch (e) {
      // في حالة حدوث خطأ، استخدم رسائل افتراضية
      final currencyAmount = await AppSettingsService.formatCurrency(
        widget.subscriber.debtAmount,
      );
      _reminderMessages = [
        'عزيزي ${widget.subscriber.fullName}، نذكركم بضرورة تسديد رسوم الاشتراك البالغة $currencyAmount.',
        'السيد/ة ${widget.subscriber.fullName}، يرجى تسديد المبلغ المستحق قدره $currencyAmount لتجنب قطع الخدمة.',
        'تحذير نهائي: سيتم إيقاف خدمة الإنترنت في حال عدم تسديد المبلغ المستحق خلال 24 ساعة.',
      ];
    } finally {
      if (!mounted) return;
      setState(() => _isLoading = false);
    }
  }

  Future<void> _sendWhatsAppMessage(String message) async {
    try {
      await WhatsAppService().sendMessage(
        widget.subscriber.phoneNumber,
        message,
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن فتح واتساب. تأكد من تثبيت التطبيق'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.message, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: 8),
          const Text('رسائل التذكير'),
        ],
      ),
      content: _isLoading
          ? const SizedBox(
              height: 100,
              child: Center(child: CircularProgressIndicator()),
            )
          : SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // رسالة توضيحية
                  Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue.shade700, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'هذه الرسائل مبنية على قوالب مخصصة من شاشة إدارة قوالب الرسائل',
                            style: TextStyle(fontSize: 12, color: Colors.blue.shade700),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // قائمة الرسائل
                  SizedBox(
                    width: double.maxFinite,
                    child: ListView.separated(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: _reminderMessages.length,
                      separatorBuilder: (context, index) => const Divider(),
                      itemBuilder: (context, index) {
                        final message = _reminderMessages[index];
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'رسالة ${index + 1}:',
                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(message),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8,
                              alignment: WrapAlignment.end,
                              children: [
                                TextButton.icon(
                                  onPressed: () {
                                    // نسخ الرسالة إلى الحافظة
                                    Clipboard.setData(ClipboardData(text: message));
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('تم نسخ الرسالة'),
                                        duration: Duration(seconds: 1),
                                      ),
                                    );
                                  },
                                  icon: const Icon(Icons.copy, size: 16),
                                  label: const Text('نسخ'),
                                ),
                                ElevatedButton.icon(
                                  onPressed: () => _sendWhatsAppMessage(message),
                                  icon: const Icon(Icons.send, size: 16),
                                  label: const Text('إرسال عبر واتساب'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(
                                      0xFF25D366, // لون واتساب
                                    ),
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
      actions: [
        TextButton.icon(
          onPressed: () {
            Navigator.of(context).pop();
            Navigator.of(context).push(
              MaterialPageRoute(builder: (context) => const MessageTemplatesPage()),
            );
          },
          icon: const Icon(Icons.settings),
          label: const Text('إدارة القوالب'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }
}

class _EditSubscriberDialog extends StatefulWidget {
  final SubscriberModel subscriber;

  const _EditSubscriberDialog({required this.subscriber});

  @override
  State<_EditSubscriberDialog> createState() => _EditSubscriberDialogState();
}

class _EditSubscriberDialogState extends State<_EditSubscriberDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _phoneController;
  late TextEditingController _addressController;
  late TextEditingController _macController;
  late TextEditingController _routerController;
  late TextEditingController _notesController;
  late TextEditingController _usernameController;
  late TextEditingController _passwordController;
  bool _isActive = true;
  bool _isLoading = false;
  DateTime? _subscriptionStart;
  DateTime? _subscriptionEnd;
  late SubscriptionType _subscriptionType;
  List<SasServerModel> _sasServers = [];
  String? _selectedSasServerId;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.subscriber.fullName);
    _phoneController = TextEditingController(
      text: widget.subscriber.phoneNumber,
    );
    _addressController = TextEditingController(text: widget.subscriber.address);
    _macController = TextEditingController(text: widget.subscriber.macAddress);
    _routerController = TextEditingController(
      text: widget.subscriber.routerName,
    );
    _notesController = TextEditingController(
      text: widget.subscriber.technicalNotes,
    );
    _usernameController = TextEditingController(
      text: widget.subscriber.username,
    );
    _passwordController = TextEditingController(
      text: widget.subscriber.password,
    );
    _isActive = widget.subscriber.isActive;
    _subscriptionStart = widget.subscriber.subscriptionStart;
    _subscriptionEnd = widget.subscriber.subscriptionEnd;
    _subscriptionType = widget.subscriber.subscriptionType;
    _selectedSasServerId = widget.subscriber.sasServerId;
    _loadSasServers();
  }

  Future<void> _loadSasServers() async {
    try {
      final servers = await FirebaseService().getSasServers();
      setState(() {
        _sasServers = servers
            .map((map) => SasServerModel.fromMap(map))
            .toList();
      });
    } catch (e) {
      print('Error loading SAS servers: $e');
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _macController.dispose();
    _routerController.dispose();
    _notesController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _pickStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _subscriptionStart ?? DateTime.now(),
      firstDate:
          DateTime(
        DateTime.now().year - 5,
      ), // Allow picking dates up to 5 years in the past
      lastDate:
          DateTime(
        DateTime.now().year + 5,
      ), // Allow picking dates up to 5 years in the future
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Theme.of(context).colorScheme.onPrimary,
              surface: Theme.of(context).dialogBackgroundColor,
              onSurface: Theme.of(context).colorScheme.onSurface,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      setState(() {
        _subscriptionStart = picked;
        // Ensure end date is not before start date if both are set
        if (_subscriptionEnd != null &&
            _subscriptionEnd!.isBefore(_subscriptionStart!)) {
          _subscriptionEnd = _subscriptionStart;
        }
      });
    }
  }

  Future<void> _pickEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _subscriptionEnd ?? _subscriptionStart ?? DateTime.now(),
      firstDate:
          _subscriptionStart ??
          DateTime(DateTime.now().year - 5), // Cannot be before start date
      lastDate:
          DateTime(
        DateTime.now().year + 5,
      ), // Allow picking dates up to 5 years in the future
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Theme.of(context).colorScheme.onPrimary,
              surface: Theme.of(context).dialogBackgroundColor,
              onSurface: Theme.of(context).colorScheme.onSurface,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      setState(() {
        _subscriptionEnd = picked;
      });
    }
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _isLoading = true);
    try {
      // إنشاء سجل نشاط للتعديل
      final user = await FirebaseAuthService().getUserData(
        FirebaseAuth.instance.currentUser!.uid,
      );
      if (user != null) {
        final now = DateTime.now();
        final activityLog = ActivityLogModel(
          adminId: widget.subscriber.adminId,
          id: DatabaseService().generateId(),
          subscriberId: widget.subscriber.id,
          userId: user.id,
          action: 'تعديل بيانات',
          description: 'تم تعديل بيانات المشترك ${widget.subscriber.fullName}',
          amount: 0.0,
          timestamp: now,
        );
        // حفظ التغييرات في قاعدة البيانات
        await DatabaseService().updateSubscriberData(
          widget.subscriber.id,
          {
            'fullName': _nameController.text.trim(),
            'phoneNumber': _phoneController.text.trim(),
            'address': _addressController.text.trim(),
            'subscriptionType': _subscriptionType,
            'username': _usernameController.text.trim(),
            'password': _passwordController.text.trim(),
            'macAddress': _macController.text.trim(),
            'routerName': _routerController.text.trim(),
            'technicalNotes': _notesController.text.trim(),
            'isActive': _isActive,
            'subscriptionStart': _subscriptionStart,
            'subscriptionEnd': _subscriptionEnd,
          },
        );
        await DatabaseService().addActivityLog(activityLog);
      }

      // تحديث MikroTik إذا كان متصل
      await _updateMikroTikIfConnected();

      setState(() => _isLoading = false);
      Navigator.of(context).pop(true);
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تعديل بيانات المشترك: ${e.toString()}')),
      );
    }
  }

  // تحديث MikroTik إذا كان متصل
  Future<void> _updateMikroTikIfConnected() async {
    try {
      // التحقق من وجود اسم مستخدم (للمشتركين المحليين فقط)
      if (widget.subscriber.username.isEmpty) {
        print('[EditSubscriber] مشترك محلي (بدون اسم مستخدم)، لا يحتاج تحديث في MikroTik');
        return;
      }

      // البحث عن خادم متصل (نفس منطق التجديد)
      final connectedServer = await DatabaseService().getConnectedServer();

      if (connectedServer == null) {
        print('[EditSubscriber] لا يوجد خادم متصل، سيتم التعديل محلياً فقط');
        return;
      }

      // التحقق من أن الخادم المتصل هو MikroTik
      if (connectedServer.serverType != ServerType.MikroTik) {
        print('[EditSubscriber] الخادم المتصل ليس من نوع MikroTik');
        return;
      }

      print('[EditSubscriber] تحديث بيانات المشترك في MikroTik: ${widget.subscriber.username}');

      // التحقق من التغييرات المهمة
      final originalUsername = widget.subscriber.username;
      final newUsername = _usernameController.text.trim();
      final newPassword = _passwordController.text.trim();
      final newSubscriptionEnd = _subscriptionEnd ?? widget.subscriber.subscriptionEnd ?? DateTime.now().add(const Duration(days: 30));

      // إنشاء التعليق الجديد
      final newComment = '${DateFormat('yyyy-MM-dd HH:mm:ss').format(newSubscriptionEnd)} | ${_nameController.text.trim()} | ${_phoneController.text.trim()}';

      final api = MikrotikSyncService(connectedServer);

      // إذا تغير اسم المستخدم، نحتاج لحذف القديم وإنشاء جديد
      if (originalUsername != newUsername) {
        print('[EditSubscriber] تغيير اسم المستخدم من $originalUsername إلى $newUsername');

        // حذف المستخدم القديم
        await api.removePppSecret(originalUsername);

        // إنشاء مستخدم جديد
        await api.addPppSecret(
          name: newUsername,
          password: newPassword.isNotEmpty ? newPassword : '123456',
          profile: await _getSubscriberProfile(),
          comment: newComment,
        );
      } else {
        // تحديث البيانات الموجودة
        await api.updatePppSecret(
          username: originalUsername,
          newProfile: await _getSubscriberProfile(),
          newComment: newComment,
        );

        // تحديث كلمة المرور إذا تغيرت
        if (newPassword.isNotEmpty && newPassword != widget.subscriber.password) {
          await api.updatePppSecretPassword(
            username: originalUsername,
            newPassword: newPassword,
          );
        }
      }

      print('[EditSubscriber] تم تحديث بيانات المشترك في MikroTik بنجاح');
    } catch (e) {
      print('[EditSubscriber] خطأ في تحديث MikroTik: $e');
      // لا نوقف العملية، فقط نسجل الخطأ
    }
  }

  // الحصول على بروفايل المشترك
  Future<String> _getSubscriberProfile() async {
    try {
      final db = DatabaseService();
      final package = await db.getPackageById(widget.subscriber.packageId);
      return package?.mikrotikProfileName ?? package?.name ?? 'default';
    } catch (e) {
      print('[EditSubscriber] خطأ في الحصول على البروفايل: $e');
      return 'default';
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تعديل المشترك'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الاسم الكامل
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'الاسم الكامل',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال اسم المشترك';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // رقم الهاتف
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال رقم الهاتف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // العنوان
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(
                  labelText: 'العنوان',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال العنوان';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // نوع الاشتراك
              DropdownButtonFormField<SubscriptionType>(
                value: _subscriptionType,
                decoration: const InputDecoration(
                  labelText: 'نوع الاشتراك',
                  border: OutlineInputBorder(),
                ),
                items: SubscriptionType.values.map((type) {
                  String text;
                  switch (type) {
                    case SubscriptionType.broadband:
                      text = 'برودباند';
                      break;
                    case SubscriptionType.hotspot:
                      text = 'هوت سبوت';
                      break;
                  }
                  return DropdownMenuItem(value: type, child: Text(text));
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _subscriptionType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // اسم المستخدم وكلمة المرور
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _usernameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم المستخدم',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextFormField(
                      controller: _passwordController,
                      decoration: const InputDecoration(
                        labelText: 'كلمة المرور',
                        border: OutlineInputBorder(),
                      ),
                      obscureText: false,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // عنوان MAC
              TextFormField(
                controller: _macController,
                decoration: const InputDecoration(
                  labelText: 'عنوان MAC (اختياري)',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // اسم الراوتر
              TextFormField(
                controller: _routerController,
                decoration: const InputDecoration(
                  labelText: 'اسم الراوتر (اختياري)',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // الملاحظات الفنية
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'الملاحظات الفنية (اختياري)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),

              // حالة النشاط
              SwitchListTile(
                title: const Text('نشط'),
                subtitle: const Text('تحديد ما إذا كان المشترك نشطًا أم لا'),
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
                activeColor: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 16),
              // --- حقل اختيار تاريخ البداية ---
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: _pickStartDate,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ بداية الاشتراك',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          _SubscriberDetailPageState._formatDate(
                            _subscriptionStart,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // --- حقل اختيار تاريخ النهاية ---
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: _pickEndDate,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ نهاية الاشتراك',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          _SubscriberDetailPageState._formatDate(
                            _subscriptionEnd,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // SAS Server selection
              if (_sasServers.isNotEmpty)
                DropdownButtonFormField<String>(
                  value:
                      (_sasServers
                              .where(
                                (server) => server.id == _selectedSasServerId,
                              )
                              .length ==
                          1)
                      ? _selectedSasServerId
                      : null,
                  decoration: const InputDecoration(
                    labelText: 'خادم SAS Radius (اختياري)',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('لا يوجد خادم SAS'),
                    ),
                    ..._sasServers
                        .where(
                          (server) => server.id != null, // Filter out servers with null IDs
                        )
                        .toSet() // Remove duplicates based on server object equality
                        .map((server) {
                          return DropdownMenuItem(
                            value: server.id,
                            child: Text(server.name),
                          );
                        }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedSasServerId = value;
                    });
                  },
                ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveChanges,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('حفظ'),
        ),
      ],
    );
  }
}

class _PreviousDebtDialog extends StatefulWidget {
  final SubscriberModel subscriber;
  final UserModel currentUser;

  const _PreviousDebtDialog({
    required this.subscriber,
    required this.currentUser,
  });

  @override
  State<_PreviousDebtDialog> createState() => _PreviousDebtDialogState();
}

class _PreviousDebtDialogState extends State<_PreviousDebtDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  bool _isLoading = false;
  bool? _sendMessage; // Changed to nullable bool

  @override
  void initState() {
    super.initState();
    _sendMessage = false; // Initialize nullable boolean
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _sendWhatsAppMessage(
    SubscriberModel subscriber,
    String message,
  ) async {
    try {
      await WhatsAppService().sendMessage(subscriber.phoneNumber, message);
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن فتح واتساب. تأكد من تثبيت التطبيق'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _addPreviousDebt() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final debtAmount = double.tryParse(_amountController.text) ?? 0.0;

      if (debtAmount <= 0) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('يجب أن يكون مبلغ الدين أكبر من صفر')),
        );
        return;
      }

      final now = DateTime.now();

      // حساب إجمالي الدين الجديد (الدين الحالي + الدين السابق)
      final newTotalDebt = widget.subscriber.debtAmount + debtAmount;

      // تحديث المشترك بإضافة الدين السابق
      final updatedSubscriber = widget.subscriber.copyWith(
        adminId: FirebaseAuth.instance.currentUser!.uid,
        debtAmount: newTotalDebt,
        paymentStatus: PaymentStatus.pending,
      );
      final FirebaseFirestore firestore = FirebaseFirestore.instance;
      // إنشاء سجل نشاط لإضافة الدين السابق
      final activityLog = ActivityLogModel(
        adminId: widget.subscriber.adminId,
        id: firestore.collection('activityLog').doc().id,
        subscriberId: widget.subscriber.id,
        userId: widget.currentUser.id,
        action: 'إضافة دين سابق',
        description:
            'تمت إضافة دين سابق بمبلغ ${await AppSettingsService.formatCurrency(debtAmount)}. ${_notesController.text.isNotEmpty ? 'ملاحظات: ${_notesController.text}' : ''}',
        amount: debtAmount,
        timestamp: now,
      );

      // حفظ التغييرات في قاعدة البيانات
      await DatabaseService().updateSubscriberWithDebt(
        widget.subscriber.id,
        debtAmount,
        _notesController.text.trim(),
      );
      await DatabaseService().addActivityLog(activityLog);

      // إرسال رسالة إذا تم تفعيل الخيار
      if (_sendMessage ?? false) {
        // Use null-aware operator
        try {
          PackageModel? package = await DatabaseService().getPackageById(
            updatedSubscriber.packageId,
          );
          // إذا لم يتم العثور على الباقة، استخدم باقة افتراضية
          package ??= PackageModel(
            adminId: updatedSubscriber.adminId,
            id: 'default',
            name: 'باقة افتراضية',
            price: updatedSubscriber.debtAmount,
            durationInDays: 30,
            speed: 'غير محدد',
            deviceCount: 1,
            createdAt: DateTime.now(),
            serverId: '',
          );

          final message = await MessageService().sendReminderMessage(
            updatedSubscriber,
            package,
          );

          // فتح واتساب مع الرسالة
          _sendWhatsAppMessage(updatedSubscriber, message);
        } catch (msgError) {
          // إذا فشل إرسال الرسالة، نعرض خطأ ولكن نستمر في العملية
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم إضافة الدين السابق ولكن فشل إرسال الرسالة: ${msgError.toString()}',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }

      Navigator.of(context).pop(true);
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في إضافة الدين السابق: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة دين سابق'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('المشترك: ${widget.subscriber.fullName}'),
                const SizedBox(height: 8),
                FutureBuilder<String>(
                  future: AppSettingsService.formatCurrency(
                    widget.subscriber.debtAmount,
                  ),
                  builder: (context, snapshot) {
                    return Text('الدين الحالي: ${snapshot.data ?? '...'}');
                  },
                ),
                const SizedBox(height: 16),

                // مبلغ الدين السابق
                TextFormField(
                  controller: _amountController,
                  decoration: const InputDecoration(
                    labelText: 'مبلغ الدين السابق',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال مبلغ الدين';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null) {
                      return 'يرجى إدخال رقم صحيح';
                    }
                    if (amount <= 0) {
                      return 'يجب أن يكون المبلغ أكبر من صفر';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // ملاحظات
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات (اختياري)',
                    border: OutlineInputBorder(),
                    hintText: 'سبب إضافة الدين السابق',
                  ),
                  maxLines: 2,
                ),

                // إضافة خيار إرسال رسالة
                const SizedBox(height: 12),
                SwitchListTile(
                  title: const Text('إرسال رسالة تذكير'),
                  subtitle: const Text('إرسال رسالة للمشترك بعد إضافة الدين'),
                  value: _sendMessage ?? false, // Use null-aware operator
                  onChanged: (value) {
                    setState(() {
                      _sendMessage = value;
                    });
                  },
                  activeColor: Theme.of(context).colorScheme.secondary,
                  secondary: Icon(
                    Icons.message,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _addPreviousDebt,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('إضافة'),
        ),
      ],
    );
  }
}

// دالة لتحويل مدة الجلسة من ثواني إلى نص منسق
String formatDuration(int seconds) {
  final days = seconds ~/ 86400;
  final hours = (seconds % 86400) ~/ 3600;
  final minutes = (seconds % 3600) ~/ 60;
  final secs = seconds % 60;
  final parts = <String>[];
  if (days > 0) parts.add('$days يوم');
  if (hours > 0) parts.add('$hours ساعة');
  if (minutes > 0) parts.add('$minutes دقيقة');
  if (secs > 0 || parts.isEmpty) parts.add('$secs ثانية');
  return parts.join(' ');
}

// دالة لتحويل البايتات إلى صيغة مقروءة
String formatBytes(dynamic value) {
  if (value == null) return '-';
  int bytes = 0;
  try {
    bytes = int.parse(value.toString());
  } catch (_) {
    return value.toString();
  }
  if (bytes < 1024) return '$bytes بايت';
  if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(2)} KB';
  if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
  return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
}

class _EditDeviceCredentialsDialog extends StatefulWidget {
  final NetworkDevice device;
  final String currentUsername;
  final String currentPassword;

  const _EditDeviceCredentialsDialog({
    required this.device,
    required this.currentUsername,
    required this.currentPassword,
  });

  @override
  State<_EditDeviceCredentialsDialog> createState() => _EditDeviceCredentialsDialogState();
}

class _EditDeviceCredentialsDialogState extends State<_EditDeviceCredentialsDialog> {
  late TextEditingController _usernameController;
  late TextEditingController _passwordController;
  bool _isLoading = false;
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
    _usernameController = TextEditingController(text: widget.currentUsername);
    _passwordController = TextEditingController(text: widget.currentPassword);
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.settings, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: 8),
          const Text('تعديل بيانات دخول الجهاز'),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الجهاز
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات الجهاز:',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text('الاسم: ${widget.device.name}'),
                  Text('IP: ${widget.device.ipAddress}'),
                  Text('النوع: ${widget.device.type}'),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // حقل اسم المستخدم
            TextField(
              controller: _usernameController,
              decoration: const InputDecoration(
                labelText: 'اسم المستخدم',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              enabled: !_isLoading,
            ),
            const SizedBox(height: 16),

            // حقل كلمة المرور
            TextField(
              controller: _passwordController,
              obscureText: _obscurePassword,
              decoration: InputDecoration(
                labelText: 'كلمة المرور',
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
              ),
              enabled: !_isLoading,
            ),
            const SizedBox(height: 16),

            // ملاحظة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.orange.shade700, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'سيتم حفظ هذه البيانات كبيانات دخول خاصة لهذا الجهاز وإعادة جلب معلومات الإشارة',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveCredentials,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('حفظ'),
        ),
      ],
    );
  }

  void _saveCredentials() {
    final username = _usernameController.text.trim();
    final password = _passwordController.text.trim();

    if (username.isEmpty || password.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى ملء جميع الحقول')),
      );
      return;
    }

    Navigator.of(context).pop({
      'username': username,
      'password': password,
    });
  }
}

String cleanHost(String url) {
  final uri = Uri.tryParse(url);
  if (uri != null && uri.host.isNotEmpty) {
    return uri.host;
  }
  return url.replaceAll(RegExp(r'https?://'), '').split('/').first;
}
