import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/support_chat_model.dart';
import '../models/subscriber_model.dart';
import '../services/support_chat_service.dart';
import 'support_chat_admin_page.dart';

class SupportChatsAdminPage extends StatelessWidget {
  final String adminId;
  const SupportChatsAdminPage({Key? key, required this.adminId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final chatService = SupportChatService();
    return Scaffold(
      appBar: AppBar(
        title: const Text('دعم المشتركين'),
        backgroundColor: theme.colorScheme.surface,
      ),
      body: StreamBuilder<List<SupportChatModel>>(
        stream: chatService.getChatsForAdmin(adminId),
        builder: (context, snapshot) {
          final chats = snapshot.data ?? [];
          if (chats.isEmpty) {
            return const Center(child: Text('لا توجد محادثات دعم بعد.'));
          }
          return ListView.separated(
            padding: const EdgeInsets.all(12),
            itemCount: chats.length,
            separatorBuilder: (_, __) => const SizedBox(height: 8),
            itemBuilder: (context, i) {
              final chat = chats[i];
              return FutureBuilder<DocumentSnapshot>(
                future: FirebaseFirestore.instance.collection('subscribers').doc(chat.subscriberId).get(),
                builder: (context, subSnap) {
                  String name = 'مشترك';
                  String phone = '';
                  if (subSnap.hasData && subSnap.data!.exists) {
                    final data = subSnap.data!.data() as Map<String, dynamic>;
                    name = data['fullName'] ?? 'مشترك';
                    phone = data['phoneNumber'] ?? '';
                  }
                  return Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: Colors.blue.shade100,
                        child: const Icon(Icons.person, color: Colors.blue),
                      ),
                      title: Text(name, style: theme.textTheme.titleMedium),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (phone.isNotEmpty) Text(phone, style: theme.textTheme.bodySmall),
                          Text(
                            chat.lastMessage.isNotEmpty ? chat.lastMessage : 'لا توجد رسائل بعد',
                            style: theme.textTheme.bodySmall?.copyWith(color: Colors.grey.shade700),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                      trailing: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.chat, color: chat.isOpen ? Colors.green : Colors.grey, size: 20),
                          const SizedBox(height: 4),
                          Text(_formatTime(chat.createdAt), style: theme.textTheme.labelSmall),
                        ],
                      ),
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (_) => SupportChatAdminPage(
                              chat: chat,
                              subscriberName: name,
                              subscriberPhone: phone,
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final min = time.minute.toString().padLeft(2, '0');
    return '$hour:$min';
  }
} 