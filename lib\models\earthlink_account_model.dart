import 'dart:convert';

/// Model for Earthlink accounts (packages) based on API response
class EarthlinkAccount {
  final int? accountIndex;
  final String accountName;
  final String? accountImagePath;
  final int? count;
  final int? available;
  final double accountCost;
  final int? needed;
  final String? description;
  final String? speed;
  final int? durationDays;
  final bool? isActive;
  final String? accountType;
  final int? deviceLimit;

  EarthlinkAccount({
    this.accountIndex,
    required this.accountName,
    this.accountImagePath,
    this.count,
    this.available,
    required this.accountCost,
    this.needed,
    this.description,
    this.speed,
    this.durationDays,
    this.isActive,
    this.accountType,
    this.deviceLimit,
  });

  factory EarthlinkAccount.fromJson(Map<String, dynamic> json) {
    return EarthlinkAccount(
      accountIndex: json['accountIndex'] is int 
          ? json['accountIndex'] 
          : int.tryParse(json['accountIndex']?.toString() ?? ''),
      accountName: json['accountName']?.toString() ?? '',
      accountImagePath: json['accountImagePath']?.toString(),
      count: json['count'] is int 
          ? json['count'] 
          : int.tryParse(json['count']?.toString() ?? '0'),
      available: json['available'] is int 
          ? json['available'] 
          : int.tryParse(json['available']?.toString() ?? '0'),
      accountCost: json['accountCost'] is double 
          ? json['accountCost']
          : double.tryParse(json['accountCost']?.toString() ?? '0') ?? 0.0,
      needed: json['needed'] is int 
          ? json['needed'] 
          : int.tryParse(json['needed']?.toString() ?? '0'),
      description: json['description']?.toString(),
      speed: json['speed']?.toString(),
      durationDays: json['durationDays'] is int 
          ? json['durationDays'] 
          : int.tryParse(json['durationDays']?.toString() ?? '30'),
      isActive: json['isActive'] == true || 
                json['isActive']?.toString().toLowerCase() == 'true' ||
                json['available'] != null && json['available'] > 0,
      accountType: json['accountType']?.toString(),
      deviceLimit: json['deviceLimit'] is int 
          ? json['deviceLimit'] 
          : int.tryParse(json['deviceLimit']?.toString() ?? '1'),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accountIndex': accountIndex,
      'accountName': accountName,
      'accountImagePath': accountImagePath,
      'count': count,
      'available': available,
      'accountCost': accountCost,
      'needed': needed,
      'description': description,
      'speed': speed,
      'durationDays': durationDays,
      'isActive': isActive,
      'accountType': accountType,
      'deviceLimit': deviceLimit,
    };
  }

  @override
  String toString() {
    return 'EarthlinkAccount{accountIndex: $accountIndex, accountName: $accountName, accountCost: $accountCost}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EarthlinkAccount && other.accountIndex == accountIndex;
  }

  @override
  int get hashCode {
    return accountIndex.hashCode;
  }
}