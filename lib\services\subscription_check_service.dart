import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../models/device_subscription_model.dart';
import 'firebase_subscription_service.dart';
import '../pages/renew_subscription_page.dart'; // Correct import for RenewSubscriptionPage
import 'package:firebase_auth/firebase_auth.dart'; // Added for FirebaseAuth.instance.currentUser

class SubscriptionCheckService {
  final SharedPreferences _prefs;
  late final FirebaseSubscriptionService _deviceService;

  SubscriptionCheckService(this._prefs) {
    _deviceService = FirebaseSubscriptionService(_prefs);
  }

  /// Static helper for global usage (main.dart)
  static Future<Map<String, dynamic>> globalCheck(BuildContext context) async {
    final prefs = await SharedPreferences.getInstance();
    final service = SubscriptionCheckService(prefs);
    // Use getDeviceSubscription instead of getDeviceSubscriptionByEmail
    return await service.checkSubscriptionStatus();
  }

  /// Returns a full-screen blocking widget for expired subscriptions
  static Widget expiredBlockingWidget(DeviceSubscription? subscription, BuildContext context, {bool showRenewButton = true}) {
    return Scaffold(
      backgroundColor: Colors.orange.shade50,
      body: Center(
        child: Container(
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.orange.withOpacity(0.2),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.lock_clock, size: 80, color: Colors.orange),
              SizedBox(height: 16),
              Text(
                'الاشتراك منتهي الصلاحية',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
              SizedBox(height: 12),
              if (subscription != null) ...[
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.08),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.account_circle, color: Colors.blue, size: 20),
                          SizedBox(width: 8),
                          Text(
                            'رقم الحساب: ${subscription.accountNumber}',
                            style: TextStyle(fontSize: 16, color: Colors.blue, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.calendar_today, color: Colors.red, size: 20),
                          SizedBox(width: 8),
                          Text(
                            'انتهى في: ${subscription.formattedEndDate}',
                            style: TextStyle(fontSize: 14, color: Colors.red, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ] else ...[
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.08),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'تعذر جلب بيانات الاشتراك، يرجى التواصل مع الدعم أو المحاولة لاحقاً',
                    style: TextStyle(fontSize: 15, color: Colors.red, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
              SizedBox(height: 16),
              Text(
                'انتهت صلاحية اشتراكك. يرجى تجديد الاشتراك للمتابعة.',
                style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24),
              if (showRenewButton)
                ElevatedButton.icon(
                  onPressed: subscription != null
                      ? () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (_) => RenewSubscriptionPage(deviceSubscription: subscription),
                            ),
                          );
                        }
                      : null,
                  icon: Icon(Icons.refresh, color: Colors.white),
                  label: Text('تجديد الاشتراك', style: TextStyle(color: Colors.white)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // فحص شامل للاشتراك عند تشغيل التطبيق
  Future<Map<String, dynamic>> checkSubscriptionStatus() async {
    try {
      print('========== فحص حالة الاشتراك ==========');
      // جلب المستخدم الحالي من Firebase
      final user = FirebaseAuth.instance.currentUser;
      final email = user?.email;
      DeviceSubscription? subscription;
      if (user != null) {
        subscription = await _deviceService.getDeviceSubscription();
      } else {
        subscription = null;
      }
      print('معرف الجهاز: \u001b[33m${subscription?.deviceId ?? 'غير متوفر'}\u001b[0m');
      print('رقم الحساب: \u001b[33m${subscription?.accountNumber ?? 'غير متوفر'}\u001b[0m');
      print('تاريخ الانتهاء: \u001b[33m${subscription?.subscriptionEndDate ?? 'غير متوفر'}\u001b[0m');
      print('الحالة isActive: \u001b[33m${subscription?.isActive}\u001b[0m');
      print('البريد الإلكتروني المستخدم: \u001b[33m$email\u001b[0m');
      // فحص صلاحية الاشتراك
      final now = await _deviceService.fetchServerTime() ?? DateTime.now();
      final isExpired = subscription == null ||
          !subscription.isActive ||
          subscription.subscriptionEndDate.isBefore(now) ||
          subscription.subscriptionEndDate.isAtSameMomentAs(now);
      final isValid = !isExpired;
      final daysRemaining = subscription != null ? subscription.subscriptionEndDate.difference(now).inDays : 0;
      print('صلاحية الاشتراك: ${isValid ? "صالح" : "منتهي"}');
      print('الأيام المتبقية: $daysRemaining');
      print('========================================');
      return {
        'subscription': subscription,
        'isValid': isValid,
        'daysRemaining': daysRemaining,
        'needsRenewal': isExpired || (isValid && daysRemaining <= 3),
      };
    } catch (e) {
      print('خطأ في فحص الاشتراك: $e');
      return {
        'subscription': null,
        'isValid': false,
        'daysRemaining': 0,
        'needsRenewal': true,
        'error': e.toString(),
      };
    }
  }

  // عرض نافذة انتهاء الاشتراك (مثل التحديث الإجباري)
  Future<void> showSubscriptionExpiredDialog(BuildContext context, DeviceSubscription subscription) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: Row(
              children: [
                Icon(Icons.block, color: Colors.red, size: 28),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'انتهى الاشتراك',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.schedule,
                  size: 64,
                  color: Colors.orange,
                ),
                SizedBox(height: 16),
                Text(
                  'انتهت صلاحية اشتراكك',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 12),
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.withOpacity(0.3)),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(Icons.account_circle, color: Colors.blue, size: 20),
                          SizedBox(width: 8),
                          Text(
                            'رقم الحساب: ${subscription.accountNumber}',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(Icons.calendar_today, color: Colors.red, size: 20),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'انتهى في: ${subscription.subscriptionEndDate.day}/${subscription.subscriptionEndDate.month}/${subscription.subscriptionEndDate.year}',
                              style: TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16),
                Text(
                  'يرجى التواصل مع المطور لتجديد الاشتراك',
                  style: TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: <Widget>[
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: Icon(Icons.contact_support, color: Colors.white),
                  label: Text(
                    'طلب تجديد الاشتراك',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    padding: EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: () async {
                    Navigator.of(context).pop(); // أغلق النافذة
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => RenewSubscriptionPage(
                          deviceSubscription: subscription,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // عرض تحذير قرب انتهاء الاشتراك
  Future<void> showSubscriptionWarningDialog(BuildContext context, DeviceSubscription subscription, int daysRemaining) async {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange, size: 28),
              SizedBox(width: 8),
              Text('تحذير انتهاء الاشتراك'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.access_time,
                size: 48,
                color: Colors.orange,
              ),
              SizedBox(height: 16),
              Text(
                'سينتهي اشتراكك خلال $daysRemaining أيام',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 12),
              Text(
                'رقم حسابك: ${subscription.accountNumber}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('تذكيرني لاحقاً'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();                // TODO: Add reason for renewal to logs: 'طلب تجديد مبكر' + 'تبقى $daysRemaining أيام'
                await _deviceService.requestSubscriptionRenewal();
                
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم إرسال طلب التجديد'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: Text('طلب التجديد'),
            ),
          ],
        );
      },
    );
  }
}
