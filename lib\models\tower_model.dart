import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:math';

class TowerModel {
  final String id;
  final String adminId;
  final String name;
  final String serviceName;
  final String agentNumber;
  final double latitude;
  final double longitude;
  final double coverageRadius; // بالكيلومترات
  final List<PackagePrice> packages;
  final DateTime createdAt;
  final bool isActive;

  TowerModel({
    required this.id,
    required this.adminId,
    required this.name,
    required this.serviceName,
    required this.agentNumber,
    required this.latitude,
    required this.longitude,
    required this.coverageRadius,
    required this.packages,
    required this.createdAt,
    this.isActive = true,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'adminId': adminId,
      'name': name,
      'serviceName': serviceName,
      'agentNumber': agentNumber,
      'latitude': latitude,
      'longitude': longitude,
      'coverageRadius': coverageRadius,
      'packages': packages.map((p) => p.toMap()).toList(),
      'createdAt': createdAt,
      'isActive': isActive,
    };
  }

  factory TowerModel.fromMap(Map<String, dynamic> map) {
    return TowerModel(
      id: map['id'] ?? '',
      adminId: map['adminId'] ?? '',
      name: map['name'] ?? '',
      serviceName: map['serviceName'] ?? '',
      agentNumber: map['agentNumber'] ?? '',
      latitude: (map['latitude'] ?? 0.0).toDouble(),
      longitude: (map['longitude'] ?? 0.0).toDouble(),
      coverageRadius: (map['coverageRadius'] ?? 0.0).toDouble(),
      packages: List<PackagePrice>.from(
        (map['packages'] ?? []).map((x) => PackagePrice.fromMap(x)),
      ),
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      isActive: map['isActive'] ?? true,
    );
  }

  // حساب المسافة بين نقطتين بالكيلومترات
  double distanceTo(double lat, double lng) {
    const double earthRadius = 6371; // نصف قطر الأرض بالكيلومترات
    
    double lat1Rad = latitude * (pi / 180);
    double lat2Rad = lat * (pi / 180);
    double deltaLat = (lat - latitude) * (pi / 180);
    double deltaLng = (lng - longitude) * (pi / 180);

    double a = sin(deltaLat / 2) * sin(deltaLat / 2) +
        cos(lat1Rad) * cos(lat2Rad) * sin(deltaLng / 2) * sin(deltaLng / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  // التحقق من أن الموقع ضمن نطاق التغطية
  bool isWithinCoverage(double lat, double lng) {
    return distanceTo(lat, lng) <= coverageRadius;
  }
}

class PackagePrice {
  final String name;
  final double price;
  final String description;

  PackagePrice({
    required this.name,
    required this.price,
    required this.description,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'price': price,
      'description': description,
    };
  }

  // تنسيق السعر مع رمز العملة
  String formatPrice() {
    return '$price'; // سيتم تنسيقها في الواجهة
  }

  factory PackagePrice.fromMap(Map<String, dynamic> map) {
    return PackagePrice(
      name: map['name'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      description: map['description'] ?? '',
    );
  }
} 