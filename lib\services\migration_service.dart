import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'sqlite_service.dart';
import 'firebase_service.dart';

class MigrationService {
  static final MigrationService _instance = MigrationService._internal();
  factory MigrationService() => _instance;
  MigrationService._internal();

  final SQLiteService _sqliteService = SQLiteService();
  final FirebaseService _firebaseService = FirebaseService();

  // Migration status key
  static const String _migrationCompletedKey = 'migration_to_firebase_completed';

  // Check if migration has been completed
  Future<bool> isMigrationCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_migrationCompletedKey) ?? false;
  }

  // Mark migration as completed
  Future<void> markMigrationCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_migrationCompletedKey, true);
  }

  // Migrate all data from SQLite to Firebase
  Future<bool> migrateToFirebase() async {
    try {
      print('==== [MIGRATION] Starting migration from SQLite to Firebase ====');
      
      // Check if migration is already completed
      if (await isMigrationCompleted()) {
        print('==== [MIGRATION] Migration already completed ====');
        return true;
      }

      // Initialize Firebase
      await _firebaseService.initialize();

      // Migrate each table/collection
      await _migrateSasServers();
      await _migrateMikrotikDevices();
      await _migrateUsers();
      await _migratePackages();
      await _migrateSubscribers();
      await _migrateActivityLogs();
      await _migratePaymentRecords();
      await _migrateMessageTemplates();
      await _migrateExpenseCategories();
      await _migrateExpenses();

      // Mark migration as completed
      await markMigrationCompleted();

      print('==== [MIGRATION] Migration completed successfully ====');
      return true;
    } catch (e) {
      print('==== [MIGRATION] Migration failed: $e ====');
      return false;
    }
  }

  // Migrate SAS Servers
  Future<void> _migrateSasServers() async {
    try {
      final sasServers = await _sqliteService.getSasServers();
      if (sasServers.isNotEmpty) {
        await _firebaseService.migrateFromSQLite(sasServers, 'sas_servers');
        print('==== [MIGRATION] Migrated ${sasServers.length} SAS servers ====');
      }
    } catch (e) {
      print('Error migrating SAS servers: $e');
    }
  }

  // Migrate Mikrotik Devices
  Future<void> _migrateMikrotikDevices() async {
    try {
      final mikrotikDevices = await _sqliteService.getMikrotikDevices();
      if (mikrotikDevices.isNotEmpty) {
        await _firebaseService.migrateFromSQLite(mikrotikDevices, 'mikrotik_devices');
        print('==== [MIGRATION] Migrated ${mikrotikDevices.length} Mikrotik devices ====');
      }
    } catch (e) {
      print('Error migrating Mikrotik devices: $e');
    }
  }

  // Migrate Users
  Future<void> _migrateUsers() async {
    try {
      final users = await _sqliteService.getUsers();
      if (users.isNotEmpty) {
        await _firebaseService.migrateFromSQLite(users, 'users');
        print('==== [MIGRATION] Migrated ${users.length} users ====');
      }
    } catch (e) {
      print('Error migrating users: $e');
    }
  }

  // Migrate Packages
  Future<void> _migratePackages() async {
    try {
      final packages = await _sqliteService.getPackages();
      if (packages.isNotEmpty) {
        await _firebaseService.migrateFromSQLite(packages, 'packages');
        print('==== [MIGRATION] Migrated ${packages.length} packages ====');
      }
    } catch (e) {
      print('Error migrating packages: $e');
    }
  }

  // Migrate Subscribers
  Future<void> _migrateSubscribers() async {
    try {
      final subscribers = await _sqliteService.getSubscribers();
      if (subscribers.isNotEmpty) {
        await _firebaseService.migrateFromSQLite(subscribers, 'subscribers');
        print('==== [MIGRATION] Migrated ${subscribers.length} subscribers ====');
      }
    } catch (e) {
      print('Error migrating subscribers: $e');
    }
  }

  // Migrate Activity Logs
  Future<void> _migrateActivityLogs() async {
    try {
      final activityLogs = await _sqliteService.getActivityLogs();
      if (activityLogs.isNotEmpty) {
        await _firebaseService.migrateFromSQLite(activityLogs, 'activity_logs');
        print('==== [MIGRATION] Migrated ${activityLogs.length} activity logs ====');
      }
    } catch (e) {
      print('Error migrating activity logs: $e');
    }
  }

  // Migrate Payment Records
  Future<void> _migratePaymentRecords() async {
    try {
      final paymentRecords = await _sqliteService.getPaymentRecords();
      if (paymentRecords.isNotEmpty) {
        await _firebaseService.migrateFromSQLite(paymentRecords, 'payment_records');
        print('==== [MIGRATION] Migrated ${paymentRecords.length} payment records ====');
      }
    } catch (e) {
      print('Error migrating payment records: $e');
    }
  }

  // Migrate Message Templates
  Future<void> _migrateMessageTemplates() async {
    try {
      final messageTemplates = await _sqliteService.getMessageTemplates();
      if (messageTemplates.isNotEmpty) {
        await _firebaseService.migrateFromSQLite(messageTemplates, 'message_templates');
        print('==== [MIGRATION] Migrated ${messageTemplates.length} message templates ====');
      }
    } catch (e) {
      print('Error migrating message templates: $e');
    }
  }

  // Migrate Expense Categories
  Future<void> _migrateExpenseCategories() async {
    try {
      final expenseCategories = await _sqliteService.getExpenseCategories();
      if (expenseCategories.isNotEmpty) {
        await _firebaseService.migrateFromSQLite(expenseCategories, 'expense_categories');
        print('==== [MIGRATION] Migrated ${expenseCategories.length} expense categories ====');
      }
    } catch (e) {
      print('Error migrating expense categories: $e');
    }
  }

  // Migrate Expenses
  Future<void> _migrateExpenses() async {
    try {
      final expenses = await _sqliteService.getExpenses();
      if (expenses.isNotEmpty) {
        await _firebaseService.migrateFromSQLite(expenses, 'expenses');
        print('==== [MIGRATION] Migrated ${expenses.length} expenses ====');
      }
    } catch (e) {
      print('Error migrating expenses: $e');
    }
  }

  // Get migration statistics
  Future<Map<String, int>> getMigrationStatistics() async {
    try {
      final stats = <String, int>{};
      
      stats['sas_servers'] = (await _sqliteService.getSasServers()).length;
      stats['mikrotik_devices'] = (await _sqliteService.getMikrotikDevices()).length;
      stats['users'] = (await _sqliteService.getUsers()).length;
      stats['packages'] = (await _sqliteService.getPackages()).length;
      stats['subscribers'] = (await _sqliteService.getSubscribers()).length;
      stats['activity_logs'] = (await _sqliteService.getActivityLogs()).length;
      stats['payment_records'] = (await _sqliteService.getPaymentRecords()).length;
      stats['message_templates'] = (await _sqliteService.getMessageTemplates()).length;
      stats['expense_categories'] = (await _sqliteService.getExpenseCategories()).length;
      stats['expenses'] = (await _sqliteService.getExpenses()).length;
      
      return stats;
    } catch (e) {
      print('Error getting migration statistics: $e');
      return {};
    }
  }

  // Reset migration status (for testing)
  Future<void> resetMigrationStatus() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_migrationCompletedKey);
    print('==== [MIGRATION] Migration status reset ====');
  }

  // Verify migration by comparing record counts
  Future<bool> verifyMigration() async {
    try {
      final sqliteStats = await getMigrationStatistics();
      
      // Get Firebase record counts
      final firebaseStats = <String, int>{};
      firebaseStats['sas_servers'] = (await _firebaseService.getSasServers()).length;
      firebaseStats['mikrotik_devices'] = (await _firebaseService.getMikrotikDevices()).length;
      firebaseStats['users'] = (await _firebaseService.getUsers()).length;
      firebaseStats['packages'] = (await _firebaseService.getPackages()).length;
      firebaseStats['subscribers'] = (await _firebaseService.getSubscribers()).length;
      firebaseStats['activity_logs'] = (await _firebaseService.getActivityLogs()).length;
      firebaseStats['payment_records'] = (await _firebaseService.getPaymentRecords()).length;
      firebaseStats['message_templates'] = (await _firebaseService.getMessageTemplates()).length;
      firebaseStats['expense_categories'] = (await _firebaseService.getExpenseCategories()).length;
      firebaseStats['expenses'] = (await _firebaseService.getExpenses()).length;

      // Compare counts
      bool allMatch = true;
      sqliteStats.forEach((key, sqliteCount) {
        final firebaseCount = firebaseStats[key] ?? 0;
        if (sqliteCount != firebaseCount) {
          print('==== [MIGRATION] Count mismatch for $key: SQLite=$sqliteCount, Firebase=$firebaseCount ====');
          allMatch = false;
        }
      });

      if (allMatch) {
        print('==== [MIGRATION] Migration verification successful ====');
      } else {
        print('==== [MIGRATION] Migration verification failed ====');
      }

      return allMatch;
    } catch (e) {
      print('Error verifying migration: $e');
      return false;
    }
  }
} 