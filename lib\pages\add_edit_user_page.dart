import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../services/firebase_auth_service.dart';

class AddEditUserPage extends StatefulWidget {
  final UserModel? user;

  const AddEditUserPage({super.key, this.user});

  @override
  State<AddEditUserPage> createState() => _AddEditUserPageState();
}

class _AddEditUserPageState extends State<AddEditUserPage> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  final FirebaseAuthService _authService = FirebaseAuthService();

  UserRole _selectedRole = UserRole.operator;
  bool _isActive = true;
  bool _isLoading = false;
  bool _showPassword = false;
  List<String> _selectedPermissions = [];

  bool get _isEditing => widget.user != null;
  String adminId = "";
  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadUserData();
    } else {
      _loadDefaultPermissions();
    }
    getAdmin();
  }

  Future<void> _loadUserData() async {
    final user = widget.user!;
    _fullNameController.text = user.fullName;
    _usernameController.text = user.username;
    _phoneController.text = user.phoneNumber;
    _selectedRole = user.role;
    _isActive = user.isActive;
    _selectedPermissions = List.from(user.permissions);
  }

  Future<void> getAdmin() async {
    final doc = await FirebaseFirestore.instance
        .collection('users')
        .doc(FirebaseAuth.instance.currentUser!.uid)
        .get();
    setState(() {
      adminId = doc["adminId"];
    });
   
  }

  void _loadDefaultPermissions() {
    _selectedPermissions = UserModel.getDefaultPermissions(_selectedRole);
  }

  void _onRoleChanged(UserRole? role) {
    if (role != null) {
      setState(() {
        _selectedRole = role;
        if (!_isEditing) {
          _selectedPermissions = UserModel.getDefaultPermissions(role);
        }
      });
    }
  }

  void _togglePermission(String permission) {
    setState(() {
      if (_selectedPermissions.contains(permission)) {
        _selectedPermissions.remove(permission);
      } else {
        _selectedPermissions.add(permission);
      }
    });
  }

  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isEditing) {
        // تحديث المستخدم
        await _authService.updateUser(
          uid: widget.user!.id,
          fullName: _fullNameController.text.trim(),
          username: _usernameController.text.trim(),
          phoneNumber: _phoneController.text.trim(),
          role: _selectedRole,
          isActive: _isActive,
          permissions: _selectedPermissions,
        );
      } else {
        // إنشاء مستخدم جديد
        await _authService.createUser(
          fullName: _fullNameController.text.trim(),
          username: _usernameController.text.trim(),
          password: _passwordController.text,
          phoneNumber: _phoneController.text.trim(),
          role: _selectedRole,
          isActive: _isActive,
          permissions: _selectedPermissions,
          adminId: adminId,
        );
      }

      if (mounted) {
        Navigator.of(context).pop(true);
        _showSuccessSnackBar(
          _isEditing ? 'تم تحديث المستخدم بنجاح' : 'تم إنشاء المستخدم بنجاح',
        );
      }
    } catch (e) {
      _showErrorSnackBar(
        'فشل في ${_isEditing ? 'تحديث' : 'إنشاء'} المستخدم: $e',
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (!_isLoading)
            IconButton(onPressed: _saveUser, icon: const Icon(Icons.save)),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Basic Information Card
                    _buildBasicInfoCard(),
                    const SizedBox(height: 16),

                    // Role and Status Card
                    _buildRoleStatusCard(),
                    const SizedBox(height: 16),

                    // Permissions Card
                    _buildPermissionsCard(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Full Name
            TextFormField(
              controller: _fullNameController,
              decoration: const InputDecoration(
                labelText: 'الاسم الكامل *',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الاسم الكامل';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Username
            TextFormField(
              controller: _usernameController,
              decoration: const InputDecoration(
                labelText: 'اسم المستخدم *',
                prefixIcon: Icon(Icons.account_circle),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم المستخدم';
                }
                if (value.length < 3) {
                  return 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Password (only for new users)
            if (!_isEditing) ...[
              TextFormField(
                controller: _passwordController,
                obscureText: !_showPassword,
                decoration: InputDecoration(
                  labelText: 'كلمة المرور *',
                  prefixIcon: const Icon(Icons.lock),
                  suffixIcon: IconButton(
                    onPressed: () {
                      setState(() {
                        _showPassword = !_showPassword;
                      });
                    },
                    icon: Icon(
                      _showPassword ? Icons.visibility : Icons.visibility_off,
                    ),
                  ),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال كلمة المرور';
                  }
                  if (value.length < 6) {
                    return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
            ],

            // Phone Number
            TextFormField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف *',
                prefixIcon: Icon(Icons.phone),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleStatusCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الدور والحالة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Role Selection
            DropdownButtonFormField<UserRole>(
              value: _selectedRole,
              decoration: const InputDecoration(
                labelText: 'الدور *',
                prefixIcon: Icon(Icons.work),
                border: OutlineInputBorder(),
              ),
              items: UserRole.values.map((role) {
                return DropdownMenuItem(
                  value: role,
                  child: Text(role.displayName),
                );
              }).toList(),
              onChanged: _onRoleChanged,
            ),
            const SizedBox(height: 16),

            // Active Status
            SwitchListTile(
              title: const Text('الحساب نشط'),
              subtitle: const Text('يمكن للمستخدم تسجيل الدخول'),
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
              secondary: Icon(
                _isActive ? Icons.check_circle : Icons.cancel,
                color: _isActive ? Colors.green : Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الصلاحيات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedPermissions = UserModel.getDefaultPermissions(
                        _selectedRole,
                      );
                    });
                  },
                  child: const Text('إعادة تعيين للافتراضي'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Permissions grouped by category
            _buildPermissionGroup('المشتركين', [
              Permission.viewSubscribers,
              Permission.addSubscribers,
              Permission.editSubscribers,
              Permission.deleteSubscribers,
            ]),

            _buildPermissionGroup('الباقات', [
              Permission.viewPackages,
              Permission.addPackages,
              Permission.editPackages,
              Permission.deletePackages,
            ]),

            _buildPermissionGroup('المدفوعات', [
              Permission.viewPaymentRecords,
              Permission.addPaymentRecords,
              Permission.editPaymentRecords,
              Permission.deletePaymentRecords,
            ]),

            _buildPermissionGroup('المصاريف', [
              Permission.viewExpenses,
              Permission.addExpenses,
              Permission.editExpenses,
              Permission.deleteExpenses,
            ]),

            _buildPermissionGroup('الأجهزة', [
              Permission.viewNetworkDevices,
              Permission.addNetworkDevices,
              Permission.editNetworkDevices,
              Permission.deleteNetworkDevices,
            ]),

            _buildPermissionGroup('المستخدمين', [
              Permission.viewUsers,
              Permission.addUsers,
              Permission.editUsers,
              Permission.deleteUsers,
            ]),

            _buildPermissionGroup('التقارير', [
              Permission.viewReports,
              Permission.exportReports,
            ]),

            _buildPermissionGroup('النشاط', [
              Permission.viewActivityLogs,
              Permission.deleteActivityLogs,
            ]),

            _buildPermissionGroup('الإعدادات', [
              Permission.viewSettings,
              Permission.editSettings,
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionGroup(String title, List<String> permissions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: permissions.map((permission) {
            final isSelected = _selectedPermissions.contains(permission);
            return FilterChip(
              label: Text(Permission.getDisplayName(permission)),
              selected: isSelected,
              onSelected: (selected) => _togglePermission(permission),
              selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
              checkmarkColor: Theme.of(context).primaryColor,
            );
          }).toList(),
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}

extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.admin:
        return 'مدير - صلاحيات كاملة';
      case UserRole.distributor:
        return 'موزع - إدارة المشتركين';
      case UserRole.support:
        return 'دعم فني - دعم المشتركين';
      case UserRole.sales:
        return 'مبيعات - إضافة المشتركين';
      case UserRole.accountant:
        return 'محاسب - إدارة المدفوعات';
      case UserRole.technician:
        return 'فني - إدارة الأجهزة';
      case UserRole.operator:
        return 'مشغل - عرض البيانات';
    }
  }
}
