import 'dart:async';
import 'dart:convert';
import 'package:dartssh2/dartssh2.dart';
import '../models/network_device.dart';

class NetworkDeviceService {
  Future<Map<String, String>> getDeviceInfo(NetworkDevice device) async {
    final socket = await SSHSocket.connect(device.ipAddress, 22);
    final client = SSHClient(
      socket,
      username: device.username,
      onPasswordRequest: () => device.password,
    );

    try {
      Map<String, String> info = {};

      switch (device.type.toUpperCase()) {
        case 'UBNT':
          info = await _getUBNTInfo(client);
          break;
        case 'MIMOSA':
          info = await _getMimosaInfo(client);
          break;
        case 'TPLINK':
          info = await _getTPLinkInfo(client);
          break;
      }

      client.close();
      await socket.close();
      return info;    } catch (e) {
      throw Exception('Failed to connect to device: $e');
    }
  }

  Future<bool> rebootDevice(NetworkDevice device) async {
    final socket = await SSHSocket.connect(device.ipAddress, 22);
    final client = SSHClient(
      socket,
      username: device.username,
      onPasswordRequest: () => device.password,
    );

    try {
      String rebootCommand;
      
      switch (device.type.toUpperCase()) {
        case 'UBNT':
          rebootCommand = 'reboot';
          break;
        case 'MIMOSA':
          rebootCommand = 'reboot';
          break;
        case 'TPLINK':
          rebootCommand = 'reboot';
          break;
        default:
          rebootCommand = 'reboot';
      }

      // تنفيذ أمر إعادة التشغيل
      await client.run(rebootCommand);
      
      client.close();
      await socket.close();
      return true;
    } catch (e) {
      client.close();
      await socket.close();
      throw Exception('Failed to reboot device: $e');
    }
  }Future<Map<String, String>> _getUBNTInfo(SSHClient client) async {
    try {
      // جمع البيانات من عدة مصادر لأجهزة UBNT
      final iwconfig = utf8.decode(await client.run('iwconfig'));
      final wstalist = utf8.decode(await client.run('wstalist'));
      final ubntbox = utf8.decode(await client.run('ubnt-box status')); 
      final mcastatus = utf8.decode(await client.run('mca-status'));
      final uptime = utf8.decode(await client.run('uptime'));
      final ifconfig = utf8.decode(await client.run('ifconfig'));
      final firmware = utf8.decode(await client.run('cat /etc/version'));
        // أوامر إضافية للحصول على معلومات النظام والأجهزة
      final boardinfo = utf8.decode(await client.run('cat /proc/cpuinfo 2>/dev/null || echo ""'));      final deviceinfo = utf8.decode(await client.run('cat /etc/board.info 2>/dev/null || echo ""'));
      final hostname = utf8.decode(await client.run('hostname 2>/dev/null || echo ""'));
      final ethtool = utf8.decode(await client.run('ethtool eth0 2>/dev/null || echo ""'));
      final systeminfo = utf8.decode(await client.run('cat /etc/system.info 2>/dev/null || echo ""'));
        // أوامر إضافية لاسم الجهاز
      final uname = utf8.decode(await client.run('uname -a 2>/dev/null || echo ""'));
      final hostctl = utf8.decode(await client.run('hostnamectl 2>/dev/null || echo ""'));
      final cfgmgr = utf8.decode(await client.run('cfgmtd -r -p /etc/ 2>/dev/null || echo ""'));
      final ubntdiscovery = utf8.decode(await client.run('ubnt-discovery enable 2>/dev/null || echo ""'));
      
      // أوامر إضافية خاصة لاسم الجهاز
      final deviceName = utf8.decode(await client.run('cat /tmp/system.cfg 2>/dev/null | grep device.name || echo ""'));
      final systemCfg = utf8.decode(await client.run('cat /tmp/system.cfg 2>/dev/null | grep system.name || echo ""'));
      final configFile = utf8.decode(await client.run('cat /etc/persistent/cfg/system.cfg 2>/dev/null | grep device.name || echo ""'));      // دمج النتائج من المصادر المختلفة
      final combinedOutput = '$iwconfig\n$wstalist\n$ubntbox\n$mcastatus\n$boardinfo\n$deviceinfo\n$systeminfo\n$uname\n$hostctl\n$cfgmgr';
      final deviceNameOutput = '$hostname\n$ubntbox\n$deviceinfo\n$systeminfo\n$hostctl\n$cfgmgr\n$ubntdiscovery\n$deviceName\n$systemCfg\n$configFile';

      return {
        'signalStrength': _parseUBNTSignalStrength(combinedOutput),
        'noise': _parseUBNTNoise(combinedOutput),
        'snr': _parseUBNTSNR(combinedOutput),
        'txRate': _parseUBNTTxRate(combinedOutput),
        'rxRate': _parseUBNTRxRate(combinedOutput),        'uptime': _parseUptime(uptime),
        'macAddress': _parseMAC(ifconfig),        'firmwareVersion': firmware.trim(),
        'deviceModel': _parseUBNTDeviceModel('$combinedOutput\n$deviceNameOutput'),
        'deviceName': _parseUBNTDeviceName(deviceNameOutput),
        'networkMode': _parseUBNTNetworkMode(combinedOutput),
        'wirelessMode': _parseUBNTWirelessMode(combinedOutput),
        'ssid': _parseUBNTSSID(combinedOutput),
        'lanSpeed': _parseUBNTLanSpeed('$ethtool\n$ifconfig\n$combinedOutput'),
      };
    } catch (e) {      try {
        // محاولة ثانية مع أوامر أكثر تخصصاً
        final iwlist = utf8.decode(await client.run('iwlist scanning 2>/dev/null || iwlist scan 2>/dev/null || echo ""'));
        final iw = utf8.decode(await client.run('iw dev 2>/dev/null || echo ""'));
        final basic = utf8.decode(await client.run('iwconfig 2>/dev/null || echo ""'));
        final uptime = utf8.decode(await client.run('uptime'));
        final ifconfig = utf8.decode(await client.run('ifconfig'));
        final firmware = utf8.decode(await client.run('cat /etc/version 2>/dev/null || cat /proc/version 2>/dev/null || echo "Unknown"'));
          // أوامر إضافية للمحاولة الثانية
        final hostname = utf8.decode(await client.run('hostname 2>/dev/null || echo ""'));
        final ethtool = utf8.decode(await client.run('ethtool eth0 2>/dev/null || echo ""'));
        final deviceinfo = utf8.decode(await client.run('cat /etc/board.info 2>/dev/null || cat /proc/cpuinfo 2>/dev/null || echo ""'));
        final systeminfo = utf8.decode(await client.run('cat /etc/system.info 2>/dev/null || echo ""'));
        
        final combinedOutput = '$iwlist\n$iw\n$basic\n$deviceinfo\n$systeminfo';
        final deviceNameOutput = '$hostname\n$deviceinfo\n$systeminfo';
        
        return {
          'signalStrength': _parseUBNTSignalStrength(combinedOutput),
          'noise': _parseUBNTNoise(combinedOutput),
          'snr': _parseUBNTSNR(combinedOutput),
          'txRate': _parseUBNTTxRate(combinedOutput),
          'rxRate': _parseUBNTRxRate(combinedOutput),
          'uptime': _parseUptime(uptime),
          'macAddress': _parseMAC(ifconfig),
          'firmwareVersion': firmware.trim(),
          'deviceModel': _parseUBNTDeviceModel('$combinedOutput\n$deviceNameOutput'),
          'deviceName': _parseUBNTDeviceName(deviceNameOutput),
          'networkMode': _parseUBNTNetworkMode(combinedOutput),
          'wirelessMode': _parseUBNTWirelessMode(combinedOutput),
          'ssid': _parseUBNTSSID(combinedOutput),
          'lanSpeed': _parseUBNTLanSpeed('$ethtool\n$ifconfig\n$combinedOutput'),
        };      } catch (e2) {        // في حالة فشل جميع المحاولات، استخدم الأوامر الأساسية
        final result = utf8.decode(await client.run('iwconfig'));
        final uptime = utf8.decode(await client.run('uptime'));
        final mac = utf8.decode(await client.run('ifconfig'));
        final firmware = utf8.decode(await client.run('cat /etc/version 2>/dev/null || cat /proc/version 2>/dev/null || echo "Unknown"'));
        final hostname = utf8.decode(await client.run('hostname 2>/dev/null || echo "Unknown"'));

        return {
          'signalStrength': _parseSignalStrength(result),
          'noise': _parseNoise(result),
          'snr': _parseSNR(result),
          'txRate': _parseTxRate(result),
          'rxRate': _parseRxRate(result),
          'uptime': _parseUptime(uptime),
          'macAddress': _parseMAC(mac),
          'firmwareVersion': firmware.trim(),
          'deviceModel': hostname.trim().isNotEmpty ? hostname.trim() : 'Unknown',
          'deviceName': hostname.trim().isNotEmpty ? hostname.trim() : 'Unknown',
          'networkMode': 'Router',
          'wirelessMode': 'Station',
          'ssid': _parseUBNTSSID(result),
          'lanSpeed': 'Unknown',
        };
      }
    }
  }  Future<Map<String, String>> _getMimosaInfo(SSHClient client) async {
    final result = utf8.decode(await client.run('mimosa-cli show wireless'));
    final uptime = utf8.decode(await client.run('uptime'));
    final mac = utf8.decode(await client.run('ifconfig | grep ether'));
    final firmware = utf8.decode(await client.run('cat /etc/version'));
    final systemInfo = utf8.decode(await client.run('mimosa-cli show system'));
    final wirelessInfo = utf8.decode(await client.run('mimosa-cli show wireless'));

    return {
      'signalStrength': _parseSignalStrength(result),
      'noise': _parseNoise(result),
      'snr': _parseSNR(result),
      'txRate': _parseTxRate(result),
      'rxRate': _parseRxRate(result),
      'uptime': _parseUptime(uptime),
      'macAddress': _parseMAC(mac),
      'firmwareVersion': firmware.trim(),
      'deviceModel': _parseMimosaDeviceModel(systemInfo),
      'deviceName': _parseMimosaDeviceName(systemInfo),
      'networkMode': _parseMimosaNetworkMode(wirelessInfo),
      'wirelessMode': _parseMimosaWirelessMode(wirelessInfo),
      'ssid': _parseMimosaSSID(wirelessInfo),
      'lanSpeed': _parseMimosaLanSpeed(systemInfo),
    };
  }
  Future<Map<String, String>> _getTPLinkInfo(SSHClient client) async {
    final result = utf8.decode(await client.run('wireless show'));
    final uptime = utf8.decode(await client.run('uptime'));
    final mac = utf8.decode(await client.run('ifconfig | grep HWaddr'));
    final firmware = utf8.decode(await client.run('cat /etc/version'));
    final systemInfo = utf8.decode(await client.run('system info'));
    final interfaceInfo = utf8.decode(await client.run('interface show'));

    return {
      'signalStrength': _parseSignalStrength(result),
      'noise': _parseNoise(result),
      'snr': _parseSNR(result),
      'txRate': _parseTxRate(result),
      'rxRate': _parseRxRate(result),
      'uptime': _parseUptime(uptime),
      'macAddress': _parseMAC(mac),
      'firmwareVersion': firmware.trim(),
      'deviceModel': _parseTPLinkDeviceModel(systemInfo),
      'deviceName': _parseTPLinkDeviceName(systemInfo),
      'networkMode': _parseTPLinkNetworkMode(result),
      'wirelessMode': _parseTPLinkWirelessMode(result),
      'ssid': _parseTPLinkSSID(result),
      'lanSpeed': _parseTPLinkLanSpeed(interfaceInfo),
    };
  }

  // دوال خاصة لمعالجة بيانات UBNT
  String _parseUBNTSignalStrength(String output) {
    try {
      // البحث في wstalist أولاً
      final wstaMatch = RegExp(r'signal\s+(-?\d+)\s+dBm', caseSensitive: false).firstMatch(output);
      if (wstaMatch != null) {
        return '${wstaMatch.group(1)} dBm';
      }

      // البحث في mca-status
      final mcaMatch = RegExp(r'signal:\s+(-?\d+)', caseSensitive: false).firstMatch(output);
      if (mcaMatch != null) {
        return '${mcaMatch.group(1)} dBm';
      }

      // البحث في ubnt-box status
      final boxMatch = RegExp(r'Signal:\s+(-?\d+)\s+dBm', caseSensitive: false).firstMatch(output);
      if (boxMatch != null) {
        return '${boxMatch.group(1)} dBm';
      }

      // استخدام الطريقة التقليدية
      return _parseSignalStrength(output);
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseUBNTNoise(String output) {
    try {
      // البحث في wstalist
      final wstaMatch = RegExp(r'noise\s+(-?\d+)\s+dBm', caseSensitive: false).firstMatch(output);
      if (wstaMatch != null) {
        return '${wstaMatch.group(1)} dBm';
      }

      // البحث في mca-status
      final mcaMatch = RegExp(r'noise:\s+(-?\d+)', caseSensitive: false).firstMatch(output);
      if (mcaMatch != null) {
        return '${mcaMatch.group(1)} dBm';
      }

      // البحث في ubnt-box status
      final boxMatch = RegExp(r'Noise:\s+(-?\d+)\s+dBm', caseSensitive: false).firstMatch(output);
      if (boxMatch != null) {
        return '${boxMatch.group(1)} dBm';
      }

      // استخدام الطريقة التقليدية
      return _parseNoise(output);
    } catch (e) {
      return 'N/A';
    }
  }
  String _parseUBNTSNR(String output) {
    try {
      // البحث المباشر عن SNR في wstalist
      final wstaSNRMatch = RegExp(r'snr\s+(-?\d+)\s+dB', caseSensitive: false).firstMatch(output);
      if (wstaSNRMatch != null) {
        return '${wstaSNRMatch.group(1)} dB';
      }

      // البحث في mca-status عن SNR
      final mcaSNRMatch = RegExp(r'snr:\s+(-?\d+)', caseSensitive: false).firstMatch(output);
      if (mcaSNRMatch != null) {
        return '${mcaSNRMatch.group(1)} dB';
      }

      // البحث في ubnt-box status عن SNR
      final boxSNRMatch = RegExp(r'SNR:\s+(-?\d+)\s+dB', caseSensitive: false).firstMatch(output);
      if (boxSNRMatch != null) {
        return '${boxSNRMatch.group(1)} dB';
      }

      // حساب SNR من Signal و Noise إذا كانت متوفرة
      final signalPatterns = [
        RegExp(r'signal\s+(-?\d+)\s+dBm', caseSensitive: false),
        RegExp(r'signal:\s+(-?\d+)', caseSensitive: false),
        RegExp(r'Signal:\s+(-?\d+)\s+dBm', caseSensitive: false),
      ];
      
      final noisePatterns = [
        RegExp(r'noise\s+(-?\d+)\s+dBm', caseSensitive: false),
        RegExp(r'noise:\s+(-?\d+)', caseSensitive: false),
        RegExp(r'Noise:\s+(-?\d+)\s+dBm', caseSensitive: false),
      ];

      int? signalValue;
      int? noiseValue;

      // البحث عن قيمة الإشارة
      for (final pattern in signalPatterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          signalValue = int.tryParse(match.group(1)!);
          break;
        }
      }

      // البحث عن قيمة الضوضاء
      for (final pattern in noisePatterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          noiseValue = int.tryParse(match.group(1)!);
          break;
        }
      }

      // حساب SNR إذا كانت القيم متوفرة
      if (signalValue != null && noiseValue != null) {
        final snr = signalValue - noiseValue;
        return '$snr dB';
      }

      // البحث العام عن SNR
      final generalSNRMatch = RegExp(r'snr[=:]\s*(-?\d+)', caseSensitive: false).firstMatch(output);
      if (generalSNRMatch != null) {
        return '${generalSNRMatch.group(1)} dB';
      }

      // استخدام الطريقة التقليدية
      return _parseSNR(output);
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseUBNTTxRate(String output) {
    try {
      // البحث في wstalist
      final wstaMatch = RegExp(r'tx\s+(\d+(?:\.\d+)?)\s+Mbps', caseSensitive: false).firstMatch(output);
      if (wstaMatch != null) {
        return '${wstaMatch.group(1)} Mbps';
      }

      // البحث في mca-status
      final mcaMatch = RegExp(r'tx-rate:\s+(\d+(?:\.\d+)?)', caseSensitive: false).firstMatch(output);
      if (mcaMatch != null) {
        return '${mcaMatch.group(1)} Mbps';
      }

      // البحث في ubnt-box status
      final boxMatch = RegExp(r'TX Rate:\s+(\d+(?:\.\d+)?)\s+Mbps', caseSensitive: false).firstMatch(output);
      if (boxMatch != null) {
        return '${boxMatch.group(1)} Mbps';
      }

      // البحث عن معدل البيانات
      final dataRateMatch = RegExp(r'txrate\s+(\d+(?:\.\d+)?)', caseSensitive: false).firstMatch(output);
      if (dataRateMatch != null) {
        return '${dataRateMatch.group(1)} Mbps';
      }

      // استخدام الطريقة التقليدية
      return _parseTxRate(output);
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseUBNTRxRate(String output) {
    try {
      // البحث في wstalist
      final wstaMatch = RegExp(r'rx\s+(\d+(?:\.\d+)?)\s+Mbps', caseSensitive: false).firstMatch(output);
      if (wstaMatch != null) {
        return '${wstaMatch.group(1)} Mbps';
      }

      // البحث في mca-status
      final mcaMatch = RegExp(r'rx-rate:\s+(\d+(?:\.\d+)?)', caseSensitive: false).firstMatch(output);
      if (mcaMatch != null) {
        return '${mcaMatch.group(1)} Mbps';
      }

      // البحث في ubnt-box status
      final boxMatch = RegExp(r'RX Rate:\s+(\d+(?:\.\d+)?)\s+Mbps', caseSensitive: false).firstMatch(output);
      if (boxMatch != null) {
        return '${boxMatch.group(1)} Mbps';
      }

      // البحث عن معدل البيانات
      final dataRateMatch = RegExp(r'rxrate\s+(\d+(?:\.\d+)?)', caseSensitive: false).firstMatch(output);
      if (dataRateMatch != null) {
        return '${dataRateMatch.group(1)} Mbps';
      }      // استخدام الطريقة التقليدية
      return _parseRxRate(output);
    } catch (e) {
      return 'N/A';
    }
  }  // دوال إضافية لمعلومات UBNT المتقدمة
  String _parseUBNTDeviceModel(String output) {
    try {
      // البحث عن نموذج الجهاز في ubnt-box status
      final modelMatch = RegExp(r'Device Model:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (modelMatch != null) {
        return modelMatch.group(1)!.trim();
      }

      // البحث في Model Number
      final modelNumberMatch = RegExp(r'Model Number:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (modelNumberMatch != null) {
        return modelNumberMatch.group(1)!.trim();
      }

      // البحث في معلومات النظام
      final productMatch = RegExp(r'Product:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (productMatch != null) {
        return productMatch.group(1)!.trim();
      }

      // البحث في معلومات الهاردوير
      final hwMatch = RegExp(r'Hardware:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (hwMatch != null) {
        return hwMatch.group(1)!.trim();
      }

      // البحث في machine من uname
      final unameMatch = RegExp(r'Linux\s+\S+\s+\S+\s+\S+\s+(\S+)', caseSensitive: false).firstMatch(output);
      if (unameMatch != null) {
        String machine = unameMatch.group(1)!.trim();
        if (machine.contains('mips') || machine.contains('arm') || machine.contains('ubnt')) {
          return machine;
        }
      }

      // البحث في board.info
      final boardMatch = RegExp(r'board\.name=(.+)', caseSensitive: false).firstMatch(output);
      if (boardMatch != null) {
        return boardMatch.group(1)!.trim();
      }

      // البحث في system.info
      final systemMatch = RegExp(r'board_name=(.+)', caseSensitive: false).firstMatch(output);
      if (systemMatch != null) {
        return systemMatch.group(1)!.trim();
      }

      // البحث في model من cpuinfo
      final cpuModelMatch = RegExp(r'model name\s*:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (cpuModelMatch != null) {
        return cpuModelMatch.group(1)!.trim();
      }      // البحث في processor
      final processorMatch = RegExp(r'processor\s*:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (processorMatch != null) {
        return processorMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }  String _parseUBNTDeviceName(String output) {
    try {
      // 1. Check configuration files first (most reliable source)
      final configPatterns = [
        RegExp(r'device\.name=(.+)', caseSensitive: false),
        RegExp(r'system\.name=(.+)', caseSensitive: false),
        RegExp(r'board\.hostname=(.+)', caseSensitive: false),
      ];

      for (final pattern in configPatterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          final name = match.group(1)!.trim();
          if (name.isNotEmpty && !_isDeviceModel(name)) {
            return name;
          }
        }
      }

      // 2. Check device status output
      final statusPatterns = [
        RegExp(r'Device Name:\s*(.+)', caseSensitive: false),
        RegExp(r'System Name:\s*(.+)', caseSensitive: false),
      ];

      for (final pattern in statusPatterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          final name = match.group(1)!.trim();
          if (name.isNotEmpty && !_isDeviceModel(name)) {
            return name;
          }
        }
      }

      // 3. Check hostname output
      final hostnamePatterns = [
        RegExp(r'hostname[=:]\s*(.+)', caseSensitive: false),
        RegExp(r'^([a-zA-Z0-9][a-zA-Z0-9-]{1,30})$', multiLine: true),
      ];

      for (final pattern in hostnamePatterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          final name = match.group(1)!.trim();
          // Clean up hostname
          final cleanName = name
              .replaceAll(RegExp(r'\s+(localhost|unknown|none)\s*', caseSensitive: false), '')
              .trim();
          
          if (cleanName.isNotEmpty && 
              cleanName.toLowerCase() != 'localhost' && 
              !_isDeviceModel(cleanName) &&
              cleanName.length > 2 &&
              cleanName.length < 30) {
            return cleanName;
          }
        }
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  // دالة مساعدة للتحقق من أن النص ليس موديل جهاز
  bool _isDeviceModel(String text) {
    final lowerText = text.toLowerCase();
    final modelKeywords = [
      'ubiquiti', 'rocket', 'nanostation', 'powerbeam', 'nanobeam', 
      'airmax', 'unifi', 'edgemax', 'airfiber', 'litebeam',
      'mips', 'arm', 'linux', 'ar71xx', 'ar934x', 'qca'
    ];
    
    for (final keyword in modelKeywords) {
      if (lowerText.contains(keyword)) {
        return true;
      }
    }
    
    return false;
  }

  String _parseUBNTNetworkMode(String output) {
    try {
      // البحث عن وضع الشبكة
      final modeMatch = RegExp(r'Network Mode:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (modeMatch != null) {
        return modeMatch.group(1)!.trim();
      }

      // البحث في معلومات الجسر
      final bridgeMatch = RegExp(r'Bridge Mode:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (bridgeMatch != null) {
        String mode = bridgeMatch.group(1)!.trim();
        return mode.toLowerCase() == 'enabled' ? 'Bridge' : 'Router';
      }

      return 'Router';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseUBNTWirelessMode(String output) {
    try {
      // البحث عن الوضع اللاسلكي
      final wirelessMatch = RegExp(r'Wireless Mode:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (wirelessMatch != null) {
        return wirelessMatch.group(1)!.trim();
      }

      // البحث في iwconfig
      final modeMatch = RegExp(r'Mode:(\w+)', caseSensitive: false).firstMatch(output);
      if (modeMatch != null) {
        String mode = modeMatch.group(1)!.trim();
        switch (mode.toLowerCase()) {
          case 'managed':
            return 'Station';
          case 'master':
            return 'Access Point';
          case 'adhoc':
            return 'Ad-Hoc';
          default:
            return mode;
        }
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }  String _parseUBNTSSID(String output) {
    try {
      // البحث عن SSID
      final ssidMatch = RegExp(r'''SSID:\s*["']?(.+?)["']?(?:\s|\$)''', caseSensitive: false).firstMatch(output);
      if (ssidMatch != null) {
        return ssidMatch.group(1)!.trim();
      }

      // البحث في iwconfig
      final iwSSIDMatch = RegExp(r'ESSID:"([^"]+)"', caseSensitive: false).firstMatch(output);
      if (iwSSIDMatch != null) {
        return iwSSIDMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }
  String _parseUBNTLanSpeed(String output) {
    try {
      // البحث عن سرعة LAN في ethtool
      final ethtoolSpeedMatch = RegExp(r'Speed:\s*(\d+)Mb/s', caseSensitive: false).firstMatch(output);
      final ethtoolDuplexMatch = RegExp(r'Duplex:\s*(Full|Half)', caseSensitive: false).firstMatch(output);
      
      if (ethtoolSpeedMatch != null) {
        String speed = ethtoolSpeedMatch.group(1)!;
        String duplex = ethtoolDuplexMatch?.group(1) ?? '';
        return duplex.isNotEmpty ? '${speed}Mbps-$duplex' : '${speed}Mbps';
      }

      // البحث عن سرعة LAN في ubnt-box
      final lanMatch = RegExp(r'LAN0:\s*(\d+)Mbps-(\w+)', caseSensitive: false).firstMatch(output);
      if (lanMatch != null) {
        return '${lanMatch.group(1)}Mbps-${lanMatch.group(2)}';
      }

      // البحث عن معلومات الإيثرنت العامة
      final ethMatch = RegExp(r'(\d+)Mbps\s+(Full|Half)', caseSensitive: false).firstMatch(output);
      if (ethMatch != null) {
        return '${ethMatch.group(1)}Mbps-${ethMatch.group(2)}';
      }

      // البحث في ifconfig عن Link detected
      final linkMatch = RegExp(r'Link detected:\s*(yes|no)', caseSensitive: false).firstMatch(output);
      if (linkMatch != null && linkMatch.group(1)!.toLowerCase() == 'yes') {
        // إذا كان هناك اتصال، ابحث عن معلومات أخرى
        final mtuMatch = RegExp(r'MTU:\s*(\d+)', caseSensitive: false).firstMatch(output);
        if (mtuMatch != null) {
          return 'Link Up - MTU ${mtuMatch.group(1)}';
        }
        return 'Link Up';
      }

      // البحث عن أي معدل نقل
      final speedGeneralMatch = RegExp(r'(\d+)\s*Mb?ps', caseSensitive: false).firstMatch(output);
      if (speedGeneralMatch != null) {
        return '${speedGeneralMatch.group(1)}Mbps';
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }
  String _parseUptime(String uptime) {
    try {
      // تنظيف النص وإزالة المعلومات غير الضرورية
      String cleanUptime = uptime.trim();
      
      // البحث عن أنماط مختلفة لمدة التشغيل
      
      // النمط: up 2 days, 3:45
      final dayHourMatch = RegExp(r'up\s+(\d+)\s+days?,\s*(\d+):(\d+)').firstMatch(cleanUptime);
      if (dayHourMatch != null) {
        final days = dayHourMatch.group(1);
        final hours = dayHourMatch.group(2);
        final minutes = dayHourMatch.group(3);
        return '$days أيام، $hours ساعات، $minutes دقائق';
      }
      
      // النمط: up 1 day, 2:30
      final dayMatch = RegExp(r'up\s+(\d+)\s+day,\s*(\d+):(\d+)').firstMatch(cleanUptime);
      if (dayMatch != null) {
        final days = dayMatch.group(1);
        final hours = dayMatch.group(2);
        final minutes = dayMatch.group(3);
        return '$days يوم، $hours ساعات، $minutes دقائق';
      }
      
      // النمط: up 5:30 (ساعات:دقائق فقط)
      final hourMinMatch = RegExp(r'up\s+(\d+):(\d+)').firstMatch(cleanUptime);
      if (hourMinMatch != null) {
        final hours = hourMinMatch.group(1);
        final minutes = hourMinMatch.group(2);
        return '$hours ساعات، $minutes دقائق';
      }
      
      // النمط: up 45 min (دقائق فقط)
      final minMatch = RegExp(r'up\s+(\d+)\s+min').firstMatch(cleanUptime);
      if (minMatch != null) {
        final minutes = minMatch.group(1);
        return '$minutes دقائق';
      }
      
      // النمط: up 2 hr (ساعات فقط)
      final hrMatch = RegExp(r'up\s+(\d+)\s+hr').firstMatch(cleanUptime);
      if (hrMatch != null) {
        final hours = hrMatch.group(1);
        return '$hours ساعات';
      }
      
      // البحث عن الوقت بالثواني
      final secMatch = RegExp(r'(\d+)\s*sec').firstMatch(cleanUptime);
      if (secMatch != null) {
        final seconds = int.parse(secMatch.group(1)!);
        if (seconds < 60) {
          return '$seconds ثانية';
        } else if (seconds < 3600) {
          final minutes = (seconds / 60).floor();
          final remainingSecs = seconds % 60;
          return '$minutes دقائق، $remainingSecs ثانية';
        } else {
          final hours = (seconds / 3600).floor();
          final remainingMins = ((seconds % 3600) / 60).floor();
          return '$hours ساعات، $remainingMins دقائق';
        }
      }
      
      // محاولة أخيرة لاستخراج الوقت من النص العام
      final generalMatch = RegExp(r'up\s+(.+?),').firstMatch(cleanUptime);
      if (generalMatch != null) {
        String uptimeStr = generalMatch.group(1)!.trim();
        
        // تحويل الكلمات الإنجليزية للعربية
        uptimeStr = uptimeStr
            .replaceAll('days', 'أيام')
            .replaceAll('day', 'يوم')
            .replaceAll('hrs', 'ساعات')
            .replaceAll('hr', 'ساعة')
            .replaceAll('mins', 'دقائق')
            .replaceAll('min', 'دقيقة')
            .replaceAll('secs', 'ثوان')
            .replaceAll('sec', 'ثانية');
        
        return uptimeStr;
      }
      
      // إذا لم تنجح كل المحاولات، أعد النص الأصلي منظف
      return cleanUptime.replaceAll(RegExp(r',\s*\d+\s*users?.*'), '').replaceAll('up ', '');
      
    } catch (e) {
      return uptime.trim();
    }
  }
  String _parseSignalStrength(String output) {
    try {
      // أنماط مختلفة للبحث عن قوة الإشارة
      final patterns = [
        RegExp(r'Signal level[=:]\s*(-?\d+)\s*dBm', caseSensitive: false),
        RegExp(r'Signal strength[=:]\s*(-?\d+)\s*dBm', caseSensitive: false),
        RegExp(r'signal[=:]\s*(-?\d+)', caseSensitive: false),
        RegExp(r'rssi[=:]\s*(-?\d+)', caseSensitive: false),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          return '${match.group(1)} dBm';
        }
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseNoise(String output) {
    try {
      // أنماط مختلفة للبحث عن مستوى الضوضاء
      final patterns = [
        RegExp(r'Noise level[=:]\s*(-?\d+)\s*dBm', caseSensitive: false),
        RegExp(r'Noise[=:]\s*(-?\d+)\s*dBm', caseSensitive: false),
        RegExp(r'noise[=:]\s*(-?\d+)', caseSensitive: false),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          return '${match.group(1)} dBm';
        }
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }
  String _parseSNR(String output) {
    try {
      // أنماط مختلفة للبحث عن SNR مباشرة
      final patterns = [
        RegExp(r'SNR[=:]\s*(-?\d+)\s*dB', caseSensitive: false),
        RegExp(r'Signal to Noise[=:]\s*(-?\d+)\s*dB', caseSensitive: false),
        RegExp(r'snr[=:]\s*(-?\d+)', caseSensitive: false),
        RegExp(r'S/N[=:]\s*(-?\d+)', caseSensitive: false),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          return '${match.group(1)} dB';
        }
      }

      // محاولة حساب SNR من Signal و Noise إذا لم تجد القيمة مباشرة
      final signalPatterns = [
        RegExp(r'Signal level[=:]\s*(-?\d+)\s*dBm', caseSensitive: false),
        RegExp(r'signal[=:]\s*(-?\d+)', caseSensitive: false),
        RegExp(r'RSSI[=:]\s*(-?\d+)', caseSensitive: false),
      ];

      final noisePatterns = [
        RegExp(r'Noise level[=:]\s*(-?\d+)\s*dBm', caseSensitive: false),
        RegExp(r'noise[=:]\s*(-?\d+)', caseSensitive: false),
      ];

      int? signalValue;
      int? noiseValue;

      // البحث عن قيمة الإشارة
      for (final pattern in signalPatterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          signalValue = int.tryParse(match.group(1)!);
          break;
        }
      }

      // البحث عن قيمة الضوضاء
      for (final pattern in noisePatterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          noiseValue = int.tryParse(match.group(1)!);
          break;
        }
      }

      // حساب SNR إذا كانت القيم متوفرة
      if (signalValue != null && noiseValue != null) {
        final snr = signalValue - noiseValue;
        return '$snr dB';
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseTxRate(String output) {
    try {
      // أنماط مختلفة للبحث عن معدل الإرسال
      final patterns = [
        RegExp(r'TX bit rate[=:]\s*(\d+(?:\.\d+)?)\s*Mb/s', caseSensitive: false),
        RegExp(r'Transmit Rate[=:]\s*(\d+(?:\.\d+)?)\s*Mb/s', caseSensitive: false),
        RegExp(r'tx-rate[=:]\s*(\d+(?:\.\d+)?)', caseSensitive: false),
        RegExp(r'txrate[=:]\s*(\d+(?:\.\d+)?)', caseSensitive: false),
        RegExp(r'tx[=:]\s*(\d+(?:\.\d+)?)\s*Mbps', caseSensitive: false),
        RegExp(r'Bit Rate[=:]\s*(\d+(?:\.\d+)?)\s*Mb/s', caseSensitive: false),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          return '${match.group(1)} Mbps';
        }
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseRxRate(String output) {
    try {
      // أنماط مختلفة للبحث عن معدل الاستقبال
      final patterns = [
        RegExp(r'RX bit rate[=:]\s*(\d+(?:\.\d+)?)\s*Mb/s', caseSensitive: false),
        RegExp(r'Receive Rate[=:]\s*(\d+(?:\.\d+)?)\s*Mb/s', caseSensitive: false),
        RegExp(r'rx-rate[=:]\s*(\d+(?:\.\d+)?)', caseSensitive: false),
        RegExp(r'rxrate[=:]\s*(\d+(?:\.\d+)?)', caseSensitive: false),
        RegExp(r'rx[=:]\s*(\d+(?:\.\d+)?)\s*Mbps', caseSensitive: false),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          return '${match.group(1)} Mbps';
        }
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }
  String _parseMAC(String output) {
    try {
      // أنماط مختلفة للبحث عن عنوان MAC
      final patterns = [
        RegExp(r'([0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2})'),
        RegExp(r'HWaddr\s+([0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2})'),
        RegExp(r'ether\s+([0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2})'),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(output);
        if (match != null) {
          return match.group(1)?.toUpperCase() ?? 'N/A';
        }
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  // دوال معالجة خاصة بـ MIMOSA
  String _parseMimosaDeviceModel(String output) {
    try {
      final modelMatch = RegExp(r'Model:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (modelMatch != null) {
        return modelMatch.group(1)!.trim();
      }

      final productMatch = RegExp(r'Product Name:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (productMatch != null) {
        return productMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseMimosaDeviceName(String output) {
    try {
      final nameMatch = RegExp(r'Device Name:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (nameMatch != null) {
        return nameMatch.group(1)!.trim();
      }

      final hostnameMatch = RegExp(r'Hostname:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (hostnameMatch != null) {
        return hostnameMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseMimosaNetworkMode(String output) {
    try {
      final modeMatch = RegExp(r'Operating Mode:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (modeMatch != null) {
        return modeMatch.group(1)!.trim();
      }

      final roleMatch = RegExp(r'Role:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (roleMatch != null) {
        return roleMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseMimosaWirelessMode(String output) {
    try {
      final wirelessMatch = RegExp(r'Wireless Mode:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (wirelessMatch != null) {
        return wirelessMatch.group(1)!.trim();
      }

      final protocolMatch = RegExp(r'Protocol:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (protocolMatch != null) {
        return protocolMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseMimosaSSID(String output) {
    try {
      final ssidMatch = RegExp(r'SSID:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (ssidMatch != null) {
        return ssidMatch.group(1)!.trim();
      }

      final networkMatch = RegExp(r'Network Name:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (networkMatch != null) {
        return networkMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseMimosaLanSpeed(String output) {
    try {
      final lanMatch = RegExp(r'Ethernet Speed:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (lanMatch != null) {
        return lanMatch.group(1)!.trim();
      }

      final speedMatch = RegExp(r'Port Speed:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (speedMatch != null) {
        return speedMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  // دوال معالجة خاصة بـ TPLINK
  String _parseTPLinkDeviceModel(String output) {
    try {
      final modelMatch = RegExp(r'Model:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (modelMatch != null) {
        return modelMatch.group(1)!.trim();
      }

      final productMatch = RegExp(r'Product:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (productMatch != null) {
        return productMatch.group(1)!.trim();
      }

      final deviceMatch = RegExp(r'Device Type:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (deviceMatch != null) {
        return deviceMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseTPLinkDeviceName(String output) {
    try {
      final nameMatch = RegExp(r'Device Name:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (nameMatch != null) {
        return nameMatch.group(1)!.trim();
      }

      final hostnameMatch = RegExp(r'System Name:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (hostnameMatch != null) {
        return hostnameMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseTPLinkNetworkMode(String output) {
    try {
      final modeMatch = RegExp(r'Operation Mode:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (modeMatch != null) {
        return modeMatch.group(1)!.trim();
      }

      final workMatch = RegExp(r'Working Mode:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (workMatch != null) {
        return workMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseTPLinkWirelessMode(String output) {
    try {
      final wirelessMatch = RegExp(r'Wireless Mode:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (wirelessMatch != null) {
        return wirelessMatch.group(1)!.trim();
      }

      final modeMatch = RegExp(r'Mode:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (modeMatch != null) {
        return modeMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseTPLinkSSID(String output) {
    try {
      final ssidMatch = RegExp(r'SSID:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (ssidMatch != null) {
        return ssidMatch.group(1)!.trim();
      }

      final networkMatch = RegExp(r'Network Name:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (networkMatch != null) {
        return networkMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String _parseTPLinkLanSpeed(String output) {
    try {
      final lanMatch = RegExp(r'LAN Port Speed:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (lanMatch != null) {
        return lanMatch.group(1)!.trim();
      }

      final ethMatch = RegExp(r'Ethernet Speed:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (ethMatch != null) {
        return ethMatch.group(1)!.trim();
      }

      final speedMatch = RegExp(r'Link Speed:\s*(.+)', caseSensitive: false).firstMatch(output);
      if (speedMatch != null) {
        return speedMatch.group(1)!.trim();
      }

      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }
}
