import 'package:flutter/material.dart';
import '../models/sync_operation_model.dart';
import '../models/sas_user_model.dart';
import '../models/subscriber_model.dart';
import '../models/package_model.dart';
import '../services/database_service.dart';
import '../services/sas_api_service.dart';
import 'package:uuid/uuid.dart';

class SyncProgressPage extends StatefulWidget {
  final String host;
  final String username;
  final String password;

  const SyncProgressPage({
    super.key,
    required this.host,
    required this.username,
    required this.password,
  });

  @override
  State<SyncProgressPage> createState() => _SyncProgressPageState();
}

class _SyncProgressPageState extends State<SyncProgressPage> {
  final List<SyncOperation> _operations = [];
  final ScrollController _scrollController = ScrollController();
  final _uuid = Uuid();
  bool _isSyncing = false;
  String _overallStatus = 'بدء المزامنة...';
  double _overallProgress = 0.0;
  int _totalSubscribers = 0;
  int _totalPackages = 0;
  int _processedSubscribers = 0;
  int _processedPackages = 0;

  @override
  void initState() {
    super.initState();
    _startSync();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _addOperation(SyncOperation operation) {
    setState(() {
      _operations.add(operation);
    });
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _startSync() async {
    setState(() {
      _isSyncing = true;
      _operations.clear();
      _overallProgress = 0.0;
      _totalSubscribers = 0;
      _totalPackages = 0;
      _processedSubscribers = 0;
      _processedPackages = 0;
    });

    try {
      // إضافة عملية بدء المزامنة
      _addOperation(SyncOperation.info(
        message: 'بدء عملية المزامنة مع الخادم',
        details: '${widget.host}',
      ));

      // اختبار الاتصال
      _addOperation(SyncOperation.connection(
        message: 'اختبار الاتصال بالخادم',
        status: SyncStatus.running,
      ));

      final api = SasApiService();
      
      // تسجيل الدخول
      _addOperation(SyncOperation.login(
        message: 'تسجيل الدخول إلى SAS',
        status: SyncStatus.running,
      ));

      final loginSuccess = await api.loginWithCredentials(
        host: widget.host,
        username: widget.username,
        password: widget.password,
      );

      if (!loginSuccess) {
        _addOperation(SyncOperation.login(
          message: 'فشل تسجيل الدخول',
          status: SyncStatus.failed,
          details: 'تأكد من صحة بيانات الاعتماد',
        ));
        _addOperation(SyncOperation.error(
          message: 'فشلت عملية المزامنة',
          details: 'لم يتم تسجيل الدخول إلى الخادم',
        ));
        setState(() {
          _isSyncing = false;
          _overallStatus = 'فشلت المزامنة';
        });
        return;
      }

      _addOperation(SyncOperation.login(
        message: 'تم تسجيل الدخول بنجاح',
        status: SyncStatus.completed,
      ));

      _addOperation(SyncOperation.connection(
        message: 'تم الاتصال بالخادم بنجاح',
        status: SyncStatus.completed,
      ));

      // جلب الباقات
      _addOperation(SyncOperation.fetchPackages(
        message: 'جلب الباقات من SAS',
        status: SyncStatus.running,
      ));

      final packages = await api.getProfiles();
      _totalPackages = packages.length;

      _addOperation(SyncOperation.fetchPackages(
        message: 'تم جلب $_totalPackages باقة',
        status: SyncStatus.completed,
        progress: packages.length,
        total: packages.length,
      ));

      // حفظ الباقات مع التحقق من التكرار
      final databaseService = DatabaseService();
      final currentLocalPackages = await databaseService.getPackagesFire();
      
      for (int i = 0; i < packages.length; i++) {
        final package = packages[i];
        _addOperation(SyncOperation.savePackage(
          packageName: package.name,
          status: SyncStatus.running,
        ));

        try {
          // البحث عن الباقة الموجودة
          final existingPackage = currentLocalPackages.firstWhere(
            (p) => p.sasProfileId == package.sasProfileId || p.name == package.name,
            orElse: () => PackageModel(
              adminId: databaseService.adminId,
              id: '',
              serverId: "",
              name: '',
              price: 0,
              durationInDays: 0,
              speed: '',
              deviceCount: 0,
              createdAt: DateTime.now(),
            ),
          );

          if (existingPackage.id.isNotEmpty) {
            // تحديث الباقة الموجودة
            _addOperation(SyncOperation.savePackage(
              packageName: 'تحديث: ${package.name}',
              status: SyncStatus.running,
            ));
            
            final updatedPackage = existingPackage.copyWith(
              adminId: databaseService.adminId,
              name: package.name,
              price: package.price,
              durationInDays: package.durationInDays,
              speed: package.speed,
              deviceCount: package.deviceCount,
              notes: package.notes,
              isActive: package.isActive,
              sasProfileId: package.sasProfileId,
            );
            
            await databaseService.updatePackage(updatedPackage);
            _addOperation(SyncOperation.savePackage(
              packageName: 'تم تحديث: ${package.name}',
              status: SyncStatus.completed,
            ));
          } else {
            // إضافة باقة جديدة
            _addOperation(SyncOperation.savePackage(
              packageName: 'إضافة: ${package.name}',
              status: SyncStatus.running,
            ));
            
            final newPackage = PackageModel(
              adminId: databaseService.adminId,
              id: databaseService.generateId(),
              serverId: package.id,
              name: package.name,
              price: package.price,
              durationInDays: package.durationInDays,
              speed: package.speed,
              deviceCount: package.deviceCount,
              notes: package.notes,
              createdAt: DateTime.now(),
              isActive: package.isActive,
              sasProfileId: package.sasProfileId,
            );
            
            await databaseService.addPackage(newPackage);
            _addOperation(SyncOperation.savePackage(
              packageName: 'تم إضافة: ${package.name}',
              status: SyncStatus.completed,
            ));
          }
          _processedPackages++;
        } catch (e) {
          _addOperation(SyncOperation.savePackage(
            packageName: package.name,
            status: SyncStatus.failed,
            details: e.toString(),
          ));
        }

        setState(() {
          _overallProgress = (_processedPackages + _processedSubscribers) / 
                           (_totalPackages + _totalSubscribers);
        });
      }

      // جلب المشتركين
      _addOperation(SyncOperation.fetchSubscribers(
        message: 'جلب المشتركين من SAS',
        status: SyncStatus.running,
      ));

      List<dynamic> allUsers = [];
      int page = 1;
      int perPage = 500;
      int maxPages = 50;
      bool shouldContinue = true;

      while (shouldContinue) {
        final usersJson = await api.getUsers(page: page, perPage: perPage);
        
        if (usersJson != null && usersJson['data'] is List) {
          final currentBatch = usersJson['data'] as List;
          
          if (currentBatch.isNotEmpty) {
            allUsers.addAll(currentBatch);
            _addOperation(SyncOperation.fetchSubscribers(
              message: 'تم جلب ${currentBatch.length} مشترك من الصفحة $page',
              status: SyncStatus.completed,
              progress: allUsers.length,
              total: null,
            ));
          }

          // شروط الإيقاف
          if (currentBatch.isEmpty && page > 1) {
            shouldContinue = false;
          } else if (usersJson.containsKey('total') && 
                     allUsers.length >= usersJson['total']) {
            shouldContinue = false;
          } else if (page >= maxPages) {
            shouldContinue = false;
          } else {
            page++;
          }
        } else {
          shouldContinue = false;
        }
      }

      _totalSubscribers = allUsers.length;
      _addOperation(SyncOperation.fetchSubscribers(
        message: 'تم جلب جميع المشتركين: $_totalSubscribers',
        status: SyncStatus.completed,
        progress: _totalSubscribers,
        total: _totalSubscribers,
      ));

      // حفظ المشتركين مع التحقق من التكرار
      final currentLocalSubscribers = await databaseService.getSubscribersFire();
      
      for (int i = 0; i < allUsers.length; i++) {
        final userData = allUsers[i];
        final userName = userData['full_name'] ?? userData['username'] ?? 'مشترك غير معروف';
        
        _addOperation(SyncOperation.saveSubscriber(
          subscriberName: userName,
          status: SyncStatus.running,
        ));

        try {
          // تحويل بيانات SAS إلى نموذج المشترك
          final sasUser = SasUser.fromJson(userData);
          
          // البحث عن الباقة المطابقة
          PackageModel? matchingPackage;
          try {
            matchingPackage = packages.firstWhere(
              (p) => p.sasProfileId == sasUser.profileId?.toString(),
            );
          } catch (e) {
            // إذا لم يتم العثور على باقة مطابقة، أنشئ باقة جديدة بنفس بيانات SAS
            if (sasUser.profileId != null && sasUser.packageName != null) {
              final newPackage = PackageModel(
                adminId: databaseService.adminId,
                id: databaseService.generateId(),
                serverId: '', // يمكن تعديلها إذا توفر معرف SAS
                name: sasUser.packageName!,
                price: 0, // يمكن جلب السعر من SAS إذا توفر
                durationInDays: 30, // أو من SAS
                speed: '', // أو من SAS
                deviceCount: 1,
                createdAt: DateTime.now(),
                isActive: true,
                sasProfileId: sasUser.profileId.toString(),
              );
              await databaseService.addPackage(newPackage);
              matchingPackage = newPackage;
              packages.add(newPackage); // حتى لا تكرر الإضافة
            } else if (packages.isNotEmpty) {
              matchingPackage = packages.first;
            }
          }

          if (matchingPackage != null) {
            // البحث عن المشترك الموجود
            SubscriberModel? existingSubscriber;
            String matchedBy = '';
            
            // البحث بواسطة SAS ID أولاً
            if (sasUser.id != null) {
              existingSubscriber = currentLocalSubscribers.firstWhere(
                (s) => s.sasServerId == sasUser.id,
                orElse: () => SubscriberModel(
                  adminId: databaseService.adminId,
                  id: '',
                  fullName: '',
                  phoneNumber: '',
                  packageId: '',
                  packageName: '',
                  address: '',
                  paymentStatus: PaymentStatus.pending,
                  subscriptionStart: DateTime.now(),
                  subscriptionEnd: DateTime.now(),
                  macAddress: '',
                  routerName: '',
                  technicalNotes: '',
                  debtAmount: 0.0,
                  createdAt: DateTime.now(),
                  isActive: false,
                  subscriptionType: SubscriptionType.broadband,
                  username: '',
                  password: '',
                  sasServerId: null,
                ),
              );
              if (existingSubscriber.id.isNotEmpty) {
                matchedBy = 'SAS ID: ${sasUser.id}';
              }
            }
            
            // إذا لم يتم العثور بواسطة SAS ID، ابحث بواسطة اسم المستخدم
            if (existingSubscriber?.id.isEmpty ?? true) {
              existingSubscriber = currentLocalSubscribers.firstWhere(
                (s) => s.username == sasUser.username,
                orElse: () => SubscriberModel(
                  adminId: databaseService.adminId,
                  id: '',
                  fullName: '',
                  phoneNumber: '',
                  packageId: '',
                  packageName: '',
                  address: '',
                  paymentStatus: PaymentStatus.pending,
                  subscriptionStart: DateTime.now(),
                  subscriptionEnd: DateTime.now(),
                  macAddress: '',
                  routerName: '',
                  technicalNotes: '',
                  debtAmount: 0.0,
                  createdAt: DateTime.now(),
                  isActive: false,
                  subscriptionType: SubscriptionType.broadband,
                  username: '',
                  password: '',
                  sasServerId: null,
                ),
              );
              if (existingSubscriber.id.isNotEmpty) {
                matchedBy = 'Username: ${sasUser.username}';
              }
            }

            if (existingSubscriber?.id.isNotEmpty ?? false) {
              // تحديث المشترك الموجود مع الحفاظ على البيانات المالية
              _addOperation(SyncOperation.saveSubscriber(
                subscriberName: 'تحديث: $userName',
                status: SyncStatus.running,
              ));
              
              final updatedSubscriber = existingSubscriber!.copyWith(
                adminId: databaseService.adminId,
                fullName: sasUser.fullName,
                phoneNumber: sasUser.phone ?? existingSubscriber.phoneNumber,
                username: sasUser.username,
                packageId: matchingPackage.id,
                packageName: matchingPackage.name,
                subscriptionEnd: sasUser.expiration,
                isActive: sasUser.isActive,
                sasServerId: widget.host,
                // الحفاظ على البيانات المالية الموجودة
                debtAmount: existingSubscriber.debtAmount,
                paymentStatus: existingSubscriber.paymentStatus,
                address: existingSubscriber.address,
                macAddress: existingSubscriber.macAddress,
                routerName: existingSubscriber.routerName,
                technicalNotes: existingSubscriber.technicalNotes,
                subscriptionStart: existingSubscriber.subscriptionStart,
                subscriptionType: existingSubscriber.subscriptionType,
              );
              
              await databaseService.updateSubscriber(updatedSubscriber, isSyncUpdate: true);
              
              _addOperation(SyncOperation.saveSubscriber(
                subscriberName: 'تم تحديث: $userName ($matchedBy)',
                status: SyncStatus.completed,
              ));
            } else {
              // إضافة مشترك جديد
              _addOperation(SyncOperation.saveSubscriber(
                subscriberName: 'إضافة: $userName',
                status: SyncStatus.running,
              ));
              
              final newSubscriber = SubscriberModel(
                adminId: databaseService.adminId,
                id: _uuid.v4(),
                fullName: sasUser.fullName,
                phoneNumber: sasUser.phone ?? '',
                packageId: matchingPackage.id,
                packageName: matchingPackage.name,
                address: 'غير محدد',
                paymentStatus: PaymentStatus.pending,
                subscriptionStart: DateTime.now(),
                subscriptionEnd: sasUser.expiration,
                macAddress: '',
                routerName: '',
                technicalNotes: 'تمت المزامنة من SAS Radius',
                debtAmount: 0.0,
                createdAt: DateTime.now(),
                isActive: sasUser.isActive,
                subscriptionType: SubscriptionType.broadband,
                username: sasUser.username,
                password: '',
                sasServerId: widget.host,
              );

              await databaseService.addSubscriber(newSubscriber, isSyncUpdate: true);
              
              _addOperation(SyncOperation.saveSubscriber(
                subscriberName: 'تم إضافة: $userName',
                status: SyncStatus.completed,
              ));
            }
            _processedSubscribers++;
          } else {
            _addOperation(SyncOperation.saveSubscriber(
              subscriberName: userName,
              status: SyncStatus.failed,
              details: 'لم يتم العثور على باقة مطابقة',
            ));
          }
        } catch (e) {
          _addOperation(SyncOperation.saveSubscriber(
            subscriberName: userName,
            status: SyncStatus.failed,
            details: e.toString(),
          ));
        }

        setState(() {
          _overallProgress = (_processedPackages + _processedSubscribers) / 
                           (_totalPackages + _totalSubscribers);
        });
      }

      // إكمال المزامنة
      _addOperation(SyncOperation.success(
        message: 'تمت المزامنة بنجاح',
        details: 'تم مزامنة $_processedPackages باقة و $_processedSubscribers مشترك',
      ));

      setState(() {
        _isSyncing = false;
        _overallStatus = 'تمت المزامنة بنجاح';
        _overallProgress = 1.0;
      });

    } catch (e) {
      _addOperation(SyncOperation.error(
        message: 'حدث خطأ أثناء المزامنة',
        details: e.toString(),
      ));
      
      setState(() {
        _isSyncing = false;
        _overallStatus = 'فشلت المزامنة';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مزامنة البيانات'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          if (_isSyncing)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // شريط التقدم العام
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _overallStatus,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${(_overallProgress * 100).toInt()}%',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: _overallProgress,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _overallProgress == 1.0 ? Colors.green : Colors.blue,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'الباقات: $_processedPackages/$_totalPackages',
                      style: const TextStyle(fontSize: 12),
                    ),
                    Text(
                      'المشتركين: $_processedSubscribers/$_totalSubscribers',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // قائمة العمليات
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(8.0),
              itemCount: _operations.length,
              itemBuilder: (context, index) {
                final operation = _operations[index];
                return _buildOperationCard(operation);
              },
            ),
          ),
          
          // أزرار التحكم
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                top: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isSyncing ? null : _startSync,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة المزامنة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    label: const Text('إغلاق'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOperationCard(SyncOperation operation) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  operation.icon,
                  color: operation.color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    operation.message,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: operation.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    operation.statusText,
                    style: TextStyle(
                      color: operation.color,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            
            if (operation.details != null) ...[
              const SizedBox(height: 4),
              Text(
                operation.details!,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
            
            if (operation.progress != null && operation.total != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: LinearProgressIndicator(
                      value: operation.progressValue,
                      backgroundColor: Colors.grey[200],
                      valueColor: AlwaysStoppedAnimation<Color>(operation.color),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    operation.progressText,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
            
            const SizedBox(height: 4),
            Text(
              _formatTimestamp(operation.timestamp),
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inSeconds < 60) {
      return 'منذ ${difference.inSeconds} ثانية';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'منذ ${difference.inHours} ساعة';
    }
  }
} 