// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        return linux;
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyChblmTwOy3ltMwq4cI5uu0WkUnHrutxAg',
    appId: '1:379353800436:web:abcdefghijklmnop',
    messagingSenderId: '379353800436',
    projectId: 'isp-mager',
    authDomain: 'isp-mager.firebaseapp.com',
    storageBucket: 'isp-mager.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyChblmTwOy3ltMwq4cI5uu0WkUnHrutxAg',
    appId: '1:379353800436:android:e2da0d70d2f3206fba48a1',
    messagingSenderId: '379353800436',
    projectId: 'isp-mager',
    storageBucket: 'isp-mager.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyChblmTwOy3ltMwq4cI5uu0WkUnHrutxAg',
    appId: '1:379353800436:ios:abcdefghijklmnop',
    messagingSenderId: '379353800436',
    projectId: 'isp-mager',
    storageBucket: 'isp-mager.firebasestorage.app',
    iosClientId: '379353800436-abcdefghijklmnop.apps.googleusercontent.com',
    iosBundleId: 'com.example.ispManager',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyChblmTwOy3ltMwq4cI5uu0WkUnHrutxAg',
    appId: '1:379353800436:ios:abcdefghijklmnop',
    messagingSenderId: '379353800436',
    projectId: 'isp-mager',
    storageBucket: 'isp-mager.firebasestorage.app',
    iosClientId: '379353800436-abcdefghijklmnop.apps.googleusercontent.com',
    iosBundleId: 'com.example.ispManager',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyChblmTwOy3ltMwq4cI5uu0WkUnHrutxAg',
    appId: '1:379353800436:web:abcdefghijklmnop',
    messagingSenderId: '379353800436',
    projectId: 'isp-mager',
    authDomain: 'isp-mager.firebaseapp.com',
    storageBucket: 'isp-mager.firebasestorage.app',
  );

  static const FirebaseOptions linux = FirebaseOptions(
    apiKey: 'AIzaSyChblmTwOy3ltMwq4cI5uu0WkUnHrutxAg',
    appId: '1:379353800436:web:abcdefghijklmnop',
    messagingSenderId: '379353800436',
    projectId: 'isp-mager',
    authDomain: 'isp-mager.firebaseapp.com',
    storageBucket: 'isp-mager.firebasestorage.app',
  );
} 