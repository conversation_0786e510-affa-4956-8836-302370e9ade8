import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../models/tower_model.dart';
import '../services/tower_service.dart';
import '../services/database_service.dart';
import '../services/map_service.dart';
import '../services/app_settings_service.dart';
import 'package:uuid/uuid.dart';

class CoverageManagementPage extends StatefulWidget {
  const CoverageManagementPage({Key? key}) : super(key: key);

  @override
  State<CoverageManagementPage> createState() => _CoverageManagementPageState();
}

class _CoverageManagementPageState extends State<CoverageManagementPage> {
  final TowerService _towerService = TowerService();
  final DatabaseService _databaseService = DatabaseService();
  
  List<TowerModel> _towers = [];
  bool _isLoading = true;
  String? _adminId;
  String _currencySymbol = 'د.ع'; // رمز العملة الافتراضي
  
  // متغيرات الخريطة
  final MapController _mapController = MapController();
  LatLng? _currentLocation;
  LatLng? _selectedLocation;
  double _selectedRadius = 0.5; // نصف كيلومتر افتراضياً
  int _currentMapProvider = 0; // 0: CartoDB, 1: Stamen, 2: OSM
  
  // متغيرات النموذج
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _serviceNameController = TextEditingController();
  final _agentNumberController = TextEditingController();
  final List<PackagePrice> _packages = [];

  @override
  void initState() {
    super.initState();
    _loadData();
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _mapController.dispose();
    _nameController.dispose();
    _serviceNameController.dispose();
    _agentNumberController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final user = await _databaseService.getCurrentUser();
      if (user != null) {
        _adminId = user.adminId;
        await _loadTowers();
      }
      
      // تحميل إعدادات العملة
      final currencySymbol = await AppSettingsService.getCurrencySymbol();
      setState(() {
        _currencySymbol = currencySymbol;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
      );
    }
    
    setState(() => _isLoading = false);
  }

  Future<void> _loadTowers() async {
    if (_adminId == null) return;
    
    final towers = await _towerService.getTowersByAdmin(_adminId!);
    setState(() {
      _towers = towers;
    });
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خدمة الموقع غير مفعلة')),
        );
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم رفض إذن الموقع')),
          );
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('إذن الموقع مرفوض نهائياً')),
        );
        return;
      }

      Position position = await Geolocator.getCurrentPosition();
      setState(() {
        _currentLocation = LatLng(position.latitude, position.longitude);
        _selectedLocation = _currentLocation;
      });
      
      _mapController.move(_currentLocation!, 13);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في الحصول على الموقع: $e')),
      );
    }
  }

  void _showAddTowerDialog() {
    _nameController.clear();
    _serviceNameController.clear();
    _agentNumberController.clear();
    _packages.clear();
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('إضافة برج جديد'),
          content: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم البرج',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال اسم البرج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _serviceNameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم الخدمة',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال اسم الخدمة';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _agentNumberController,
                    decoration: const InputDecoration(
                      labelText: 'رقم الوكيل',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال رقم الوكيل';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: Text('نطاق التغطية: ${_selectedRadius.toStringAsFixed(1)} كم'),
                      ),
                      Expanded(
                        child: Slider(
                          value: _selectedRadius,
                          min: 0.05,
                          max: 1.0,
                          divisions: 19,
                          onChanged: (value) {
                            setDialogState(() {
                              _selectedRadius = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      const Text('الباقات:'),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: () => _showAddPackageDialog(setDialogState),
                      ),
                    ],
                  ),
                  ..._packages.map((package) => Card(
                    child: ListTile(
                      title: Text(package.name),
                      subtitle: Text('${package.price} $_currencySymbol - ${package.description}'),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () {
                          setDialogState(() {
                            _packages.remove(package);
                          });
                        },
                      ),
                    ),
                  )).toList(),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => _saveTower(),
              child: const Text('حفظ'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddPackageDialog(StateSetter setDialogState) {
    final nameController = TextEditingController();
    final priceController = TextEditingController();
    final descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة باقة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم الباقة',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: priceController,
              decoration: const InputDecoration(
                labelText: 'السعر',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'الوصف',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.isNotEmpty && 
                  priceController.text.isNotEmpty) {
                setDialogState(() {
                  _packages.add(PackagePrice(
                    name: nameController.text,
                    price: double.tryParse(priceController.text) ?? 0,
                    description: descriptionController.text,
                  ));
                });
                Navigator.of(context).pop();
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveTower() async {
    if (!_formKey.currentState!.validate()) return;
    if (_adminId == null || _adminId!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تعذر تحديد مدير النظام. يرجى إعادة تسجيل الدخول أو إعادة تحميل الصفحة.')),
      );
      return;
    }
    if (_selectedLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى تحديد موقع البرج على الخريطة')),
      );
      return;
    }
    if (_packages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة باقة واحدة على الأقل')),
      );
      return;
    }

    try {
      final tower = TowerModel(
        id: const Uuid().v4(),
        adminId: _adminId!,
        name: _nameController.text,
        serviceName: _serviceNameController.text,
        agentNumber: _agentNumberController.text,
        latitude: _selectedLocation!.latitude,
        longitude: _selectedLocation!.longitude,
        coverageRadius: _selectedRadius,
        packages: _packages,
        createdAt: DateTime.now(),
      );

      await _towerService.addTower(tower);
      Navigator.of(context).pop();
      await _loadTowers();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم إضافة البرج بنجاح')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في حفظ البرج: $e')),
      );
    }
  }

  Future<void> _deleteTower(TowerModel tower) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل تريد حذف البرج "${tower.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await _towerService.deleteTower(tower.id);
        await _loadTowers();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف البرج بنجاح')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في حذف البرج: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة التغطية'),
        actions: [
          IconButton(
            icon: const Icon(Icons.map),
            onPressed: _showMapProviderDialog,
            tooltip: 'تغيير مزود الخريطة',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddTowerDialog,
            tooltip: 'إضافة برج جديد',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // الخريطة
                Expanded(
                  flex: 2,
                  child: FlutterMap(
                    mapController: _mapController,
                    options: MapOptions(
                      initialCenter: _currentLocation ?? const LatLng(24.7136, 46.6753), // الرياض افتراضياً
                      initialZoom: 13,
                      onTap: (_, location) {
                        setState(() {
                          _selectedLocation = location;
                        });
                      },
                    ),
                                          children: [
                        _getCurrentMapLayer(),
                      // رسم الأبراج الموجودة
                      ..._towers.map((tower) => CircleLayer(
                        circles: [
                          CircleMarker(
                            point: LatLng(tower.latitude, tower.longitude),
                            radius: tower.coverageRadius * 1000, // تحويل الكيلومترات إلى أمتار
                            color: Colors.blue.withOpacity(0.3),
                            borderColor: Colors.blue,
                            borderStrokeWidth: 2,
                          ),
                        ],
                      )).toList(),
                      // رسم علامات الأبراج
                      ..._towers.map((tower) => MarkerLayer(
                        markers: [
                          Marker(
                            point: LatLng(tower.latitude, tower.longitude),
                            width: 40,
                            height: 40,
                            child: GestureDetector(
                              onTap: () => _showTowerDetails(tower),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.blue,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: Colors.white, width: 2),
                                ),
                                child: const Icon(
                                  Icons.cell_tower,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )).toList(),
                      // رسم الموقع المحدد
                      if (_selectedLocation != null)
                        MarkerLayer(
                          markers: [
                            Marker(
                              point: _selectedLocation!,
                              width: 40,
                              height: 40,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: Colors.white, width: 2),
                                ),
                                child: const Icon(
                                  Icons.location_on,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
                // قائمة الأبراج
                Expanded(
                  flex: 1,
                  child: _towers.isEmpty
                      ? const Center(
                          child: Text('لا توجد أبراج مضافة بعد'),
                        )
                      : ListView.builder(
                          itemCount: _towers.length,
                          itemBuilder: (context, index) {
                            final tower = _towers[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 4,
                              ),
                              child: ListTile(
                                leading: const Icon(Icons.cell_tower, color: Colors.blue),
                                title: Text(tower.name),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('الخدمة: ${tower.serviceName}'),
                                    Text('الوكيل: ${tower.agentNumber}'),
                                    Text('نطاق التغطية: ${tower.coverageRadius.toStringAsFixed(1)} كم'),
                                    Text('عدد الباقات: ${tower.packages.length}'),
                                    Text('أقل سعر: ${tower.packages.isNotEmpty ? '${tower.packages.map((p) => p.price).reduce((a, b) => a < b ? a : b)} $_currencySymbol' : 'غير محدد'}'),
                                  ],
                                ),
                                trailing: PopupMenuButton<String>(
                                  onSelected: (value) {
                                    if (value == 'delete') {
                                      _deleteTower(tower);
                                    }
                                  },
                                  itemBuilder: (context) => [
                                    const PopupMenuItem(
                                      value: 'delete',
                                      child: Text('حذف'),
                                    ),
                                  ],
                                ),
                                onTap: () => _showTowerDetails(tower),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
    );
  }

  void _showTowerDetails(TowerModel tower) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.cell_tower, size: 32, color: Colors.blue),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            tower.name,
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          Text(
                            tower.serviceName,
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                _buildDetailRow('رقم الوكيل', tower.agentNumber),
                _buildDetailRow('نطاق التغطية', '${tower.coverageRadius.toStringAsFixed(1)} كم'),
                _buildDetailRow('الموقع', '${tower.latitude.toStringAsFixed(6)}, ${tower.longitude.toStringAsFixed(6)}'),
                _buildDetailRow('تاريخ الإنشاء', _formatDate(tower.createdAt)),
                const SizedBox(height: 16),
                Text(
                  'الباقات المتاحة',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                ...tower.packages.map((package) => Card(
                  child: ListTile(
                    title: Text(package.name),
                    subtitle: Text(package.description),
                    trailing: Text(
                      '${package.price} $_currencySymbol',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ),
                )).toList(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  void _showMapProviderDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر مزود الخريطة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<int>(
              title: const Text('CartoDB (مستحسن)'),
              subtitle: const Text('خرائط واضحة وسريعة'),
              value: 0,
              groupValue: _currentMapProvider,
              onChanged: (value) {
                setState(() {
                  _currentMapProvider = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<int>(
              title: const Text('Stamen'),
              subtitle: const Text('خرائط طبوغرافية'),
              value: 1,
              groupValue: _currentMapProvider,
              onChanged: (value) {
                setState(() {
                  _currentMapProvider = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<int>(
              title: const Text('OpenStreetMap'),
              subtitle: const Text('خرائط تفصيلية'),
              value: 2,
              groupValue: _currentMapProvider,
              onChanged: (value) {
                setState(() {
                  _currentMapProvider = value!;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  TileLayer _getCurrentMapLayer() {
    switch (_currentMapProvider) {
      case 0:
        return MapService.cartoDBLayer;
      case 1:
        return MapService.stamenLayer;
      case 2:
        return MapService.openStreetMapLayer;
      default:
        return MapService.cartoDBLayer;
    }
  }
} 