# نظام إدارة التغطية والخدمات

## نظرة عامة

تم إضافة نظام شامل لإدارة التغطية والخدمات في تطبيق إدارة مشتركي الإنترنت. يتيح هذا النظام للمديرين إضافة أبراج تغطية وتحديد نطاقاتها، وللمشتركين طلب خدمات جديدة من خلال تحديد مواقعهم.

## الميزات الرئيسية

### للمديرين:
1. **إدارة الأبراج**: إضافة وتعديل وحذف أبراج التغطية
2. **تحديد نطاق التغطية**: رسم دوائر تغطية من 50 متر إلى 1 كم
3. **إدارة الباقات**: إضافة باقات مختلفة مع أسعارها
4. **عرض الطلبات**: مراجعة وإدارة طلبات الخدمة من المشتركين
5. **تتبع الحالات**: متابعة حالة كل طلب (في الانتظار، موافقة، رفض، مكتمل)
6. **عرض المواقع**: خريطة تفاعلية تظهر موقع المشترك والبرج
7. **حساب المسافات**: عرض المسافة المباشرة بين المشترك والبرج
8. **التوجيه**: زر لفتح Google Maps مع التوجيه للموقع

### للمشتركين:
1. **تحديد الموقع**: تحديد موقع المشترك تلقائياً أو يدوياً
2. **البحث عن الأبراج**: العثور على أقرب برج متاح
3. **عرض الباقات**: رؤية الباقات المتاحة وأسعارها
4. **إرسال الطلب**: إرسال طلب خدمة مع البيانات المطلوبة

## كيفية الاستخدام

### إعداد الأبراج (للمدير):

1. **الوصول إلى إدارة التغطية**:
   - اذهب إلى الإعدادات
   - اختر "إدارة التغطية"

2. **إضافة برج جديد**:
   - اضغط على زر "+" في الشريط العلوي
   - أدخل اسم البرج واسم الخدمة ورقم الوكيل
   - حدد موقع البرج على الخريطة
   - اضبط نطاق التغطية (50م - 1كم)
   - أضف الباقات المتاحة مع أسعارها

3. **إدارة الطلبات**:
   - اذهب إلى "طلبات الخدمة" في الإعدادات
   - راجع الطلبات الجديدة
   - وافق أو ارفض الطلبات
   - حدد حالة الطلب (مكتمل، ملغي)

### طلب خدمة (للمشترك):

1. **الوصول إلى طلب الخدمة**:
   - اذهب إلى صفحة دخول المشترك
   - اضغط على "طلب خدمة جديدة"

2. **تحديد الموقع**:
   - سيتم تحديد موقعك تلقائياً
   - أو اضغط على الخريطة لتحديد موقع يدوياً

3. **اختيار الباقة**:
   - ستظهر لك الأبراج المتاحة في منطقتك
   - اختر البرج المناسب
   - اختر الباقة المطلوبة

4. **إرسال الطلب**:
   - أدخل اسمك ورقم هاتفك
   - اضغط على "إرسال الطلب"

## النماذج الجديدة

### TowerModel
```dart
class TowerModel {
  final String id;
  final String adminId;
  final String name;
  final String serviceName;
  final String agentNumber;
  final double latitude;
  final double longitude;
  final double coverageRadius;
  final List<PackagePrice> packages;
  final DateTime createdAt;
  final bool isActive;
}
```

### PackagePrice
```dart
class PackagePrice {
  final String name;
  final double price;
  final String description;
  
  // تنسيق السعر مع رمز العملة من إعدادات التطبيق
  String formatPrice(String currencySymbol) {
    return '$price $currencySymbol';
  }
}
```

### ServiceRequestModel
```dart
class ServiceRequestModel {
  final String id;
  final String subscriberName;
  final String subscriberPhone;
  final double subscriberLatitude;
  final double subscriberLongitude;
  final String towerId;
  final String towerName;
  final String selectedPackage;
  final double packagePrice;
  final RequestStatus status;
  final String? adminId;
  final String? notes;
  final DateTime createdAt;
  final DateTime? updatedAt;
}
```

## الخدمات الجديدة

### TowerService
- `addTower()`: إضافة برج جديد
- `getTowersByAdmin()`: جلب أبراج المدير
- `findNearestTower()`: البحث عن أقرب برج
- `addServiceRequest()`: إضافة طلب خدمة
- `updateServiceRequestStatus()`: تحديث حالة الطلب

### AppSettingsService
- `getCurrencySymbol()`: الحصول على رمز العملة
- `getCurrencyCode()`: الحصول على كود العملة
- `formatCurrency()`: تنسيق المبلغ مع رمز العملة
- `getSettings()`: الحصول على إعدادات التطبيق

## الصفحات الجديدة

1. **CoverageManagementPage**: إدارة الأبراج والتغطية
2. **ServiceRequestsPage**: إدارة طلبات الخدمة مع خريطة تفاعلية
3. **SubscriberServiceRequestPage**: صفحة طلب الخدمة للمشتركين

## المكتبات المطلوبة

تم إضافة المكتبات التالية:
- `flutter_map`: لعرض الخرائط
- `latlong2`: لإدارة إحداثيات الموقع
- `geolocator`: للحصول على الموقع الحالي
- `geocoding`: لتحويل الإحداثيات إلى عناوين

## تثبيت المكتبات

```bash
flutter pub get
```

## الأذونات المطلوبة

### Android (android/app/src/main/AndroidManifest.xml):
```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.INTERNET" />
```
✅ **تم إضافتها بالفعل في الملف**

### iOS (ios/Runner/Info.plist):
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>This app needs access to location to find nearby towers and provide location-based services.</string>
<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>This app needs access to location to find nearby towers and provide location-based services.</string>
<key>NSLocationAlwaysUsageDescription</key>
<string>This app needs access to location to find nearby towers and provide location-based services.</string>
```
✅ **تم إضافتها**

### macOS (macos/Runner/Info.plist):
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>This app needs access to location to find nearby towers and provide location-based services.</string>
<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>This app needs access to location to find nearby towers and provide location-based services.</string>
<key>NSLocationAlwaysUsageDescription</key>
<string>This app needs access to location to find nearby towers and provide location-based services.</string>
```
✅ **تم إضافتها**

### macOS Entitlements (macos/Runner/DebugProfile.entitlements & Release.entitlements):
```xml
<key>com.apple.security.network.client</key>
<true/>
<key>com.apple.security.personal-information.location</key>
<true/>
```
✅ **تم إضافتها**

## قاعدة البيانات

تم إنشاء مجموعتين جديدتين في Firestore:
- `towers`: لتخزين معلومات الأبراج
- `service_requests`: لتخزين طلبات الخدمة

## ملاحظات مهمة

1. **الأمان**: تأكد من إعداد قواعد Firestore المناسبة
2. **الأداء**: استخدم الفهرسة المناسبة للاستعلامات
3. **الخصوصية**: احترم خصوصية مواقع المستخدمين
4. **التحديثات**: راقب تحديثات المكتبات بانتظام
5. **العملة**: النظام يستخدم إعدادات العملة من التطبيق (افتراضياً: دينار عراقي - د.ع)
6. **الخرائط**: تم حل مشكلة خطأ 403 باستخدام مزود خرائط بديل (CartoDB)

## استكشاف الأخطاء

### مشاكل الموقع:
- تأكد من تفعيل خدمة الموقع
- تحقق من الأذونات
- اختبر على جهاز حقيقي

### مشاكل الخريطة:
- **خطأ 403 Forbidden**: تم حل هذه المشكلة باستخدام مزود خرائط بديل (CartoDB)
- يمكن تغيير مزود الخريطة من زر الخريطة في شريط الأدوات
- المزودين المتاحين:
  - **CartoDB**: خرائط واضحة وسريعة (مستحسن)
  - **Stamen**: خرائط طبوغرافية
  - **OpenStreetMap**: خرائط تفصيلية
- تأكد من اتصال الإنترنت
- تحقق من إعدادات flutter_map
- اختبر على منصات مختلفة

### مشاكل قاعدة البيانات:
- تحقق من قواعد Firestore
- تأكد من صحة البيانات
- راقب سجلات الأخطاء

## حل مشاكل الخرائط

### إذا واجهت خطأ 403 Forbidden:
1. اضغط على أيقونة الخريطة في شريط الأدوات
2. اختر مزود خرائط مختلف
3. جرب CartoDB أولاً (الأكثر استقراراً)

### إعدادات الخريطة الموصى بها:
```dart
// استخدام CartoDB (مستحسن)
TileLayer(
  urlTemplate: 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png',
  subdomains: const ['a', 'b', 'c', 'd'],
  userAgentPackageName: 'com.example.isp_manager',
)
```

## ميزات الخريطة التفاعلية

### في صفحة طلبات الخدمة:
1. **خريطة تفاعلية**: تعرض موقع المشترك والبرج
2. **دائرة التغطية**: تظهر نطاق تغطية البرج
3. **خط المسار**: خط أحمر يربط المشترك بالبرج
4. **حساب المسافة**: عرض المسافة المباشرة بالكيلومترات
5. **زر التوجيه**: يفتح Google Maps مع التوجيه للموقع

### العناصر على الخريطة:
- 🔴 **علامة حمراء**: موقع المشترك
- 🔵 **علامة زرقاء**: موقع البرج
- 🔵 **دائرة زرقاء**: نطاق تغطية البرج
- 🔴 **خط أحمر**: المسار المباشر بين المشترك والبرج 