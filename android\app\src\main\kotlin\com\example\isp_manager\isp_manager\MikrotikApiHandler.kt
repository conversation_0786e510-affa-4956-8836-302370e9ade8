package com.example.isp_manager.isp_manager

import me.legrange.mikrotik.ApiConnection
import java.util.concurrent.CompletableFuture
import javax.net.SocketFactory

class MikrotikApiHandler {

    private var connection: ApiConnection? = null

    fun connect(ip: String, username: String, password: String): CompletableFuture<Map<String, Any>> {
        return CompletableFuture.supplyAsync {
            try {
                // Close existing connection if any
                connection?.close()

                connection = ApiConnection.connect(SocketFactory.getDefault(), ip, 8728, 5000) // Default API port and timeout
                connection?.login(username, password)                // Fetch system resource details
                val resourceResults: List<Map<String, String>> = connection!!.execute("/system/resource/print")
                val resourceDetails = resourceResults.firstOrNull() ?: emptyMap()

                // Fetch identity
                val identityResults: List<Map<String, String>> = connection!!.execute("/system/identity/print")
                val identityDetails = identityResults.firstOrNull() ?: emptyMap()

                val combinedDetails = mutableMapOf<String, Any>()
                
                // Add resource details
                resourceDetails.forEach { (key, value) -> 
                    combinedDetails[key] = value 
                }
                
                // Add identity details
                identityDetails.forEach { (key, value) -> 
                    combinedDetails[key] = value 
                }

                // Add some common fields if available with proper null checking
                combinedDetails["identity"] = identityDetails["name"] ?: "N/A"
                combinedDetails["board-name"] = resourceDetails["board-name"] ?: "N/A"
                combinedDetails["version"] = resourceDetails["version"] ?: "N/A"
                combinedDetails["uptime"] = resourceDetails["uptime"] ?: "N/A"
                combinedDetails["cpu"] = resourceDetails["cpu"] ?: "N/A"
                combinedDetails["cpu-load"] = resourceDetails["cpu-load"] ?: "N/A"
                combinedDetails["free-memory"] = resourceDetails["free-memory"] ?: "N/A"
                combinedDetails["total-memory"] = resourceDetails["total-memory"] ?: "N/A"
                combinedDetails["free-hdd-space"] = resourceDetails["free-hdd-space"] ?: "N/A"
                combinedDetails["total-hdd-space"] = resourceDetails["total-hdd-space"] ?: "N/A"
                combinedDetails["architecture-name"] = resourceDetails["architecture-name"] ?: "N/A"

                connection?.close() // Close connection after fetching details

                combinedDetails.toMap() // Return as immutable map
            } catch (e: Exception) {
                connection?.close()
                throw e
            }
        }
    }

    fun sendCommand(ip: String, username: String, password: String, command: String): CompletableFuture<Map<String, Any>> {
        return CompletableFuture.supplyAsync {
            var tempConnection: ApiConnection? = null
            try {
                println("Attempting to connect to Mikrotik: $ip for command: $command")
                tempConnection = ApiConnection.connect(SocketFactory.getDefault(), ip, 8728, 5000)
                tempConnection?.login(username, password)
                println("Connection successful. Executing command: $command")

                val results: List<Map<String, String>> = tempConnection!!.execute(command)
                println("Command executed. Results: $results")
                val responseMap = mutableMapOf<String, Any>()
                responseMap["command"] = command
                responseMap["success"] = true
                responseMap["results"] = results

                tempConnection?.close()
                responseMap.toMap() // Return as immutable map
            } catch (e: Exception) {
                println("Error executing command on Mikrotik: $e")
                tempConnection?.close()
                throw e
            }
        }
    }

    fun reboot(ip: String, username: String, password: String): CompletableFuture<Map<String, Any>> {
        return CompletableFuture.supplyAsync {
            var tempConnection: ApiConnection? = null
            try {
                tempConnection = ApiConnection.connect(SocketFactory.getDefault(), ip, 8728, 5000)
                tempConnection?.login(username, password)

                val results: List<Map<String, String>> = tempConnection!!.execute("/system/reboot")
                val responseMap = mutableMapOf<String, Any>()
                responseMap["success"] = true
                responseMap["message"] = "Device reboot command sent."
                responseMap["results"] = results

                tempConnection?.close()
                responseMap.toMap()
            } catch (e: Exception) {
                tempConnection?.close()
                throw e
            }
        }
    }

    fun closeConnection() {
        connection?.close()
        connection = null
    }
}
