import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:isp_manager/services/database_service.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/network_device.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  factory DatabaseHelper() => _instance;

  DatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'isp_manager.db');
    return await openDatabase(path, version: 1, onCreate: _onCreate);
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE network_devices(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        ip_address TEXT NOT NULL,
        type TEXT NOT NULL,
        username TEXT NOT NULL,
        password TEXT NOT NULL,
        signal_strength TEXT,
        noise TEXT,
        snr TEXT,
        tx_rate TEXT,
        rx_rate TEXT,
        uptime TEXT,
        mac_address TEXT,
        firmware_version TEXT
      )
    ''');
  }

  Future<void> insertNetworkDevice(NetworkDevice device) async {
    // final db = await database;
    // await db.insert('network_devices', {
    //   'id': device.id,
    //   'name': device.name,
    //   'ip_address': device.ipAddress,
    //   'type': device.type,
    //   'username': device.username,
    //   'password': device.password,
    //   'signal_strength': device.signalStrength,
    //   'noise': device.noise,
    //   'snr': device.snr,
    //   'tx_rate': device.txRate,
    //   'rx_rate': device.rxRate,
    //   'uptime': device.uptime,
    //   'mac_address': device.macAddress,
    //   'firmware_version': device.firmwareVersion,
    // }, conflictAlgorithm: ConflictAlgorithm.replace);
    
    await _firestore
        .collection('network_devices')
        .doc(device.id)
        .set(device.toMap());
  }

  Future<List<NetworkDevice>> getNetworkDevices() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('network_devices');

    return List.generate(maps.length, (i) {
      return NetworkDevice(
        adminId: maps[i]['adminId'] ?? "",
        id: maps[i]['id'],
        name: maps[i]['name'],
        ipAddress: maps[i]['ip_address'],
        type: maps[i]['type'],
        username: maps[i]['username'],
        password: maps[i]['password'],
        signalStrength: maps[i]['signal_strength'],
        noise: maps[i]['noise'],
        snr: maps[i]['snr'],
        txRate: maps[i]['tx_rate'],
        rxRate: maps[i]['rx_rate'],
        uptime: maps[i]['uptime'],
        macAddress: maps[i]['mac_address'],
        firmwareVersion: maps[i]['firmware_version'],
      );
    });
  }

  Future<List<NetworkDevice>> getNetworkDevicesFire() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('network_devices')
          .where("adminId", isEqualTo: DatabaseService().adminId)
          .get();

      return snapshot.docs.map((doc) {
        return NetworkDevice.fromMap(doc.data());
      }).toList();
    } catch (e) {
      print('Error fetching packages from Firestore: $e');
      return [];
    }
  }

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  Future<void> syncDevicesToFirebase() async {
    final localDevices = await getNetworkDevices(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('network_devices')
        .where("adminId", isEqualTo: DatabaseService().adminId)
        .get();

    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final device in localDevices) {
      if (!firebaseIds.contains(device.id)) {
        await _firestore
            .collection('network_devices')
            .doc(device.id)
            .set(device.toMap());
      }
    }
  }

  Future<void> deleteNetworkDevice(String id) async {
    try {
      await FirebaseFirestore.instance
          .collection('network_devices')
          .doc(id)
          .delete();
    } catch (e) {
      print('Error deleting package: $e');
    }
  }

  Future<void> updateNetworkDevice(NetworkDevice device) async {
    try {
      await FirebaseFirestore.instance
          .collection('network_devices')
          .doc(device.id) // لازم الـ id يكون مضبوط
          .update(device.toMap());
    } catch (e) {
      print('Error updating package: $e');
    }
  }

  Future<NetworkDevice?> getNetworkDeviceByIpFire(String ip) async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('network_devices')
          .where("adminId", isEqualTo: DatabaseService().adminId)
          .where("ip_address", isEqualTo: ip)
          .limit(1)
          .get();
      if (snapshot.docs.isNotEmpty) {
        return NetworkDevice.fromMap(snapshot.docs.first.data());
      }
      return null;
    } catch (e) {
      print('Error fetching device by IP from Firestore: $e');
      return null;
    }
  }
}
