import 'package:flutter_test/flutter_test.dart';
import 'package:isp_manager/services/earthlink_service.dart';
import 'package:isp_manager/models/earthlink_user_model.dart';
import 'package:isp_manager/models/earthlink_account_model.dart';

void main() {
  group('EarthlinkService Sync', () {
    late EarthlinkService earthlinkService;

    setUp(() {
      earthlinkService = EarthlinkService();
    });

    tearDown(() {
      earthlinkService.close();
    });

    test('should initialize properly', () {
      expect(earthlinkService, isNotNull);
      expect(earthlinkService.isLoggedIn, isFalse);
    });

    test('EarthlinkAccount model should parse JSON correctly', () {
      final json = {
        'accountIndex': 1,
        'accountName': 'Test Package',
        'accountImagePath': 'path/to/image.png',
        'count': 10,
        'available': 5,
        'accountCost': 25.99,
        'needed': 0,
        'description': 'Test package description',
        'speed': '10Mbps',
        'durationDays': 30,
        'isActive': true,
        'accountType': 'Residential',
        'deviceLimit': 3,
      };

      final account = EarthlinkAccount.fromJson(json);
      
      expect(account.accountIndex, 1);
      expect(account.accountName, 'Test Package');
      expect(account.accountCost, 25.99);
      expect(account.isActive, true);
      expect(account.durationDays, 30);
    });

    test('EarthlinkUser model should parse JSON correctly', () {
      final json = {
        'userIndex': 12345,
        'userId': 'testuser@hus',
        'firstName': 'Test',
        'lastName': 'User',
        'displayName': 'Test User',
        'accountName': 'Test Package',
        'accountIndex': 1,
        'accountCost': 25.99,
        'subscriptionEnd': '2024-12-31T23:59:59',
        'isActive': true,
        'status': 'Active',
        'affiliateIndex': 9876,
        'affiliateName': 'Test Affiliate',
      };

      final user = EarthlinkUser.fromJson(json);
      
      expect(user.userIndex, 12345);
      expect(user.userID, 'testuser@hus');
      expect(user.firstName, 'Test');
      expect(user.lastName, 'User');
      expect(user.fullName, 'Test User');
      expect(user.accountName, 'Test Package');
      expect(user.accountCost, 25.99);
      expect(user.isActive, true);
      expect(user.status, 'Active');
    });

    test('EarthlinkUser model should handle various date formats', () {
      // Test ISO format
      final json1 = {
        'userIndex': 1,
        'subscriptionEnd': '2024-11-29T17:21:00',
      };
      
      final user1 = EarthlinkUser.fromJson(json1);
      expect(user1.subscriptionEnd, isNotNull);
      
      // Test DD/MM/YYYY format
      final json2 = {
        'userIndex': 2,
        'subscriptionEnd': '29/11/2024 05:21 PM',
      };
      
      final user2 = EarthlinkUser.fromJson(json2);
      expect(user2.subscriptionEnd, isNotNull);
      
      // Test YYYY-MM-DD format
      final json3 = {
        'userIndex': 3,
        'subscriptionEnd': '2024-11-29',
      };
      
      final user3 = EarthlinkUser.fromJson(json3);
      expect(user3.subscriptionEnd, isNotNull);
    });
  });
}