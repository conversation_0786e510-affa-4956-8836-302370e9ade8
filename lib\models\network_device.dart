import 'package:uuid/uuid.dart';

class NetworkDevice {
  String adminId;
  final String id;
  final String name;
  final String ipAddress;
  final String type;
  final String username;
  final String password;
  final String? ubntUsername;
  final String? ubntPassword;
  final String? signalStrength;
  final String? noise;
  final String? snr;
  final String? txRate;
  final String? rxRate;
  final String? uptime;
  final String? macAddress;
  final String? firmwareVersion;

  NetworkDevice({
    String? id,
    required this.name,
    required this.adminId,
    required this.ipAddress,
    required this.type,
    required this.username,
    required this.password,
    this.ubntUsername,
    this.ubntPassword,
    this.signalStrength,
    this.noise,
    this.snr,
    this.txRate,
    this.rxRate,
    this.uptime,
    this.macAddress,
    this.firmwareVersion,
  }) : id = id ?? const Uuid().v4();

  Map<String, dynamic> toMap() {
    return {
      "adminId":adminId,
      'id': id,
      'name': name,
      'ip_address': ipAddress,
      'type': type,
      'username': username,
      'password': password,
      'ubnt_username': ubntUsername,
      'ubnt_password': ubntPassword,
      'signal_strength': signalStrength,
      'noise': noise,
      'snr': snr,
      'tx_rate': txRate,
      'rx_rate': rxRate,
      'uptime': uptime,
      'mac_address': macAddress,
      'firmware_version': firmwareVersion,
    };
  }

  Map<String, dynamic> toJson() => toMap();

  factory NetworkDevice.fromMap(Map<String, dynamic> map) {
    return NetworkDevice(
      adminId: map["adminId"] ?? '',
      id: map['id'],
      name: map['name'],
      ipAddress: map['ip_address'],
      type: map['type'],
      username: map['username'],
      password: map['password'],
      ubntUsername: map['ubnt_username'],
      ubntPassword: map['ubnt_password'],
      signalStrength: map['signal_strength'],
      noise: map['noise'],
      snr: map['snr'],
      txRate: map['tx_rate'],
      rxRate: map['rx_rate'],
      uptime: map['uptime'],
      macAddress: map['mac_address'],
      firmwareVersion: map['firmware_version'],
    );
  }

  factory NetworkDevice.fromJson(Map<String, dynamic> json) =>
      NetworkDevice.fromMap(json);

  NetworkDevice copyWith({
    String? adminId,
    String? id,
    String? name,
    String? ipAddress,
    String? type,
    String? username,
    String? password,
    String? ubntUsername,
    String? ubntPassword,
    String? signalStrength,
    String? noise,
    String? snr,
    String? txRate,
    String? rxRate,
    String? uptime,
    String? macAddress,
    String? firmwareVersion,
  }) {
    return NetworkDevice(
      adminId: adminId ?? this.adminId,
      id: id ?? this.id,
      name: name ?? this.name,
      ipAddress: ipAddress ?? this.ipAddress,
      type: type ?? this.type,
      username: username ?? this.username,
      password: password ?? this.password,
      ubntUsername: ubntUsername ?? this.ubntUsername,
      ubntPassword: ubntPassword ?? this.ubntPassword,
      signalStrength: signalStrength ?? this.signalStrength,
      noise: noise ?? this.noise,
      snr: snr ?? this.snr,
      txRate: txRate ?? this.txRate,
      rxRate: rxRate ?? this.rxRate,
      uptime: uptime ?? this.uptime,
      macAddress: macAddress ?? this.macAddress,
      firmwareVersion: firmwareVersion ?? this.firmwareVersion,
    );
  }
}
