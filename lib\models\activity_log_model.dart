import '../services/app_settings_service.dart';

class ActivityLogModel {
  String adminId;
  final String id;
  final String subscriberId;
  final String userId;
  final String action;
  final String description;
  final double amount;
  final DateTime timestamp;

  ActivityLogModel({
    required this.id,
    required this.adminId,
    required this.subscriberId,
    required this.userId,
    required this.action,
    required this.description,
    required this.amount,
    required this.timestamp,
  });

  // Getter for actionType (alias for action)
  String get actionType => action;

  // Getter for actionDescription (alias for description)
  String get actionDescription => description;

  // Method to get details with currency
  Future<String?> getDetailsWithCurrency() async {
    if (amount > 0) {
      final currencySymbol = AppSettingsService.getCurrencySymbol();
      return '$amount $currencySymbol';
    }
    return null;
  }

  // Firebase compatibility methods
  Map<String, dynamic> toMap() => {
    'id': id,
    'subscriberId': subscriberId,
    'userId': userId,
    'action': action,
    'description': description,
    'amount': amount,
    'timestamp': timestamp.toIso8601String(),
    "adminId": adminId,
  };

  factory ActivityLogModel.fromMap(Map<String, dynamic> map) =>
      ActivityLogModel(
        id: map['id'],
        subscriberId: map['subscriberId'],
        userId: map['userId'],
        action: map['action'],
        description: map['description'],
        amount: map['amount']?.toDouble() ?? 0.0,
        timestamp: DateTime.parse(map['timestamp']),
        adminId: map['adminId']??"",
      );

  // Legacy compatibility methods
  Map<String, dynamic> toJson() => toMap();

  factory ActivityLogModel.fromJson(Map<String, dynamic> json) =>
      ActivityLogModel.fromMap(json);

  ActivityLogModel copyWith({
    String? id,
    String? subscriberId,
    String? userId,
    String? action,
    String? description,
    double? amount,
    DateTime? timestamp,
    required String adminId,
  }) => ActivityLogModel(
    id: id ?? this.id,
    subscriberId: subscriberId ?? this.subscriberId,
    userId: userId ?? this.userId,
    action: action ?? this.action,
    description: description ?? this.description,
    amount: amount ?? this.amount,
    timestamp: timestamp ?? this.timestamp,
    adminId: adminId,
  );
}
