import '../services/app_settings_service.dart';

class PackageModel {
  final String id;
  final String serverId;
  final String name;
  final double price;
  final int durationInDays;
  final String speed;
  final int deviceCount;
  final String notes;
  final DateTime createdAt;
  final bool isActive;
  final String? sasProfileId;
  final String? earthlinkAccountId;
  final String? mikrotikProfileName; // For MikroTik Profile Name
  final double? sellingPrice;
  final String? adminId;

  PackageModel({
    required this.id,
    required this.name,
    required this.price,
    required this.durationInDays,
    required this.speed,
    required this.deviceCount,
    required this.adminId,
    required this.serverId,
    this.notes = '',
    required this.createdAt,
    this.isActive = true,
    this.sasProfileId,
    this.earthlinkAccountId,
    this.mikrotikProfileName,
    this.sellingPrice,
  });

  // Firebase compatibility methods
  Map<String, dynamic> toMap() => {
        'id': id,
        'name': name,
        'price': price,
        'durationInDays': durationInDays,
        'speed': speed,
        'deviceCount': deviceCount,
        'notes': notes,
        'createdAt': createdAt.toIso8601String(),
        'isActive': isActive,
        'sasProfileId': sasProfileId,
        'earthlinkAccountId': earthlinkAccountId,
        'mikrotikProfileName': mikrotikProfileName,
        'sellingPrice': sellingPrice,
        "adminId": adminId,
        "serverId": serverId,
      };

  factory PackageModel.fromMap(Map<String, dynamic> map) => PackageModel(
        id: map['id'],
        name: map['name'],
        price: map['price']?.toDouble() ?? 0.0,
        durationInDays: map['durationInDays'] ?? 30,
        speed: map['speed'] ?? '',
        deviceCount: map['deviceCount'] ?? 1,
        notes: map['notes'] ?? '',
        createdAt: DateTime.parse(map['createdAt']),
        isActive: map['isActive'] == 1 ||
            map['isActive'] == true ||
            map['isActive'] == null,
        sasProfileId: map['sasProfileId']?.toString(),
        earthlinkAccountId: map['earthlinkAccountId']?.toString(),
        mikrotikProfileName: map['mikrotikProfileName']?.toString(),
        sellingPrice: map['sellingPrice']?.toDouble(),
        adminId: map['adminId'] ?? "",
        serverId: map['serverId'] ?? "",
      );

  // Legacy compatibility methods
  Map<String, dynamic> toJson() => toMap();

  factory PackageModel.fromJson(Map<String, dynamic> json) =>
      PackageModel.fromMap(json);

  PackageModel copyWith({
    String? id,
    String? name,
    double? price,
    int? durationInDays,
    String? speed,
    int? deviceCount,
    String? notes,
    DateTime? createdAt,
    bool? isActive,
    String? sasProfileId,
    String? earthlinkAccountId,
    String? mikrotikProfileName,
    double? sellingPrice,
    required String adminId,
  }) =>
      PackageModel(
        id: id ?? this.id,
        name: name ?? this.name,
        price: price ?? this.price,
        durationInDays: durationInDays ?? this.durationInDays,
        speed: speed ?? this.speed,
        deviceCount: deviceCount ?? this.deviceCount,
        notes: notes ?? this.notes,
        createdAt: createdAt ?? this.createdAt,
        isActive: isActive ?? this.isActive,
        sasProfileId: sasProfileId ?? this.sasProfileId,
        earthlinkAccountId: earthlinkAccountId ?? this.earthlinkAccountId,
        mikrotikProfileName: mikrotikProfileName ?? this.mikrotikProfileName,
        sellingPrice: sellingPrice ?? this.sellingPrice,
        adminId: adminId,
        serverId: serverId,
      );

  String get durationDisplayText {
    if (durationInDays == 30) return 'شهر واحد';
    if (durationInDays == 90) return 'ثلاثة أشهر';
    if (durationInDays == 365) return 'سنة واحدة';
    if (durationInDays == 7) return 'أسبوع واحد';
    return '$durationInDays يوم';
  }

  String get priceDisplayText => price.toStringAsFixed(
        0,
      ); // This should not be used anymore - use getFormattedPrice() instead

  // New async method for getting formatted price with current currency
  Future<String> getFormattedPrice() async {
    return await AppSettingsService.formatCurrency(price);
  }
}
